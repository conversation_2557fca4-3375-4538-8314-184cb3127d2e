# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.dev"
    source: hosted
    version: "61.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: f5628cd9c92ed11083f425fd1f8f1bc60ecdda458c81d73b143aeda036c35fe7
      url: "https://pub.dev"
    source: hosted
    version: "1.3.16"
  action_slider:
    dependency: "direct main"
    description:
      name: action_slider
      sha256: fad0720cde9bf06c12594c15da17dba087556a3285875a91aee3d3a64a3072e2
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  aescryptojs:
    dependency: "direct main"
    description:
      name: aescryptojs
      sha256: "2727de8a3006cda4fdb7b0ebd6155b777fc85169b34dbc632c07ed0fcfc84eeb"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  amdjs:
    dependency: transitive
    description:
      name: amdjs
      sha256: "97e6d96fd0e64d3eeb6fc85f386160322a23b1192662aac1fa4139569add2434"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  android_sms_retriever:
    dependency: "direct main"
    description:
      name: android_sms_retriever
      sha256: "86f71e617b0cb4a11bc808b841a2983293c2f7ed21cfa1ec3a451e018429d1e8"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  animated_custom_dropdown:
    dependency: "direct main"
    description:
      name: animated_custom_dropdown
      sha256: "5d651fd00c770c32ef7c98978d19b9d8c0a9ebc6e305974af05c1e1a03692289"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  animated_loading_border:
    dependency: "direct main"
    description:
      name: animated_loading_border
      sha256: e0d3f53a006a49d2fb936c21ab0e609e9c0e8a4948d6c2ad4472179f2e35d107
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1"
  animated_text_kit:
    dependency: transitive
    description:
      name: animated_text_kit
      sha256: "37392a5376c9a1a503b02463c38bc0342ef814ddbb8f9977bc90f2a84b22fa92"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  app_usage:
    dependency: "direct main"
    description:
      name: app_usage
      sha256: "6e387c9dbf1a7232e43d1a65edfd077c0fffd4460e2cec86555c2428787cf511"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  appcheck:
    dependency: "direct main"
    description:
      name: appcheck
      sha256: a9e5852e8c9d14342cfa3569fc464a1d49a49eac41e3d006f5825ce54663678f
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  archive:
    dependency: "direct main"
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: "21afe4333076c02877d14f4a89df111e658a6d466cbfc802eb705eb91bd5adfd"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  async_extension:
    dependency: transitive
    description:
      name: async_extension
      sha256: f5589e5e0611648f610b7ad00c40fbee4cb7398061ea73463bbeb8ec29fc8a28
      url: "https://pub.dev"
    source: hosted
    version: "1.2.5"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      sha256: "6fdf255ed3af86535c96452c33ecff1245990bb25a605bfb1958661ccc3d467f"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.18"
  audioplayers:
    dependency: transitive
    description:
      name: audioplayers
      sha256: bb506873ab4fb663db9b47243754ef669adf684dbe6ba8b57c26e27b834065c4
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: "53969a1c5d94ebdaef72e334f1c0ea2f3946ab2baa8d398a9584ac27baf4f037"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: dcd5a4ceef5aa3d04e8ae49441f13a3d9f93fe6e9e88fe121ff6b6f391b2a40b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: ea1cb9a5c9389b38f293ee1375b22b02fb1d1f7a2e1517fcb674297dd074dc7b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "589c3106d0c656540e81ac2c7a78a9414a4a6534ae7b77f06ddb5d6aa5dc653c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: "13fb044a443276223774f8ed1f8d2b82f443cf8980edd0e172e55967c1556a49"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "87964ddece7275b97277935df67af60536155914c81c633f708914b99433129b"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  auto_route:
    dependency: "direct main"
    description:
      name: auto_route
      sha256: "82f8df1d177416bc6b7a449127d0270ff1f0f633a91f2ceb7a85d4f07c3affa1"
      url: "https://pub.dev"
    source: hosted
    version: "7.8.4"
  auto_route_generator:
    dependency: "direct dev"
    description:
      name: auto_route_generator
      sha256: "11067a3bcd643812518fe26c0c9ec073990286cabfd9d74b6da9ef9b913c4d22"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.2"
  auto_start_flutter:
    dependency: "direct main"
    description:
      name: auto_start_flutter
      sha256: "27b12be7dc2d37b1a535189f461a12dad15b54c12f0a36e6ea69e73e579d433b"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  awesome_circular_chart:
    dependency: "direct main"
    description:
      name: awesome_circular_chart
      sha256: efbf8b84b871fc5fc10680cd812dc5983507f7bad0910c4ced4a7816fe59a25e
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  awesome_notifications:
    dependency: "direct main"
    description:
      name: awesome_notifications
      sha256: "65f730f9c0e73a346039ef746384bcff1773f9f03821b859705a7ab8db977b23"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.2"
  badges:
    dependency: "direct main"
    description:
      name: badges
      sha256: a7b6bbd60dce418df0db3058b53f9d083c22cdb5132a052145dc267494df0b84
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "3820f15f502372d979121de1f6b97bfcf1630ebff8fe1d52fb2b0bfa49be5b49"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.2"
  bloc_concurrency:
    dependency: "direct main"
    description:
      name: bloc_concurrency
      sha256: "44535c9f429cd7e91d548cf89fde1c23a8b4b3637decdb1865bb583091a00d4e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "80184af8b6cb3e5c1c4ec6d8544d27711700bc3e6d2efad04238c7b5290889f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "0343061a33da9c5810b2d6cee51945127d8f4c060b7fbdd9d54917f0a3feaaa1"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "64e12b0521812d1684b1917bc80945625391cb9bdd4312536b1d69dcb6133ed8"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "10c6bcdbf9d049a0b666702cf1cee4ddfdc38f02a19d35ae392863b47519848b"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.6"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: c9e32d21dd6626b5c163d48b037ce906bbe428bc23ab77bcd77bb21e593b6185
      url: "https://pub.dev"
    source: hosted
    version: "7.2.11"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "723b4021e903217dfc445ec4cf5b42e27975aece1fc4ebbc1ca6329c2d9fb54e"
      url: "https://pub.dev"
    source: hosted
    version: "8.7.0"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: f98972704692ba679db144261172a8e20feb145636c617af0eb4022132a6797f
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "56aa42a7a01e3c9db8456d9f3f999931f1e05535b5a424271e9a38cabf066613"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "759b9a9f8f6ccbb66c185df805fac107f05730b1dab9c64626d1008cca532257"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  camera:
    dependency: "direct main"
    description:
      name: camera
      sha256: "9499cbc2e51d8eb0beadc158b288380037618ce4e30c9acbc4fae1ac3ecb5797"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.5+9"
  camera_android:
    dependency: "direct main"
    description:
      name: camera_android
      sha256: "58463140f1b39591b8e2155861b436abad4ceb48160058be8374164ff0309ef3"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.8+13"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: "3b6d9f550cfd658c71f34a99509528501e5e5d4fa79f11e3a4d6ef380d8e0254"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.13+7"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: "86fd4fc597c6e455265ddb5884feb352d0171ad14b9cdf3aba30da59b25738c4"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: d4c2c571c7af04f8b10702ca16bb9ed2a26e64534171e8f75c9349b2c004d8f1
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2+3"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "9c695cc963bf1d04a47bd6021f68befce8970bcd61d24938e1fb0918cf5d9c42"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  change_app_package_name:
    dependency: "direct dev"
    description:
      name: change_app_package_name
      sha256: f9ebaf68a4b5a68c581492579bb68273c523ef325fbf9ce2f1b57fb136ad023b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  chopper:
    dependency: transitive
    description:
      name: chopper
      sha256: "813cabd029ad8c020874429a671f2c15f45cfc3ced66b566bfa181a9b61b89b8"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  circular_countdown_timer:
    dependency: "direct main"
    description:
      name: circular_countdown_timer
      sha256: "9ba5fbc076cedbcbf6190ed86762e679f43d7c67cdd903ea34df059dabdc08d4"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: b2151ce26a06171005b379ecff6e08d34c470180ffe16b8e14b6d52be292b55f
      url: "https://pub.dev"
    source: hosted
    version: "4.8.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: a1ace0a119f20aabc852d165077c036cd864315bd99b7eaa10a60100341941bf
      url: "https://pub.dev"
    source: hosted
    version: "1.19.0"
  color:
    dependency: transitive
    description:
      name: color
      sha256: ddcdf1b3badd7008233f5acffaf20ca9f5dc2cd0172b75f68f24526a5f5725cb
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: "3f8fe4e504c2d33696dac671a54909743bc6a902a9bb0902306f7a2aed7e528e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.9"
  connectivity_plus_linux:
    dependency: transitive
    description:
      name: connectivity_plus_linux
      sha256: "3caf859d001f10407b8e48134c761483e4495ae38094ffcca97193f6c271f5e2"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  connectivity_plus_macos:
    dependency: transitive
    description:
      name: connectivity_plus_macos
      sha256: "488d2de1e47e1224ad486e501b20b088686ba1f4ee9c4420ecbc3b9824f0b920"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.6"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.dev"
    source: hosted
    version: "1.2.4"
  connectivity_plus_web:
    dependency: transitive
    description:
      name: connectivity_plus_web
      sha256: "81332be1b4baf8898fed17bb4fdef27abb7c6fd990bf98c54fd978478adf2f1a"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.5"
  connectivity_plus_windows:
    dependency: transitive
    description:
      name: connectivity_plus_windows
      sha256: "535b0404b4d5605c4dd8453d67e5d6d2ea0dd36e3b477f50f31af51b0aeab9dd"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  context_holder:
    dependency: "direct main"
    description:
      name: context_holder
      sha256: "29561fdf80133077538bd9db59102550b4de520aa78adf8ec0bfb714b9dc98cd"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "2f9d2cbccb76127ba28528cb3ae2c2326a122446a83de5a056aaa3880d3882c5"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+7"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  csv:
    dependency: transitive
    description:
      name: csv
      sha256: "63ed2871dd6471193dffc52c0e6c76fb86269c00244d244297abbb355c84a86e"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: d57953e10f9f8327ce64a508a355f0b1ec902193f66288e8cb5070e7c47eeb2d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  custom_pin_screen:
    dependency: "direct main"
    description:
      name: custom_pin_screen
      sha256: f4312585a7363eb1b37700c4d2fc5765e30a94307906618a9a51a099537ae411
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "1efa911ca7086affd35f463ca2fc1799584fb6aa89883cf0af8e3664d6a02d55"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  dartx:
    dependency: transitive
    description:
      name: dartx
      sha256: "8b25435617027257d43e6508b5fe061012880ddfdaa75a71d607c3de2a13d244"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  dartz:
    dependency: "direct main"
    description:
      name: dartz
      sha256: e6acf34ad2e31b1eb00948692468c30ab48ac8250e0f0df661e29f12dd252168
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  device_info:
    dependency: transitive
    description:
      name: device_info
      sha256: f4a8156cb7b7480d969cb734907d18b333c8f0bc0b1ad0b342cdcecf30d62c48
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  device_info_platform_interface:
    dependency: transitive
    description:
      name: device_info_platform_interface
      sha256: b148e0bf9640145d09a4f8dea96614076f889e7f7f8b5ecab1c7e5c2dbc73c1b
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: "0042cb3b2a76413ea5f8a2b40cec2a33e01d0c937e91f0f7c211fde4f7739ba6"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.1"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: d3b01d5868b50ae571cd1dc6e502fc94d956b665756180f7b16ead09e836fd64
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "49af28382aefc53562459104f64d16b9dfd1e8ef68c862d5af436cc8356ce5a8"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.1"
  dom_tools:
    dependency: transitive
    description:
      name: dom_tools
      sha256: "55f1a0d30e2bb51fed0d0354514546d3f967bf0781a1f8562646b82a4da14c81"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.17"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  dropdown_search:
    dependency: "direct main"
    description:
      name: dropdown_search
      sha256: "55106e8290acaa97ed15bea1fdad82c3cf0c248dd410e651f5a8ac6870f783ab"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.6"
  easy_localization:
    dependency: "direct main"
    description:
      name: easy_localization
      sha256: de63e3b422adfc97f256cbb3f8cf12739b6a4993d390f3cadb3f51837afaefe5
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  easy_localization_loader:
    dependency: "direct main"
    description:
      name: easy_localization_loader
      sha256: "89e70b2516123d639e8e68b8dd08fc07c8b83b0cfee17c642165e7e8807ebc86"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1+1"
  easy_logger:
    dependency: transitive
    description:
      name: easy_logger
      sha256: c764a6e024846f33405a2342caf91c62e357c24b02c04dbc712ef232bf30ffb7
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "62d9aa4670cc2a8798bab89b39fc71b6dfbacf615de6cf5001fb39f7e4a996a2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  enum_to_string:
    dependency: transitive
    description:
      name: enum_to_string
      sha256: bd9e83a33b754cb43a75b36a9af2a0b92a757bfd9847d2621ca0b1bed45f8e7a
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  epubx:
    dependency: "direct main"
    description:
      name: epubx
      sha256: "0ab9354efa177c4be52c46f857bc15bf83f83a92667fb673465c8f89fca26db3"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  equatable:
    dependency: "direct main"
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  external_path:
    dependency: "direct main"
    description:
      name: external_path
      sha256: "2095c626fbbefe70d5a4afc9b1137172a68ee2c276e51c3c1283394485bea8f4"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  facesdk_plugin:
    dependency: "direct dev"
    description:
      path: facesdk_plugin
      relative: true
    source: path
    version: "0.0.1"
  fading_edge_scrollview:
    dependency: "direct main"
    description:
      name: fading_edge_scrollview
      sha256: "1f84fe3ea8e251d00d5735e27502a6a250e4aa3d3b330d3fdcb475af741464ef"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  fancy_indicator:
    dependency: "direct main"
    description:
      name: fancy_indicator
      sha256: "9a4616015f70637ac887e229838914e1d057b31f282ef5b7874e9cfd414f7a12"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.2"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  ffmpeg_kit_flutter_min_gpl:
    dependency: transitive
    description:
      name: ffmpeg_kit_flutter_min_gpl
      sha256: ca3dc2330c176c8ef96fb512e7a1a37ab369e15685d2e72a52ec1d50726360a0
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  ffmpeg_kit_flutter_platform_interface:
    dependency: transitive
    description:
      name: ffmpeg_kit_flutter_platform_interface
      sha256: addf046ae44e190ad0101b2fde2ad909a3cd08a2a109f6106d2f7048b7abedee
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "5fc22d7c25582e38ad9a8515372cd9a93834027aacf1801cf01164dac0ffa08c"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  file_saver:
    dependency: "direct main"
    description:
      name: file_saver
      sha256: "566c0029cccf79848cb5c261c7fb161873d4109dbc145bfeea1f7e4097b62dc7"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.6"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "045d372bf19b02aeb69cacf8b4009555fb5f6f0b7ad8016e5f46dd1387ddd492"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2+1"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: b15c3da8bd4908b9918111fa486903f5808e388b8d1c559949f584725a6594d6
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+3"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: "0aa47a725c346825a2bd396343ce63ac00bda6eff2fbc43eabe99737dede8262"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: d3547240c20cabf205c7c7f01a50ecdbc413755814d6677f3cb366f04abcead0
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "96607c0e829a581c2a483c658f04e8b159964c3bae2730f73297070bc85d40bb"
      url: "https://pub.dev"
    source: hosted
    version: "2.24.2"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: c437ae5d17e6b5cc7981cf6fd458a5db4d12979905f9aafd1fea930428a9fe63
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: d585bdf3c656c3f7821ba1bd44da5f13365d22fcecaf5eb75c4295246aaa83c0
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      sha256: "27f78b1fdad2a7f557abea17c3e0ba882bd0430ddffb7844634d41e51422e43e"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.5"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "48b6cfb3e2fe3955ce1dfe16a0cceacb7d293277fda77eb47c058bfff94268e0"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.13"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "980259425fa5e2afc03e533f33723335731d21a56fd255611083bceebf4373a8"
      url: "https://pub.dev"
    source: hosted
    version: "14.7.10"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: "54e283a0e41d81d854636ad0dad73066adc53407a60a7c3189c9656e2f1b6107"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.18"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "90dc7ed885e90a24bb0e56d661d4d2b5f84429697fd2cbb9e5890a0ca370e6f4"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.18"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_alice:
    dependency: "direct main"
    description:
      name: flutter_alice
      sha256: "4d701d0db1cf3fb9a53e5d411ed5fef60a3563ea8b2d2c231614910644a3ca21"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_animate:
    dependency: "direct main"
    description:
      name: flutter_animate
      sha256: "3cb5eb80827abb48e362a9f29f7ba4e1b0edba6b2e2f35b1d350b6ef069bb547"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  flutter_awesome_select:
    dependency: "direct main"
    description:
      name: flutter_awesome_select
      sha256: f7beb2704bd3128e4bcd6931e741971cdebf5e13ef01e8e090349f89699e1d12
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  flutter_background_service:
    dependency: "direct main"
    description:
      name: flutter_background_service
      sha256: "94d9a143852729140e17254a53769383b03738cd92b6e588a8762003e6cd9dd9"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.5"
  flutter_background_service_android:
    dependency: "direct main"
    description:
      name: flutter_background_service_android
      sha256: "30863ebafd8214b8e76d5e5c9f27887dc5cc303fcf3e89f71534f621fc486782"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
  flutter_background_service_ios:
    dependency: "direct main"
    description:
      name: flutter_background_service_ios
      sha256: ab73657535876e16abc89e40f924df3e92ad3dee83f64d187081417e824709ed
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_background_service_platform_interface:
    dependency: transitive
    description:
      name: flutter_background_service_platform_interface
      sha256: cd5720ff5b051d551a4734fae16683aace779bd0425e8d3f15d84a0cdcc2d8d9
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      sha256: e74efb89ee6945bcbce74a5b3a5a3376b088e5f21f55c263fc38cbdc6237faae
      url: "https://pub.dev"
    source: hosted
    version: "8.1.3"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_gen_core:
    dependency: transitive
    description:
      name: flutter_gen_core
      sha256: "8b4ff1d45d125e576e26ea99d15e0419bb3c45b53696e022880866b78bb6b830"
      url: "https://pub.dev"
    source: hosted
    version: "5.3.2"
  flutter_gen_runner:
    dependency: "direct dev"
    description:
      name: flutter_gen_runner
      sha256: fd197f8c657e79313d53d3934de602ebe604ba722a84c88ae3a43cd90428c67a
      url: "https://pub.dev"
    source: hosted
    version: "5.3.2"
  flutter_gradients_reborn:
    dependency: "direct main"
    description:
      name: flutter_gradients_reborn
      sha256: b1e85754c31d88f9dd7dd98eca8da9b9d2c75bfff197ac4e918f6ef4e1d22bb5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+7"
  flutter_html:
    dependency: "direct main"
    description:
      name: flutter_html
      sha256: "02ad69e813ecfc0728a455e4bf892b9379983e050722b1dce00192ee2e41d1ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_image_compress:
    dependency: "direct main"
    description:
      name: flutter_image_compress
      sha256: "4edadb0ca2f957b85190e9c3aa728569b91b64b6e06e0eec5b622d47a8692ab2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  flutter_image_compress_common:
    dependency: transitive
    description:
      name: flutter_image_compress_common
      sha256: "7f79bc6c8a363063620b4e372fa86bc691e1cb28e58048cd38e030692fbd99ee"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  flutter_image_compress_macos:
    dependency: transitive
    description:
      name: flutter_image_compress_macos
      sha256: "26df6385512e92b3789dc76b613b54b55c457a7f1532e59078b04bf189782d47"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  flutter_image_compress_ohos:
    dependency: transitive
    description:
      name: flutter_image_compress_ohos
      sha256: "70360371698be994786e5dd2e364a6525b1c5a4f843bff8af9b8a2fbe808d8d8"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  flutter_image_compress_platform_interface:
    dependency: transitive
    description:
      name: flutter_image_compress_platform_interface
      sha256: "579cb3947fd4309103afe6442a01ca01e1e6f93dc53bb4cbd090e8ce34a41889"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  flutter_image_compress_web:
    dependency: transitive
    description:
      name: flutter_image_compress_web
      sha256: f02fe352b17f82b72f481de45add240db062a2585850bea1667e82cc4cd6c311
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4+1"
  flutter_inappwebview:
    dependency: "direct overridden"
    description:
      name: flutter_inappwebview
      sha256: "100ed8b5c59fe02076d5c3a69ddfe433c7172bd57e2da847b3e88bfeba2bfeb8"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0-beta.9"
  flutter_inappwebview_internal_annotations:
    dependency: transitive
    description:
      name: flutter_inappwebview_internal_annotations
      sha256: "5f80fd30e208ddded7dbbcd0d569e7995f9f63d45ea3f548d8dd4c0b473fb4c8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      sha256: "559c600f056e7c704bd843723c21e01b5fba47e8824bd02422165bcc02a5de1d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: e2a421b7e59244faef694ba7b30562e489c2b489866e505074eb005cd7060db7
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: "674173fd3c9eda9d4c8528da2ce0ea69f161577495a9cc835a2a4ecd7eadeb35"
      url: "https://pub.dev"
    source: hosted
    version: "17.2.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_mask_view:
    dependency: "direct main"
    description:
      name: flutter_mask_view
      sha256: d9cc42ebe048a433213f4438a5f26e4d84a349a5f419ebd6c1e0bfeb0bd222ac
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  flutter_native_image:
    dependency: "direct main"
    description:
      name: flutter_native_image
      sha256: "0ff23d6222064259df8f85ea56925627ea1ec8658814672c5b6c23fc9174c65e"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.6+1"
  flutter_native_splash:
    dependency: "direct dev"
    description:
      name: flutter_native_splash
      sha256: "6777a3abb974021a39b5fdd2d46a03ca390e03903b6351f21d10e7ecc969f12d"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.16"
  flutter_network_connectivity:
    dependency: "direct main"
    description:
      name: flutter_network_connectivity
      sha256: "3dddee42e06f8479b021cc17b60ea891cf71b124c03896d4fedec366671cb623"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.6"
  flutter_pdfview:
    dependency: "direct main"
    description:
      name: flutter_pdfview
      sha256: a9055bf920c7095bf08c2781db431ba23577aa5da5a056a7152dc89a18fbec6f
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  flutter_phoenix:
    dependency: "direct main"
    description:
      name: flutter_phoenix
      sha256: "39589dac934ea476d0e43fb60c1ddfba58f14960743640c8250dea11c4333378"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: b068ffc46f82a55844acfa4fdbb61fad72fa2aef0905548419d97f0f95c456da
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "8cf100b8e4973dc570b6415a2090b0bfaa8756ad333db46939efc3e774ee100d"
      url: "https://pub.dev"
    source: hosted
    version: "5.9.0"
  flutter_session_manager:
    dependency: "direct main"
    description:
      name: flutter_session_manager
      sha256: "5483f1c245ee7a3bc294d93b4d73d6a068d5c0df292ccd4b48bf8b4460f0cd19"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  flutter_slidable:
    dependency: "direct main"
    description:
      name: flutter_slidable
      sha256: "19ed4813003a6ff4e9c6bcce37e792a2a358919d7603b2b31ff200229191e44c"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_staggered_grid_view:
    dependency: transitive
    description:
      name: flutter_staggered_grid_view
      sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: d39e7f95621fc84376bc0f7d504f05c3a41488c562f4a8ad410569127507402c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_timezone:
    dependency: "direct main"
    description:
      name: flutter_timezone
      sha256: "06b35132c98fa188db3c4b654b7e1af7ccd01dfe12a004d58be423357605fb24"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  flutter_vibrate:
    dependency: "direct main"
    description:
      name: flutter_vibrate
      sha256: "9cc9b32cf52c90dd34c1cf396ed40010b2c74e69adbb0ff16005afa900971ad8"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: "81b68579e23fcbcada2db3d50302813d2371664afe6165bc78148050ab94bf66"
      url: "https://pub.dev"
    source: hosted
    version: "8.2.5"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  gallery_saver:
    dependency: "direct main"
    description:
      name: gallery_saver
      sha256: df8b7e207ca12d64c71e0710a7ee3bc48aa7206d51cc720716fedb1543a66712
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      sha256: e946395fc608842bb2f6c914807e9183f86f3cb787f6b8f832753e5251036f02
      url: "https://pub.dev"
    source: hosted
    version: "10.1.0"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "93906636752ea4d4e778afa981fdfe7409f545b3147046300df194330044d349"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.1"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: ab90ae811c42ec2f6021e01eca71df00dee6ff1e69d2c2dafd4daeb0b793f73d
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "6c8d494d6948757c56720b778af742f6973f31fca1f702a7539b8917e4a2468a"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "59083f7e0871b78299918d92bf930a14377f711d2d1156c558cd5ebae6c20d58"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: a92fae29779d5c6dc60e8411302f5221ade464968fe80a36d330e80a71f087af
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.dev"
    source: hosted
    version: "4.6.6"
  get_it:
    dependency: "direct main"
    description:
      name: get_it
      sha256: f79870884de16d689cf9a7d15eedf31ed61d750e813c538a6efb92660fea83c3
      url: "https://pub.dev"
    source: hosted
    version: "7.6.4"
  get_storage:
    dependency: "direct main"
    description:
      name: get_storage
      sha256: "39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  google_fonts:
    dependency: "direct main"
    description:
      name: google_fonts
      sha256: "6b6f10f0ce3c42f6552d1c70d2c28d764cf22bb487f50f66cca31dcd5194f4d6"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4"
  google_maps:
    dependency: transitive
    description:
      name: google_maps
      sha256: "555d5d736339b0478e821167ac521c810d7b51c3b2734e6802a9f046b64ea37a"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      sha256: d4914cb38b3dcb62c39c085d968d434de0f8050f00f4d9f5ba4a7c7e004934cb
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: "4279a338b79288fad5c8b03e5ae6ec30888bff210e0bab10b1f31f31e5a90558"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: "6ad65362aeeeda44b7c2c807e36bf578ef4b1c163882e085bdb040bf2934b246"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: a3e9e6896501e566d902c6c69f010834d410ef4b7b5c18b90c77e871c86b7907
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  google_maps_flutter_web:
    dependency: transitive
    description:
      name: google_maps_flutter_web
      sha256: f893d1542c6562bc8299ef768fbbe92ade83c220ab3209b9477ec9f81ad585e4
      url: "https://pub.dev"
    source: hosted
    version: "0.5.4+2"
  google_nav_bar:
    dependency: "direct main"
    description:
      name: google_nav_bar
      sha256: "1c8e3882fa66ee7b74c24320668276ca23affbd58f0b14a24c1e5590f4d07ab0"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.6"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  group_radio_button:
    dependency: "direct main"
    description:
      name: group_radio_button
      sha256: "204de8d16b224be7fc72dade0c3afd410ff5a34417d89f74f0fd8be7a8c2b4d6"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  grouped_list:
    dependency: "direct main"
    description:
      name: grouped_list
      sha256: fef106470186081c32636aa055492eee7fc7fe8bf0921a48d31ded24821af19f
      url: "https://pub.dev"
    source: hosted
    version: "5.1.2"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.6"
  http_interceptor:
    dependency: "direct main"
    description:
      name: http_interceptor
      sha256: "5f3dde028e67789339c250252c09510a74aff21ce16b06d07d9096bda6582bab"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: "direct main"
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "8e9d133755c3e84c73288363e6343157c383a0c6c56fc51afcc5d4d7180306d6"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  image_editor:
    dependency: "direct main"
    description:
      name: image_editor
      sha256: "0fe70befea0dbaf24a7cacc32c28311a65118f66637997ad072e9063f59efdd8"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  image_editor_common:
    dependency: transitive
    description:
      name: image_editor_common
      sha256: d141c0847148a7da573a5be5ca02e70d381e61cb6484ebef52a230ca1d6c56ab
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_editor_ohos:
    dependency: transitive
    description:
      name: image_editor_ohos
      sha256: "06756859586d5acefec6e3b4f356f9b1ce05ef09213bcb9a0ce1680ecea2d054"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.9"
  image_editor_platform_interface:
    dependency: transitive
    description:
      name: image_editor_platform_interface
      sha256: "474517efc770464f7d99942472d8cfb369a3c378e95466ec17f74d2b80bd40de"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  image_gallery_saver:
    dependency: "direct main"
    description:
      name: image_gallery_saver
      sha256: "0aba74216a4d9b0561510cb968015d56b701ba1bd94aace26aacdd8ae5761816"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: b6951e25b795d053a6ba03af5f710069c99349de9341af95155d52665cb4607c
      url: "https://pub.dev"
    source: hosted
    version: "0.8.9"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: d6a6e78821086b0b737009b09363018309bbc6de3fd88cc5c26bc2bb44a4957f
      url: "https://pub.dev"
    source: hosted
    version: "0.8.8+2"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "869fe8a64771b7afbc99fc433a5f7be2fea4d1cb3d7c11a48b6b579eb9c797f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "76ec722aeea419d03aa915c2c96bf5b47214b053899088c9abb4086ceecf97a7"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.8+4"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: ed9b00e63977c93b0d2d2b343685bed9c324534ba5abafbb3dfbd6a780b1b514
      url: "https://pub.dev"
    source: hosted
    version: "2.9.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  images_picker:
    dependency: "direct main"
    description:
      name: images_picker
      sha256: cc99347a6fa93228bf92f15ce36e4474256e4af0e6c0e1f0e7e9f047adbccd5b
      url: "https://pub.dev"
    source: hosted
    version: "1.2.11"
  in_app_update:
    dependency: "direct main"
    description:
      name: in_app_update
      sha256: b6ccb757281a96a4b18536f68fe2567aeca865134218719364212da8fe94615c
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  infinite_scroll_pagination:
    dependency: "direct main"
    description:
      name: infinite_scroll_pagination
      sha256: b68bce20752fcf36c7739e60de4175494f74e99e9a69b4dd2fe3a1dd07a7f16a
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  internet_connection_checker:
    dependency: "direct main"
    description:
      name: internet_connection_checker
      sha256: "1c683e63e89c9ac66a40748b1b20889fd9804980da732bf2b58d6d5456c8e876"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  isar:
    dependency: "direct main"
    description:
      name: isar
      sha256: "99165dadb2cf2329d3140198363a7e7bff9bbd441871898a87e26914d25cf1ea"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  isar_flutter_libs:
    dependency: "direct main"
    description:
      name: isar_flutter_libs
      sha256: bc6768cc4b9c61aabff77152e7f33b4b17d2fc93134f7af1c3dd51500fe8d5e8
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  isar_generator:
    dependency: "direct dev"
    description:
      name: isar_generator
      sha256: "76c121e1295a30423604f2f819bc255bc79f852f3bc8743a24017df6068ad133"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  js_wrapping:
    dependency: transitive
    description:
      name: js_wrapping
      sha256: e385980f7c76a8c1c9a560dfb623b890975841542471eade630b2871d243851c
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.dev"
    source: hosted
    version: "4.8.1"
  json_object_mapper:
    dependency: transitive
    description:
      name: json_object_mapper
      sha256: "8566947565045eb2a8dd679029c4fcbb84dfd0e10309a1693e67e83e174893a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  just_audio:
    dependency: transitive
    description:
      name: just_audio
      sha256: b607cd1a43bac03d85c3aaee00448ff4a589ef2a77104e3d409889ff079bf823
      url: "https://pub.dev"
    source: hosted
    version: "0.9.36"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      sha256: c3dee0014248c97c91fe6299edb73dc4d6c6930a2f4f713579cd692d9e47f4a1
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      sha256: "134356b0fe3d898293102b33b5fd618831ffdc72bb7a1b726140abdf22772b70"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.9"
  jwt_decoder:
    dependency: "direct main"
    description:
      name: jwt_decoder
      sha256: "54774aebf83f2923b99e6416b4ea915d47af3bde56884eb622de85feabbc559f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  kplayer:
    dependency: "direct main"
    description:
      name: kplayer
      sha256: "7da2e1d3e1b73a4c43a8163767db40878d56c2c881bc8063c6e63a32435d90dc"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.4"
  kplayer_platform_interface:
    dependency: transitive
    description:
      name: kplayer_platform_interface
      sha256: "4ee1ea5d27551c2af65b23e7293e6cbd315fb300955ceca8e966dbf0afbfe77b"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  kplayer_with_audioplayers:
    dependency: transitive
    description:
      name: kplayer_with_audioplayers
      sha256: e5f7ef003225ecdd7cfb2261a702174dbe33768474755acb07595f8b95d9c5cd
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  kplayer_with_just_audio:
    dependency: transitive
    description:
      name: kplayer_with_just_audio
      sha256: cb7bc8cb008abf60ef2c274bf54e7254fcdfd8977c55b9b3aeba115e8fc0293e
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "7bb2830ebd849694d1ec25bf1f44582d6ac531a57a365a803a6034ff751d2d06"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.7"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "9491a714cca3667b60b5c420da8217e6de0d1ba7a5ec322fab01758f6998f379"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.8"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: cbf8d4b858bb0134ef3ef87841abdf8d63bfc255c266b7bf6b39daa1085c4290
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  list_counter:
    dependency: transitive
    description:
      name: list_counter
      sha256: c447ae3dfcd1c55f0152867090e67e219d42fe6d4f2807db4bbe8b8d69912237
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  lite_rolling_switch:
    dependency: "direct main"
    description:
      name: lite_rolling_switch
      sha256: f5685911d249534ac6ac9862622f18846d31799c90fa4600b475d902d132b090
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      sha256: "7e6c63082e399b61e4af71266b012e767a5d4525dd6e9ba41e174fd42d76e115"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.7"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: df4ccb3193525b8a60c78a5ca7bf188a47705bcf77bcc837a6b2cf6da64ae0e2
      url: "https://pub.dev"
    source: hosted
    version: "1.0.35"
  local_auth_ios:
    dependency: transitive
    description:
      name: local_auth_ios
      sha256: "8293faf72ef0ac4710f209edd03916c2d4c1eeab0483bdcf9b2e659c2f7d737b"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: fc5bd537970a324260fda506cfb61b33ad7426f37a8ea5c461cf612161ebba54
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: "505ba3367ca781efb1c50d3132e44a2446bccc4163427bc203b9b4d8994d97ea"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  logger:
    dependency: "direct main"
    description:
      name: logger
      sha256: "6bbb9d6f7056729537a4309bda2e74e18e5d9f14302489cc1e93f33b3fe32cac"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2+1"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: a93542cc2d60a7057255405f62252533f8e8956e7e06754955669fd32fb4b216
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: c2b81e184067b41d0264d514f7cdaa2c02d38511e39d6521a1ccc238f6d7b3f2
      url: "https://pub.dev"
    source: hosted
    version: "6.0.1"
  mask_text_input_formatter:
    dependency: "direct main"
    description:
      name: mask_text_input_formatter
      sha256: fc7a1d59262bbaa97b1e1bf009c2c045163e70f229f6c146bfb17a84cfd8635d
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: bdb68674043280c3428e9ec998512fb681678676b3c54e773629ffe74419f8c7
      url: "https://pub.dev"
    source: hosted
    version: "1.15.0"
  mime:
    dependency: "direct main"
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  mno_zoom_widget:
    dependency: "direct main"
    description:
      name: mno_zoom_widget
      sha256: "2bfb0702a0b7a28d34975f03900a4fa56938545beb6caa7fa8599810a6f94f6f"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  native_device_orientation:
    dependency: "direct main"
    description:
      name: native_device_orientation
      sha256: "0c330c068575e4be72cce5968ca479a3f8d5d1e5dfce7d89d5c13a1e943b338c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  ntp:
    dependency: "direct main"
    description:
      name: ntp
      sha256: "198db73e5059b334b50dbe8c626011c26576778ee9fc53f4c55c1d89d08ed2d2"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "45b40f99622f11901238e18d48f5f12ea36426d8eced9f4cbf58479c7aa2430d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  open_filex:
    dependency: "direct main"
    description:
      name: open_filex
      sha256: a6c95237767c5647e68b71a476602fcf4f1bfc530c126265e53addae22ef5fc2
      url: "https://pub.dev"
    source: hosted
    version: "4.3.4"
  otp_autofill:
    dependency: "direct main"
    description:
      name: otp_autofill
      sha256: "4757f7c58b0a5acaf89083015efc119df09671a5e937161961e8c4c5a51dc83f"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  overlay_support:
    dependency: "direct main"
    description:
      name: overlay_support
      sha256: fc39389bfd94e6985e1e13b2a88a125fc4027608485d2d4e2847afe1b2bb339c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: "7e76fad405b3e4016cd39d08f455a4eb5199723cf594cd1b8916d47140d93017"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  package_usage_stats:
    dependency: "direct dev"
    description:
      name: package_usage_stats
      sha256: f3133438bc616628a3cb4882cad9edefdc966deb1dc6c5555ab56642dc37a930
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  page_transition:
    dependency: "direct main"
    description:
      name: page_transition
      sha256: dee976b1f23de9bbef5cd512fe567e9f6278caee11f5eaca9a2115c19dc49ef6
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: a1aa8aaa2542a6bc57e381f132af822420216c80d4781f7aa085ca3229208aaa
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "19314d595120f82aca0ba62787d58dde2cc6b5df7d2f0daf72489e38d1b57f2d"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  pausable_timer:
    dependency: "direct main"
    description:
      name: pausable_timer
      sha256: "6ef1a95441ec3439de6fb63f39a011b67e693198e7dae14e20675c3c00e86074"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+3"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "284a66179cabdf942f838543e10413246f06424d960c92ba95c84439154fcac8"
      url: "https://pub.dev"
    source: hosted
    version: "11.0.1"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: f9fddd3b46109bd69ff3f9efa5006d2d309b7aec0f3c1c5637a60a2d5659e76e
      url: "https://pub.dev"
    source: hosted
    version: "11.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.dev"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: cb3798bef7fc021ac45b308f4b51208a152792445cce0448c9a4ba5879dd8750
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  pin_code_fields:
    dependency: "direct main"
    description:
      name: pin_code_fields
      sha256: "4c0db7fbc889e622e7c71ea54b9ee624bb70c7365b532abea0271b17ea75b729"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  pinput:
    dependency: "direct main"
    description:
      name: pinput
      sha256: a92b55ecf9c25d1b9e100af45905385d5bc34fc9b6b04177a9e82cb88fe4d805
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "0a279f0707af40c890e80b1e9df8bb761694c074ba7e1d4ab1bc4b728e200b59"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  platform_device_id_linux:
    dependency: transitive
    description:
      name: platform_device_id_linux
      sha256: "994b1608593e527a629af2d5aeb241c60d308d3434bc78b0f6fcb3c1a02dff43"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  platform_device_id_macos:
    dependency: transitive
    description:
      name: platform_device_id_macos
      sha256: "968db2a504c611294b12a031b3734432d6df10553a0d3ae3b33ed21abfdbaba0"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  platform_device_id_platform_interface:
    dependency: transitive
    description:
      name: platform_device_id_platform_interface
      sha256: c61607594252aaddacf3e4c4371ab08f2ef85ff427817fa6e48a169429610c46
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  platform_device_id_v3:
    dependency: "direct main"
    description:
      name: platform_device_id_v3
      sha256: "022fa690995fe100274793c1a0442b44a351f95760d09ca4e56a76f2f4095658"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  platform_device_id_web:
    dependency: transitive
    description:
      name: platform_device_id_web
      sha256: "58e124594e1165db7f108395a780b1d1e1cd403021978e5228cf4289fbe736d5"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  platform_device_id_windows:
    dependency: transitive
    description:
      name: platform_device_id_windows
      sha256: dbf8dcf03ad8555320ebae2403a3081b79f137f37661874e161fe2de0a84eeeb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: f4f88d4a900933e7267e2b353594774fc0d07fb072b47eedcd5b54e1ea3269f8
      url: "https://pub.dev"
    source: hosted
    version: "2.1.7"
  point_in_polygon:
    dependency: "direct main"
    description:
      path: "."
      ref: feature-fixes
      resolved-ref: a064ee4c81de0b71d442f88c6f6ec92d70865473
      url: "https://github.com/aa-cee/point_in_polygon.git"
    source: git
    version: "1.0.0"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "7c1e5f0d23c9016c5bbd8b1473d0d3fb3fc851b876046039509e18e0c7485f2c"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.3"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "9a96a0a19b594dbc5bf0f1f27d2bc67d5f95957359b461cd9feb44ed6ae75096"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c63b2876e58e194e4b0828fcb080ad0e06d051cb607a6be51a9e084f47cb9367
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: b1c1ac5ce6688d77f65f3375a9abb9319b3cb32486bdc7a1e0fdf004d7ba4e47
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  record:
    dependency: transitive
    description:
      name: record
      sha256: f703397f5a60d9b2b655b3acc94ba079b2d9a67dc0725bdb90ef2fee2441ebf7
      url: "https://pub.dev"
    source: hosted
    version: "4.4.4"
  record_linux:
    dependency: transitive
    description:
      name: record_linux
      sha256: "348db92c4ec1b67b1b85d791381c8c99d7c6908de141e7c9edc20dad399b15ce"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.1"
  record_macos:
    dependency: transitive
    description:
      name: record_macos
      sha256: d1d0199d1395f05e218207e8cacd03eb9dc9e256ddfe2cfcbbb90e8edea06057
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  record_platform_interface:
    dependency: transitive
    description:
      name: record_platform_interface
      sha256: "7a2d4ce7ac3752505157e416e4e0d666a54b1d5d8601701b7e7e5e30bec181b4"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  record_web:
    dependency: transitive
    description:
      name: record_web
      sha256: "219ffb4ca59b4338117857db56d3ffadbde3169bcaf1136f5f4d4656f4a2372d"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  record_windows:
    dependency: transitive
    description:
      name: record_windows
      sha256: "42d545155a26b20d74f5107648dbb3382dbbc84dc3f1adc767040359e57a1345"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.1"
  rename:
    dependency: "direct dev"
    description:
      name: rename
      sha256: "4d08eafe78e0787167c2fcdd5e32bbb0a6b2d8a7d23b38280f590df2a051415c"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  resource_portable:
    dependency: transitive
    description:
      name: resource_portable
      sha256: "76af61ea5af4719f0a5a9776dddb7bc61e0a85fa5b7978423cc20566f2f97b53"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  rive:
    dependency: "direct main"
    description:
      name: rive
      sha256: fb18e38f9de28261e6f4a2d097dff419b4b741b13999fc6c2fd459481a0bbc6f
      url: "https://pub.dev"
    source: hosted
    version: "0.13.18"
  rive_common:
    dependency: transitive
    description:
      name: rive_common
      sha256: "031b29f7b1fc2732d06f60d053e0d7fbe0358fa19e991a91fa3991aa2760b240"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.13"
  rocket_singleton:
    dependency: "direct main"
    description:
      name: rocket_singleton
      sha256: "2c8a1c205a7193ff66a1952bd13a51ff17109ff8544f870bb9c7c58b1c3b5420"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  sanitize_html:
    dependency: transitive
    description:
      name: sanitize_html
      sha256: "12669c4a913688a26555323fb9cec373d8f9fbe091f2d01c40c723b33caa8989"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  screen_brightness:
    dependency: "direct main"
    description:
      name: screen_brightness
      sha256: "7d4ac84ae26b37c01d6f5db7123a72db7933e1f2a2a8c369a51e08f81b3178d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_android:
    dependency: transitive
    description:
      name: screen_brightness_android
      sha256: "8c69d3ac475e4d625e7fa682a3a51a69ff59abe5b4a9e57f6ec7d830a6c69bd6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_ios:
    dependency: transitive
    description:
      name: screen_brightness_ios
      sha256: f08f70ca1ac3e30719764b5cfb8b3fe1e28163065018a41b3e6f243ab146c2f1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_macos:
    dependency: transitive
    description:
      name: screen_brightness_macos
      sha256: "70c2efa4534e22b927e82693488f127dd4a0f008469fccf4f0eefe9061bbdd6a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_platform_interface:
    dependency: transitive
    description:
      name: screen_brightness_platform_interface
      sha256: "9f3ebf7f22d5487e7676fe9ddaf3fc55b6ff8057707cf6dc0121c7dfda346a16"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_windows:
    dependency: transitive
    description:
      name: screen_brightness_windows
      sha256: c8e12a91cf6dd912a48bd41fcf749282a51afa17f536c3460d8d05702fb89ffa
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  settings_ui:
    dependency: "direct main"
    description:
      name: settings_ui
      sha256: d9838037cb554b24b4218b2d07666fbada3478882edefae375ee892b6c820ef3
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  share:
    dependency: transitive
    description:
      name: share
      sha256: "97e6403f564ed1051a01534c2fc919cb6e40ea55e60a18ec23cee6e0ce19f4be"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "81429e4481e1ccfb51ede496e916348668fd0921627779233bd24cc3ff6abd02"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "8568a389334b6e83415b6aae55378e158fbc2314e074983362d20c562780fb06"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "7bf53a9f2d007329ee6f3df7268fd498f8373602f943c975598bbb34649b62a7"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: d4ec5fc9ebb2f2e056c617112aa75dcf92fc2e4faaf2ae999caa297473f75d8a
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d762709c2bbe80626ecc819143013cc820fa49ca5e363620ee20a8b15a3e3daf
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  slide_action:
    dependency: "direct main"
    description:
      name: slide_action
      sha256: "6eabc637d767eaa71905f01a24dbf037c120dd596157e52b7d5c8af82cedcf1c"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  sliver_tools:
    dependency: transitive
    description:
      name: sliver_tools
      sha256: eae28220badfb9d0559207badcbbc9ad5331aac829a88cb0964d330d2a4636a6
      url: "https://pub.dev"
    source: hosted
    version: "0.2.12"
  smart_auth:
    dependency: transitive
    description:
      name: smart_auth
      sha256: a25229b38c02f733d0a4e98d941b42bed91a976cb589e934895e60ccfa674cf6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  sn_progress_dialog:
    dependency: "direct main"
    description:
      name: sn_progress_dialog
      sha256: b739d4b91c0bf575bfe1116b0a12ec2f3345d90c4ec3e7bc88b486daed6dd3e6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  social_media_recorder:
    dependency: "direct main"
    description:
      name: social_media_recorder
      sha256: "4620632866d891866f79d29296451106e94c3b2e4058e8368b882cdef62bbb8a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.12"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: fc0da689e5302edb6177fdd964efcb7f58912f43c28c2047a808f5bfff643d16
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sqflite:
    dependency: "direct main"
    description:
      name: sqflite
      sha256: a9016f495c927cb90557c909ff26a6d92d9bd54fc42ba92e19d4e79d61e798c6
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: bb4738f15b23352822f4c42a531677e5c6f522e079461fd240ead29d8d8a54a6
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0+2"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "9f47fd3630d76be3ab26f0ee06d213679aa425996925ff3feffdec504931c377"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "688af5ed3402a4bde5b3a6c15fd768dbf2621a614950b17f04626c431ab3c4c3"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  swipeable_button_view:
    dependency: "direct main"
    description:
      name: swipeable_button_view
      sha256: "6fbe3fe9084d849828fa49bf1f5d48a107f7797e3c7eff41aa61541c0b6d97f2"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  swiss_knife:
    dependency: transitive
    description:
      name: swiss_knife
      sha256: "3066fc2ed9a90139cf69f1733f6f9714f7a0a4e6a9d668451d5a1e371da8897b"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.5"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: "6e67726b85812afc7105725a23620b876ab7f6b04b8410e211330ffb8c2cdbe8"
      url: "https://pub.dev"
    source: hosted
    version: "26.2.14"
  syncfusion_flutter_datepicker:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_datepicker
      sha256: "9d0e1db2f49cef3fde28c5a42c7ef9b64fc9a49c52557eb6961cd41af3cf8adc"
      url: "https://pub.dev"
    source: hosted
    version: "26.2.14"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "664d3a9a64782fcdeb83ce9c6b39e78fd2971d4e37827b9b06c3aa1edc5e760c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.3"
  theme_mode_handler:
    dependency: "direct main"
    description:
      name: theme_mode_handler
      sha256: "06b08e58e66f80b89c4945a5883a85f7ceeb040056e8c559e1ccc0f2509309d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  time:
    dependency: transitive
    description:
      name: time
      sha256: "83427e11d9072e038364a5e4da559e85869b227cf699a541be0da74f14140124"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  time_change_detector:
    dependency: "direct main"
    description:
      name: time_change_detector
      sha256: "8f4b883a15f5ccd50c122ec18fcb87b8a9c3cf1e392055fbea808d6b64643ddb"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  top_snackbar_flutter:
    dependency: "direct main"
    description:
      name: top_snackbar_flutter
      sha256: "22d14664a13db6ac714934c3382bd8d4daa57fb888a672f922df71981c5a5cb2"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  tuple:
    dependency: transitive
    description:
      name: tuple
      sha256: a97ce2013f240b2f3807bcbaf218765b6f301c3eff91092bcfa23a039e7dd151
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  typewritertext:
    dependency: "direct main"
    description:
      name: typewritertext
      sha256: c6b90cdc9f7fd7ae3235d9c926012000f7a1765cd49d67b78370dc61123212b2
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: d315be0f6641898b280ffa34e2ddb14f3d12b1a37882557869646e0cc363d0cc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: b1c9e98774adf8820c96fbc7ae3601231d324a7d5ebd8babe27b6dfac91357ba
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "31222ffb0063171b526d3e569079cf1f8b294075ba323443fdc690842bfd4def"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: bba3373219b7abb6b5e0d071b0fe66dfbe005d07517a68e38d4fc3638f35c6d3
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "9f2d390e096fdbe1e6e6256f97851e51afc2d9c423d3432f1d6a02a8a9a8b9fd"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "980e8d9af422f477be6948bdfb68df8433be71f5743a188968b0c1b887807e50"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "7fd2f55fe86cea2897b963e864dc01a7eb0719ecc65fcef4c1cc3d686d718bb2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "7754a1ad30ee896b265f8d14078b0513a4dba28d358eabb9d5f339886f4a1adc"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "0f0c746dd2d6254a0057218ff980fc7f5670fd0fcf5e4db38a490d31eed4ad43"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.9+1"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "0edf6d630d1bfd5589114138ed8fada3234deacc37966bec033d3047c29248b7"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.9+1"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: d24333727332d9bd20990f1483af4e09abdb9b1fc7c3db940b56ab5c42790c26
      url: "https://pub.dev"
    source: hosted
    version: "1.1.9+1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  video_compress:
    dependency: "direct main"
    description:
      path: "."
      ref: caf41183e84adebd6ef5c8fba547937daf556a65
      resolved-ref: caf41183e84adebd6ef5c8fba547937daf556a65
      url: "https://github.com/SpectoraSoftware/VideoCompress"
    source: git
    version: "1.0.0"
  video_editor:
    dependency: "direct main"
    description:
      name: video_editor
      sha256: "4bfe1d2fb1b45df1af68adafc0293e1111fe982d346620213301aff46f33c764"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.2"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: e16f0a83601a78d165dabc17e4dac50997604eb9e4cc76e10fa219046b70cef3
      url: "https://pub.dev"
    source: hosted
    version: "2.8.1"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "3fe89ab07fdbce786e7eb25b58532d6eaf189ceddc091cb66cba712f8d9e8e55"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.10"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: bc923884640d6dc403050586eb40713cdb8d1d84e6886d8aca50ab04c59124c2
      url: "https://pub.dev"
    source: hosted
    version: "2.5.2"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: be72301bf2c0150ab35a8c34d66e5a99de525f6de1e8d27c0672b836fe48f73a
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: ab7a462b07d9ca80bed579e30fb3bce372468f1b78642e0911b10600f2c5cb5b
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  video_thumbnail:
    dependency: transitive
    description:
      name: video_thumbnail
      sha256: "3455c189d3f0bb4e3fc2236475aa84fe598b9b2d0e08f43b9761f5bc44210016"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.3"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: f6be3ed8bd01289b34d679c2b62226f63c0e69f9fd2e50a6b3c1c729a961041b
      url: "https://pub.dev"
    source: hosted
    version: "14.3.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "3d2ad6751b3c16cf07c7fca317a1413b3f26530319181b37e3b9039b84fc01d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: cd3543bd5798f6ad290ea73d210f423502e71900302dde696f8bff84bf89a1cb
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  win32:
    dependency: "direct main"
    description:
      name: win32
      sha256: "10169d3934549017f0ae278ccb07f828f9d6ea21573bab0fb77b0e1ef0fce454"
      url: "https://pub.dev"
    source: hosted
    version: "5.7.2"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "41fd8a189940d8696b1b810efb9abcf60827b6cbfab90b0c43e8439e3a39d85a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "589ada45ba9e39405c198fe34eb0f607cddb2108527e658136120892beac46d2"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "5bc72e1e45e941d825fd7468b9b4cc3b9327942649aeb6fc5cdbf135f0a86e84"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  xxh3:
    dependency: transitive
    description:
      name: xxh3
      sha256: a92b30944a9aeb4e3d4f3c3d4ddb3c7816ca73475cd603682c4f8149690f56d7
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  youtube_player_flutter:
    dependency: "direct main"
    description:
      name: youtube_player_flutter
      sha256: "72d487e1a1b9155a2dc9d448c137380791101a0ff623723195275ac275ac6942"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.2"
  zoom_tap_animation:
    dependency: "direct main"
    description:
      name: zoom_tap_animation
      sha256: d9f7a73cab65aa1546ba6886b5e21d3c8ccccb34e4e5f770301c306d4868bee0
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
sdks:
  dart: ">=3.5.0 <4.0.0"
  flutter: ">=3.18.0-18.0.pre.54"
