import 'dart:async';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:external_path/external_path.dart';
import 'package:flutter_alice/alice.dart';
import 'package:get_it/get_it.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:http_interceptor/http/http.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/photo/image_picker_utils.dart';
import 'package:yetakchi/features/auth/data/datasources/auth_local_datasources.dart';
import 'package:yetakchi/features/auth/data/datasources/auth_remote_datasources.dart';
import 'package:yetakchi/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:yetakchi/features/auth/domain/repositories/auth_repository.dart';
import 'package:yetakchi/features/auth/domain/usescases/auth.dart';
import 'package:yetakchi/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:yetakchi/features/electron_library/book_list/domain/usecases/u_delete_book.dart';
import 'package:yetakchi/features/electron_library/book_test/book_test_cubit/book_test_cubit.dart';
import 'package:yetakchi/features/electron_library/book_test/book_test_result_cubit/book_test_result_cubit.dart';
import 'package:yetakchi/features/complaint/presentation/bloc/complaint_new/complaint_cubit.dart';
import 'package:yetakchi/features/complaint/presentation/bloc/complaint_read/complain_read_cubit.dart';
import 'package:yetakchi/features/complaint/presentation/bloc/complaint_send/complaint_send_cubit.dart';
import 'package:yetakchi/features/electron_library/book_list/domain/usecases/u_row.dart';
import 'package:yetakchi/features/electron_library/book_list/presentation/bloc/row_bloc/row_bloc.dart';
import 'package:yetakchi/features/home/<USER>/datasources/home_local_datasources.dart';
import 'package:yetakchi/features/home/<USER>/datasources/home_remote_datasources.dart';
import 'package:yetakchi/features/home/<USER>/repositories/repository_impl.dart';
import 'package:yetakchi/features/home/<USER>/repositories/home_repository.dart';
import 'package:yetakchi/features/home/<USER>/usescases/u_category.dart';
import 'package:yetakchi/features/home/<USER>/bloc/category_bloc.dart';
import 'package:yetakchi/features/labor_discipline/presentation/cubit/labor_discipline_cubit.dart';
import 'package:yetakchi/features/lock/data/datasources/lock_local_datasources.dart';
import 'package:yetakchi/features/lock/data/repositories/lock_repositories.dart';
import 'package:yetakchi/features/lock/domain/repositories/lock_repositories.dart';
import 'package:yetakchi/features/lock/domain/usescases/u_lock.dart';
import 'package:yetakchi/features/lock/presentation/bloc/pin_bloc.dart';
import 'package:yetakchi/features/login/data/datasources/login_local_datasources.dart';
import 'package:yetakchi/features/login/data/datasources/login_remote_datasources.dart';
import 'package:yetakchi/features/login/data/repositories/login_repository_impl.dart';
import 'package:yetakchi/features/login/data/repositories/u_login.dart';
import 'package:yetakchi/features/login/presentation/bloc/login_bloc.dart';
import 'package:yetakchi/features/not_send_page/presentation/bloc/not_sent_bloc/not_send_cubit.dart';
import 'package:yetakchi/features/password/presentation/bloc/pass_bloc.dart';
import 'package:yetakchi/features/profile/presentation/bloc/web_password/web_password_cubit.dart';
import 'package:yetakchi/features/quiz/presentation/cubits/test/test_cubit.dart';
import 'package:yetakchi/features/quiz/presentation/cubits/test_card_finished_cubit/test_card_finished_cubit.dart';
import 'package:yetakchi/features/quiz/presentation/cubits/test_card_new_cubit/test_card_new_cubit.dart';
import 'package:yetakchi/features/quiz/presentation/cubits/test_result_cubit/test_result_cubit.dart';
import 'package:yetakchi/features/sent_page/presentation/bloc/sent_cubit.dart';
import 'package:yetakchi/features/statistics/presentation/bloc/statistic_cubit.dart';
import 'package:yetakchi/features/tasks/data/datasource/task_done_local_datasource.dart';
import 'package:yetakchi/features/tasks/data/datasource/task_done_remote_datasource.dart';
import 'package:yetakchi/features/tasks/data/datasource/task_local_datasource.dart';
import 'package:yetakchi/features/tasks/data/datasource/task_remote_datasource.dart';
import 'package:yetakchi/features/tasks/data/repository/special_done_task_repository_impl.dart';
import 'package:yetakchi/features/tasks/data/repository/special_task_repository_impl.dart';
import 'package:yetakchi/features/tasks/domain/repository/special_done_task_repository.dart';
import 'package:yetakchi/features/tasks/domain/repository/special_task_repository.dart';
import 'package:yetakchi/features/tasks/domain/usecases/special_done_task.dart';
import 'package:yetakchi/features/tasks/domain/usecases/special_task.dart';
import 'package:yetakchi/features/tasks/presentation/bloc/download/download_file_cubit.dart';
import 'package:yetakchi/features/tasks/presentation/bloc/tasks_done_bloc/task_done_cubit.dart';
import 'package:yetakchi/features/tasks/presentation/bloc/tasks_new_bloc/task_new_cubit.dart';
import 'package:yetakchi/push_notifications/notification_service.dart';
import '../core/location/location_service.dart';
import '../core/network/interceptors.dart';
import '../core/network/network_info.dart';
import '../core/utils/app_constants.dart';
import '../features/electron_library/book_list/data/datasource/book_local_datasource.dart';
import '../features/electron_library/book_list/data/datasource/book_remote_datasource.dart';
import '../features/electron_library/book_list/data/repositories/book_repository_impl.dart';
import '../features/electron_library/book_list/domain/repositories/book_repository.dart';
import '../features/electron_library/book_list/domain/usecases/u_book.dart';
import '../features/electron_library/book_list/presentation/bloc/book_bloc.dart';
import '../features/quiz/presentation/cubits/test_result_review/test_result_review_cubit.dart';

final di = GetIt.instance;
//di is referred to as Service Locator

///TODO: Set alice switchable in debug
Future<void> init() async {
  /// Local cache
  await GetStorage.init();
  di.registerLazySingleton(() => GetStorage());
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  di.registerLazySingleton(() => prefs);

  ///Versioning
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  APP_VERSION = packageInfo.version;

  // bool isSameVer =
  //     (prefs.getString('ver') ?? '0') == APP_VERSION ? true : false;
  //
  // if (!isSameVer) {
  //   prefs.setString('ver', APP_VERSION);
  // }

  ///Android version

  if (Platform.isAndroid) {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    final androidInfo = await deviceInfoPlugin.androidInfo;
    di.registerLazySingleton(() => androidInfo);
  }

  /// Blocs
  di.registerFactory(() => DownloadFileCubit());

  //login
  di.registerFactory(
    () => LoginBloc(loginData: di()),
  );

  //auth
  di.registerFactory(
    () => AuthBloc(authData: di()),
  );

  //lock
  di.registerFactory(
    () => PinBloc(
      pass: di(),
    ),
  );

  // password
  di.registerFactory(
    () => PassBloc(
      sharedPreferences: di(),
    ),
  );

  //home
  di.registerFactory(
    () => HomeBloc(home: di()),
  );

  //book
  di.registerFactory(
    () => BookBloc(book: di()),
  );

  di.registerFactory(
    () => RowBloc(deleteRow: di(), row: di()),
  );

  ///Repositories

  // lock
  di.registerLazySingleton<PassRepository>(
    () => PinRepositoryImpl(passLocalDataSource: di()),
  );

  // login[
  di.registerLazySingleton<LoginRepository>(
    () => LoginRepositoryImpl(
        networkInfo: di(),
        loginRemoteDatasource: di(),
        loginLocalDatasource: di()),
  );

  // auth
  di.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
        networkInfo: di(),
        authRemoteDatasource: di(),
        authLocalDatasource: di()),
  );

  // home
  di.registerLazySingleton<HomeRepository>(
    () => HomeRepositoryImpl(
        networkInfo: di(),
        homeRemoteDatasourceImpl: di(),
        homeLocalDatasourceImpl: di()),
  );
  // book
  di.registerLazySingleton<BookRepository>(
    () => BookRepositoryImpl(
        networkInfo: di(),
        bookRemoteDataSourceImp: di(),
        bookLocalDatasourceImpl: di()),
  );

  /// UsesCases

  // lock
  di.registerLazySingleton(() => UPin(repository: di()));
  //login
  di.registerLazySingleton(() => LoginData(loginRepository: di()));
  //auth
  di.registerLazySingleton(() => AuthData(authRepository: di()));
  // home
  di.registerLazySingleton(() => UHome(homeRepository: di()));
  // book
  di.registerLazySingleton(() => UBook(bookRepository: di()));

  di.registerLazySingleton(() => URow(bookRepository: di()));
  di.registerLazySingleton(() => UDeleteBook(bookRepository: di()));

  /// Data sources

  // lock
  di.registerLazySingleton(
    () => PinLocalDataSourceImpl(sharedPreferences: di()),
  );
  //login
  di.registerLazySingleton(
    () => LoginRemoteDatasourceImpl(client: di()),
  );
  di.registerLazySingleton(
    () => LoginLocalDataSourceImpl(sharedPreferences: di()),
  );

  //auth
  di.registerLazySingleton(
    () => AuthRemoteDatasourceImpl(client: di()),
  );
  di.registerLazySingleton(
    () => AuthLocalDataSourceImpl(sharedPreferences: di()),
  );

  //home
  di.registerLazySingleton(
    () => HomeRemoteDatasourceImpl(sharedPreferences: di(), dioClient: di()),
  );
  di.registerLazySingleton(
    () => HomeLocalDataSourceImpl(),
  );
  //book
  di.registerLazySingleton(
    () => BookRemoteDataSourceImp(dioClient: di()),
  );
  di.registerLazySingleton(
    () => BookLocalDatasourceImpl(),
  );

  /// Image picker
  di.registerLazySingleton<ImagePickerUtils>(() => ImagePickerUtilsImpl());

  /// Location Service
  di.registerLazySingleton<LocationService>(() => LocationServiceImpl());

  ///Isar
  final IsarService isar = await IsarService.buildIsarService();
  di.registerLazySingleton(() => isar);

  /// Network Info
  di.registerLazySingleton(() => InternetConnectionChecker());

  di.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(di()));

  ///Interceptors
  ///
  Alice alice = Alice(darkTheme: true);
  di.registerLazySingleton(() => alice);

  //Dio interceptor
  final Dio dio = Dio(BaseOptions(
      // baseUrl: baseUrl,
      connectTimeout: Duration(seconds: 60),
      receiveTimeout: Duration(seconds: 60)));

  //dio.interceptors.add(alice.getDioInterceptor());
  dio.interceptors.add(AppInterceptor(prefs: di(), networkInfo: di()));
  di.registerLazySingleton<Dio>(() => dio);

  //HTTP interceptor
  di.registerLazySingleton<InterceptedClient>(() =>
      InterceptedClient.build(interceptors: [ErrorInterceptor(prefs: di())]));
  di.registerLazySingleton<http.Client>(() => http.Client());

  ///Camera
  // Obtain a list of the available cameras on the device.
  var cameras = await availableCameras();

  di.registerLazySingleton<List<CameraDescription>>(() => cameras);

  ///Notification channel
  String notificationToken;
  var listener =
  InternetConnectionChecker().onStatusChange.listen((status) async {
    switch (status) {
      case InternetConnectionStatus.connected:
        print('Data connection is available.');
        notificationToken = await createChannel(di());
        if (notificationToken.isNotEmpty) {
          prefs.setString(firebaseTokenKEY, notificationToken);
          print('Firebase token is set: ' + notificationToken);
        } else {
          print('Firebase token is EMPTY');
        }
        break;
      case InternetConnectionStatus.disconnected:
        print('You are disconnected from the internet.');
        break;
    }
  });

  ///Update
  // Check for in app updates

  AppUpdateInfo _updateInfo = AppUpdateInfo(
      updateAvailability: UpdateAvailability.unknown,
      immediateUpdateAllowed: false,
      immediateAllowedPreconditions: [],
      flexibleUpdateAllowed: false,
      flexibleAllowedPreconditions: [],
      availableVersionCode: 0,
      installStatus: InstallStatus.unknown,
      packageName: 'com.example.uz',
      clientVersionStalenessDays: 0,
      updatePriority: 0);

  if (Platform.isAndroid ) {
    try {
      _updateInfo = await InAppUpdate.checkForUpdate();
    } catch (e) {
      print("Error in 'In-AppUpdate:' $e");
    }
  }
  di.registerLazySingleton<AppUpdateInfo>(() => _updateInfo);

  ///Getting directories

  //Problem at saving to downloads
  if (Platform.isAndroid && false) {
    Future<List<String>> getExternalPath() async {
      return await ExternalPath.getExternalStorageDirectories();
    }

    String downloadSoundDir;
    getExternalPath().then((value) {
      downloadSoundDir =
          "${value.first}/${ExternalPath.DIRECTORY_DOWNLOADS}/Yetakchi";
      di.registerLazySingleton<Directory>(() => Directory(downloadSoundDir));
    });
  } else {
    var dir = await getApplicationDocumentsDirectory();
    di.registerLazySingleton<Directory>(
        () => Directory("${dir.path}/Yetakchi"));
  }

  ///Task section (TODO: Place this to appropriate place)
  di.registerLazySingleton(() => TaskLocalDataSourceImpl(isarService: di()));
  di.registerLazySingleton(() => TaskRemoteDatasourceImpl(dio: di()));

  di.registerLazySingleton<SpecialNewTaskRepository>(() =>
      SpecialNewTaskRepositoryImpl(
          taskRemoteDatasourceImpl: di(),
          taskLocalDataSourceImpl: di(),
          networkInfo: di()));

  di.registerLazySingleton(
      () => SpecialNewTaskUseCase(specialNewTaskRepository: di()));
  di.registerFactory(
    () => TaskNewCubit(specialNewTaskUseCase: di()),
  );

  ///TaskDone

  di.registerLazySingleton(
      () => TaskDoneLocalDataSourceImpl(isarService: di()));
  di.registerLazySingleton(() => TaskDoneRemoteDatasourceImpl(dio: di()));

  di.registerLazySingleton<SpecialDoneTaskRepository>(() =>
      SpecialDoneTaskRepositoryImpl(
          taskRemoteDatasourceImpl: di(),
          taskLocalDataSourceImpl: di(),
          networkInfo: di()));

  di.registerLazySingleton(
      () => SpecialDoneTaskUseCase(specialDoneTaskRepository: di()));
  di.registerFactory(
    () => TaskDoneCubit(specialDoneTaskUseCase: di()),
  );

  ///NotSendTask
  di.registerFactory(() => NotSendTaskCubit(isarService: di()));
  di.registerFactory(
      () => WebPasswordCubit(dio: di(), networkInfo: di(), prefs: di()));
  di.registerFactory(() => StatisticCubit(
      dio: di(), networkInfo: di(), prefs: di(), isarService: di()));
  di.registerFactory(() =>
      SentCubit(dio: di(), networkInfo: di(), prefs: di(), isarService: di()));

  ///NotSendWork
  di.registerFactory(() => NotSendWorkCubit(isarService: di()));
  di.registerFactory(() => ComplaintSendCubit(
        dio: di(),
        networkInfo: di(),
      ));
  di.registerFactory(() => ComplaintCubit(
        dio: di(),
        networkInfo: di(),
        isarService: di(),
      ));
  di.registerFactory(() => ComplaintReadCubit(
        dio: di(),
        networkInfo: di(),
        isarService: di(),
      ));

  di.registerFactory(() => TestCardNewCubit(
      dio: di(), networkInfo: di(), isarService: di(), gs: di()));
  di.registerFactory(() =>
      TestCardFinishedCubit(dio: di(), networkInfo: di(), isarService: di()));
  di.registerFactory(() => TestCubit(dio: di(), networkInfo: di()));
  di.registerFactory(() => TestResultCubit(dio: di(), networkInfo: di()));
  di.registerFactory(() => TestResultReviewCubit(dio: di(), networkInfo: di()));
  di.registerFactory(() => BookTestCubit(dio: di(), networkInfo: di()));
  di.registerFactory(() => BookTestResultCubit(dio: di(), networkInfo: di()));
  di.registerFactory(() => LaborDisciplineCubit(dio: di(), networkInfo: di()));
}
