import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

enum Environment { dev, prod }

abstract class AppEnvironment {
  static late Environment _environment;
  static late String baseApiUrl;

  static Environment get environment => _environment;

  static setupEnv(Environment env) {
    _environment = env;
    switch (env) {
      case Environment.dev:
        {
          EMULATOR = true;
          baseApiUrl = testUrl;
          break;
        }
      case Environment.prod:
        {
          EMULATOR = false;
          baseApiUrl = prodUrl;
          break;
        }
    }
  }
}
