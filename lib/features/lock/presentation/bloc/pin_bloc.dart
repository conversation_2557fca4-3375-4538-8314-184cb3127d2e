import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dartz/dartz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/features/lock/domain/usescases/u_lock.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'pin_event.dart';

part 'pin_state.dart';

class PinBloc extends Bloc<PinEvent, PinState> {
  final UPin _pass;

  PinBloc({required UPin pass})
      : _pass = pass,
        super(PinInitial(LocaleKeys.pin_code.tr())) {
    on<PinCompileEvent>(
      _nextPage,
      transformer: restartable(), //Unknown line
    );
    on<PinInitEvent>(
      _init,
      transformer: restartable(), //Unknown line
    );
    on<PinConfirmEvent>(
      _confirm,
      transformer: restartable(), //Unknown line
    );
  }

  FutureOr<void> _confirm(PinConfirmEvent event, Emitter<PinState> emit) async {
    emit(PinConfirmState(message: LocaleKeys.confirm_pin_code.tr()));
  }

  FutureOr<void> _init(PinInitEvent event, Emitter<PinState> emit) async {
    emit(const PinInitial(""));
  }

  FutureOr<void> _nextPage(
      PinCompileEvent event, Emitter<PinState> emit) async {
    Either<Failure, bool>? result;

    if (event.confirmPinController != "") {
      if (event.pinController == event.confirmPinController) {
        emit(const PinLoading(""));
        result = await _pass(PasswordParams(event.pinController, true));
      } else {
        emit(PinError(
            errorMessage: LocaleKeys.pin_code_not_match.tr(),
            message: "Not Match Error"));
      }
    } else {
      emit(const PinLoading(""));
      result = await _pass(PasswordParams(event.pinController, false));
    }

    result?.fold(
        (f) =>
            {emit(PinError(errorMessage: f.message, message: "Local Error"))},
        (s) => {
              if (s)
                {emit(const PinSuccess(""))}
              else
                {
                  emit(PinError(
                      errorMessage: LocaleKeys.pin_code_error_retry.tr(),
                      message: "Logic error"))
                }
            });

    // event.passController.clear();
    // event.confirmPassController.clear();
  }
}
