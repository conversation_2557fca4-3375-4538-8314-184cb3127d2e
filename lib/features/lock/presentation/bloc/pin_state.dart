part of 'pin_bloc.dart';

@immutable
abstract class PinState {
  final String message;

  const PinState({required this.message});
}

class PinInitial extends PinState {
  const PinInitial(String pass) : super(message: pass);
}

class PinSuccess extends PinState {
  const PinSuccess(String pass) : super(message: pass);
}

class PinLoading extends PinState {
  const PinLoading(String pass) : super(message: pass);
}

class PinError extends PinState {
  final String errorMessage;

  const PinError({required String message, required this.errorMessage})
      : super(message: message);
}
class PinConfirmState extends PinState {
  const PinConfirmState({required String message}) : super(message: message);

}
