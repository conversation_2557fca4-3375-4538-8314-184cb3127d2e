import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/auth.dart';
import 'package:yetakchi/features/home/<USER>/navigation.dart';
import 'package:yetakchi/features/lock/presentation/widgets/number.dart';
import 'package:yetakchi/generated/assets.dart';

Container numPad(TextEditingController _pinPutController, BuildContext context) {
  return Container(
    // height: 360.h,
    // width: 300.w,
    margin: EdgeInsets.symmetric(horizontal: 40.w),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            number(_pinPutController, '1',context),
            number(_pinPutController, '2',context),
            number(_pinPutController, '3',context),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            number(_pinPutController, '4',context),
            number(_pinPutController, '5',context),
            number(_pinPutController, '6',context),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            number(_pinPutController, '7',context),
            number(_pinPutController, '8',context),
            number(_pinPutController, '9',context),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              height: 40.h,
              width: cNumberLockW90.w,
              child: GestureDetector(
                  onTap: () async {
                    try {
                      final isAuthenticated = await LocalAuthApi.authenticate();
                      if (isAuthenticated) {
                        Navigator.pushReplacement(
                          context,
                          CupertinoPageRoute(
                              builder: (context) =>
                                  const BottomNavigationPage()),
                        );
                      }
                    } catch (e) {
                      debugPrint(e.toString());
                    }
                  },
                  child: Platform.isAndroid
                      ? SvgPicture.asset(
                    Assets.iconsFingerScan,
                    color: cFirstColor,
                    height: 25.h,
                  )
                      : Image.asset(
                    Assets.iconsBiometrics,
                    color: cFirstColor,
                    height: 25.h,
                  )),
            ),
            number(_pinPutController, '0',context),
            GestureDetector(
              onTap: () {
                if (_pinPutController.text.isNotEmpty) {
                  _pinPutController.text = _pinPutController.text
                      .substring(0, _pinPutController.text.length - 1);
                  _pinPutController.selection = TextSelection.collapsed(
                      offset: _pinPutController.text.length);
                }
              },
              behavior: HitTestBehavior.translucent,
              child: SizedBox(
                height: cNumberLockH90.h,
                width: cNumberLockW90.w,
                child: Center(
                  child: SvgPicture.asset(
                    Assets.iconsIcDelete,
                    colorFilter: ColorFilter.mode(Theme.of(context).textTheme.bodySmall!.color!,BlendMode.srcIn),
                    width: 50.h,
                    height: 50.h,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    ),
  );
}
