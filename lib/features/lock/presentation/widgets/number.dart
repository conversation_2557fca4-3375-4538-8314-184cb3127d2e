import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

Widget number(TextEditingController _pinPutController, String number,
    BuildContext context) {
  return MaterialButton(
    focusColor: cSecondColor,
    hoverColor: cSecondColor,
    highlightColor: cSecondColor,
    height: cNumberLockH90.h,
    shape: CircleBorder(),
    onPressed: () {
      if (_pinPutController.text.length != 4 &&
          _pinPutController.text.length != 5) {
        _pinPutController.text = _pinPutController.text + number;
      }
      _pinPutController.selection =
          TextSelection.collapsed(offset: _pinPutController.text.length);
    },
    child: SizedBox(
      height: context.isTablet ? 100.h : null,
      width: context.isTablet ? 100.h : null,
      child: Center(
        child: Text(
          number,
          textAlign: TextAlign.center,
          style: TextStyle(
              fontSize: cNumberLockText42.sp,
              fontWeight: FontWeight.w400,
              color: Theme.of(context).textTheme.bodySmall?.color),
        ),
      ),
    ),
  );
}
