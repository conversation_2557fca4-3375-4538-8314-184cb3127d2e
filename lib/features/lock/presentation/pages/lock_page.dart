import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/logout.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/home/<USER>/navigation.dart';
import 'package:yetakchi/features/lock/presentation/bloc/pin_bloc.dart';
import 'package:yetakchi/features/lock/presentation/widgets/num_pad.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:yetakchi/features/password/presentation/bloc/pass_bloc.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class PincodeScreen extends StatefulWidget {
  const PincodeScreen({Key? key}) : super(key: key);

  static Widget screen() => MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => di<PinBloc>()),
          BlocProvider(
              create: (context) => di<PassBloc>()..add(IsSharedPinEmpty())),
        ],
        child: const PincodeScreen(),
      );

  @override
  _PincodeScreenState createState() => _PincodeScreenState();
}

class _PincodeScreenState extends State<PincodeScreen> {
  late PinBloc bloc;
  late PassBloc blocCheck;
  bool onError = false;
  bool isNotSet = false;
  bool isAssigned = false;
  bool isForgotten = false;
  var confirm = "";
  SharedPreferences prefs = di();

  @override
  void initState() {
    bloc = BlocProvider.of<PinBloc>(context);
    blocCheck = BlocProvider.of<PassBloc>(context);
    super.initState();
  }

  @override
  void dispose() {
    bloc.close();
    blocCheck.close();
    super.dispose();
  }

  final TextEditingController _pinPutController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColorLight,
      body: SafeArea(
        child: Container(
          decoration: BoxDecoration(color: Theme.of(context).primaryColorLight),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: EdgeInsets.only(right: 10.w),
                    child: Column(children: [
                      SizedBox(height: 10.h),
                      BlocBuilder<PinBloc, PinState>(builder: (context, state) {
                        return BlocBuilder<PassBloc, PassState>(
                          builder: (context, statePass) {
                            if (state is PinError) {
                              return TextButton(
                                  onPressed: () {
                                    showDialog(
                                        context: context,
                                        builder: (_) {
                                          return LogOutDialog();
                                        });
                                  },
                                  child: Text(
                                    LocaleKeys.forgot_password.tr(),
                                    style: TextStyle(
                                      fontSize: 15.sp,
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyMedium!
                                          .color,
                                    ),
                                    textAlign: TextAlign.center,
                                  ));
                            } else
                              return SizedBox(height: 40.h);
                          },
                        );
                      }),
                      SizedBox(
                        height: 80.h,
                      ),
                    ]),
                  ),
                ),
                Container(
                    margin: EdgeInsets.symmetric(horizontal: 50.w),
                    child: BlocBuilder<PinBloc, PinState>(
                        builder: (context, state) {
                      return BlocBuilder<PassBloc, PassState>(
                        builder: (context, statePass) {
                          if (statePass is PassIsEmptyState) {
                            isNotSet = statePass.isEmpty;
                            // CustomToast.showToast(isNotSet.toString());
                          }

                          if (state is PinSuccess) {
                            SchedulerBinding.instance.addPostFrameCallback((_) {
                              //Unknown line
                              Navigator.pushReplacement(
                                context,
                                CupertinoPageRoute(
                                    builder: (context) =>
                                        const BottomNavigationPage()),
                              );
                            });
                          }
                          if (state is PinInitial) {
                            isAssigned = false;
                            return Column(
                              children: [
                                isNotSet
                                    ? Padding(
                                        padding: EdgeInsets.only(bottom: 40.h),
                                        child: Text(
                                          LocaleKeys.create_pin_code.tr(),
                                          style: TextStyle(
                                            fontSize: 24.sp,
                                            fontWeight: FontWeight.w700,
                                            color:
                                                Theme.of(context).primaryColor,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      )
                                    : Padding(
                                        padding: EdgeInsets.only(bottom: 40.h),
                                        child: Text(
                                          LocaleKeys.pin_code_text.tr(),
                                          style: TextStyle(
                                            fontSize: 24.sp,
                                            fontWeight: FontWeight.w700,
                                            color:
                                                Theme.of(context).primaryColor,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                numConsole(),
                                SizedBox(
                                  height: 40.h,
                                ),
                                Text(
                                  state.message,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: cGrayColor1,
                                      fontSize: 14.sp,
                                      fontFamily: 'Regular'),
                                ),
                              ],
                            );
                          } else if (state is PinError) {
                            isAssigned = false;

                            Timer(Duration(milliseconds: 10), () {
                              _pinPutController.clear();
                            });

                            return Column(
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(bottom: 40.h),
                                  child: Text(
                                    LocaleKeys.enter_pin_code.tr(),
                                    style: TextStyle(
                                      fontSize: 24.sp,
                                      fontWeight: FontWeight.w700,
                                      color: cRedColor,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                numConsole(),
                                SizedBox(
                                  height: 40.h,
                                ),
                                Text(
                                  state.errorMessage,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: cRedTextColor,
                                      fontSize: 14.sp,
                                      fontFamily: 'Regular'),
                                ),
                              ],
                            );
                          } else if (state is PinConfirmState) {
                            return Column(
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(bottom: 40.h),
                                  child: Text(
                                    LocaleKeys.confirm_pin_code1.tr(),
                                    style: TextStyle(
                                      fontSize: 24.sp,
                                      fontWeight: FontWeight.w700,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                numConsole(),
                                SizedBox(
                                  height: 40.h,
                                ),
                                Text(
                                  state.message,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: cGrayColor1,
                                      fontSize: 14.sp,
                                      fontFamily: 'Regular'),
                                ),
                              ],
                            );
                          } else {
                            return SizedBox(
                                height: 150.h,
                                child: Center(
                                  child: CupertinoActivityIndicator(
                                    radius: 30.r,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ));
                          }
                        },
                      );
                    })),
                SizedBox(
                  height: 40.h,
                ),
                numPad(_pinPutController, context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget numConsole() {
    var opacity = 40;

    var themeColor = Theme.of(context).primaryColor;
    return SizedBox(
      width: 160.w,
      height: context.isTablet ? 40.h : 30.h,
      child: PinCodeTextField(
        showCursor: false,
        appContext: context,
        controller: _pinPutController,
        length: 4,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        obscuringWidget: Container(
          height: context.isTablet ? 40.h : 30.h,
          width: context.isTablet ? 40.h : 30.h,
          decoration: BoxDecoration(
            color: cFirstColor,
            borderRadius: BorderRadius.circular(50.r),
          ),
        ),
        enableActiveFill: true,
        enablePinAutofill: true,
        pinTheme: PinTheme(
          fieldHeight: context.isTablet ? 40.h : 30.h,
          fieldWidth: context.isTablet ? 40.h : 30.h,
          borderWidth: 0,
          shape: PinCodeFieldShape.circle,
          activeColor: themeColor.withAlpha(opacity),
          inactiveColor: Colors.transparent,
          disabledColor: themeColor.withAlpha(opacity),
          activeFillColor: cFirstColor,
          selectedFillColor: themeColor.withAlpha(opacity),
          inactiveFillColor: themeColor.withAlpha(opacity),
          errorBorderColor: themeColor.withAlpha(opacity),
        ),
        onCompleted: (value) {
          if (value.length == 4) {
            if (isNotSet) {
              if (isAssigned == false) {
                // CustomToast.showToast("Reassign");
                confirm = _pinPutController.text;
                bloc.add(PinConfirmEvent());
              } else {
                // CustomToast.showToast(
                //     "${_pinPutController.text} & ${confirm}");
                bloc.add(PinCompileEvent(
                    pinController: confirm,
                    confirmPinController: _pinPutController.text));
              }
              _pinPutController.clear();
              isAssigned = true;
            } else {
              String variable = "";
              bloc.add(PinCompileEvent(
                  pinController: _pinPutController.text,
                  confirmPinController: variable));
            }
          }
        },
        onChanged: (controllerPin) {},
      ),
    );
  }
}
