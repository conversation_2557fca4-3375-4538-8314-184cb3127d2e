import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/header_widget.dart';
import 'package:yetakchi/features/app_time/bloc/app_exist_cubit.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:get/get.dart' hide Trans;

class AppTimePage extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => AppExistCubit(),
      child: AppTimePage(),
    );
  }

  const AppTimePage({super.key});

  @override
  State<AppTimePage> createState() => _AppTimePageState();
}

class _AppTimePageState extends State<AppTimePage> with WidgetsBindingObserver {
  late AppExistCubit bloc;

  @override
  void initState() {
    super.initState();
    bloc = BlocProvider.of<AppExistCubit>(context);
    bloc.checkAppExist();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.detached:
        {}
      case AppLifecycleState.resumed:
        {
          print("--------------------Resumed: APP_TIME_PAGE------------------------");
          bloc.checkAppExist();
        }
      case AppLifecycleState.inactive:
        {}
      case AppLifecycleState.hidden:
        {}
      case AppLifecycleState.paused:
        {}
    }
  }

  @override
  Widget build(BuildContext context) {
    final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
    final DateFormat formatterHour = DateFormat('HH:mm:ss');
    return Scaffold(
        appBar: AppHeaderWidget(
          title: 'Ibrat farzandlari',
          onBackTap: () {
            Get.back();
          },
          isBackVisible: true,
        ),
        body: BlocBuilder<AppExistCubit, AppExist>(
          builder: (context, state) {
            if (state.status == AppExistStatus.initial) {
              return Center(
                child: CircularProgressIndicator(
                  color: Theme.of(context).primaryColor,
                ),
              );
            } else if (state.status == AppExistStatus.installed) {
              return Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [boxShadow60],
                    color: isDark() ? cCardDarkColor : cIbratColor),
                width: MediaQuery.of(context).size.width,
                height: 300.h,
                padding: EdgeInsets.symmetric(vertical: 20.h),
                margin: EdgeInsets.all(20.w),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text(
                        state.date ?? LocaleKeys.date_gathering.tr(),
                        style: TextStyle(fontSize: 24.sp, color: cWhiteColor),
                        textAlign: TextAlign.center,
                      ),
                      Divider(
                        indent: 100.w,
                        endIndent: 100.w,
                        thickness: 2.h,
                        color: cWhiteColor,
                      ),
                      Flexible(
                        child: Text(
                          LocaleKeys.ibrat_far_time.tr(),
                          style: TextStyle(fontSize: 22.sp, color: cWhiteColor),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Text(
                        "${state.usageDuration} - ${LocaleKeys.minute.tr()}",
                        style: TextStyle(
                            fontSize: 30.sp,
                            color: cWhiteColor,
                            fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            } else if (state.status == AppExistStatus.not_installed) {
              return Container(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Spacer(
                      flex: 1,
                    ),
                    SvgPicture.asset(
                      Assets.iconsWarning,
                      width: 100.h,
                      height: 100.h,
                    ),
                    Padding(
                      padding: EdgeInsets.all(25.h),
                      child: Text(
                        LocaleKeys.ibrat_app_not_found.tr(),
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 20.sp),
                      ),
                    ),
                    MaterialButton(
                      padding: EdgeInsets.symmetric(
                          vertical: 10.h, horizontal: 20.w),
                      onPressed: () async {
                        launchCustomUrl(IBRAT_FARZANDLARI_LINK);
                      },
                      child: Text(
                        LocaleKeys.install_app.tr(),
                        style: TextStyle(
                            color: cWhiteColor,
                            fontSize: context.isTablet ? 12.sp : 16.sp),
                      ),
                      color: cFirstColor,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.r)),
                    ),
                    Spacer(
                      flex: 2,
                    )
                  ],
                ),
              );
            } else {
              return SizedBox();
            }
          },
        ));
  }

  String _printDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }
}
