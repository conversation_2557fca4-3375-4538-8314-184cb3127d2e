import 'package:isar/isar.dart';

part 'app_usage_model.g.dart';

@Name("AppUsageModel")
@collection
class AppUsageModel {
  AppUsageModel({
    this.date,
    this.time,
  });

  AppUsageModel.fromJson(dynamic json) {
    date = json['date'];
    time = json['time'];
  }

  Id localId = Isar.autoIncrement;
  String? date;
  int? time;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['date'] = date;
    map['time'] = time;
    return map;
  }

}
