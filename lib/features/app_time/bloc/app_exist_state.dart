part of 'app_exist_cubit.dart';

enum AppExistStatus { initial, installed, not_installed }

class AppExist extends Equatable {
  final AppExistStatus? status;
  final int? usageDuration;
  final String? date;

  AppExist(
      {required this.status, this.usageDuration, this.date});

  static AppExist initial() =>
      AppExist(status: AppExistStatus.initial, usageDuration: null);

  AppExist copyWith(
          {AppExistStatus? status,
          int? usageDuration,
          String? date}) =>
      AppExist(
          status: status ?? this.status,
          usageDuration: usageDuration ?? this.usageDuration,
          date: date);

  @override
  List<Object?> get props => [status, usageDuration,date];
}
