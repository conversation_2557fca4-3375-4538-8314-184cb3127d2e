import 'package:app_usage/app_usage.dart';
import 'package:appcheck/appcheck.dart';
import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'app_exist_state.dart';

class AppExistCubit extends Cubit<AppExist> {
  AppExistCubit() : super(AppExist(status: AppExistStatus.initial));
  SharedPreferences prefs = di();


  checkAppExist() async {
    try {
      DateTime now = DateTime.now();
      AppInfo? data = await AppCheck.checkAvailability(IBRAT_FARZANDLARI);
      if (data != null) {
        await AppUsage().getAppUsage(now.subtract(Duration(hours: 1)), now);
        int? usage_duration = prefs.getInt(USAGE_DURATION) ?? 0;
        String? usage_date =
            prefs.getString(USAGE_DATE) ?? LocaleKeys.date_gathering.tr();
        emit(state.copyWith(
            status: AppExistStatus.installed,
            usageDuration: usage_duration,
            date: usage_date));
      }
    } catch (e) {
      emit(state.copyWith(status: AppExistStatus.not_installed));
    }
  }
}
