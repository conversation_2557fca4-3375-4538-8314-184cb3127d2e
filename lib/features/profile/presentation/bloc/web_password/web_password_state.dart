part of 'web_password_cubit.dart';

enum WebPasswordStatus { initial, loading, failure, success }

class WebPasswordState extends Equatable {
  final WebPasswordStatus? status;
  final String? message;

  WebPasswordState({this.status, this.message});

  static WebPasswordState initial() =>
      WebPasswordState(status: WebPasswordStatus.initial, message: null);

  WebPasswordState copyWith({WebPasswordStatus? status, String? message}) =>
      WebPasswordState(status: status ?? this.status, message: message);

  @override
  List<Object?> get props => [status, message];
}
