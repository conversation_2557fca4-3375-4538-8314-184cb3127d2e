import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/password/presentation/bloc/pass_bloc.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class PinCodeChangePage extends StatefulWidget {
  const PinCodeChangePage({super.key});

  static Widget screen() => BlocProvider(
        create: (context) => di<PassBloc>()..add(IsSharedPinEmpty()),
        child: const PinCodeChangePage(),
      );

  @override
  State<PinCodeChangePage> createState() => _PinCodeChangePageState();
}

class _PinCodeChangePageState extends State<PinCodeChangePage> {
  bool isOldVisible = true;
  bool isNewVisible = true;
  bool isConfirmVisible = true;
  TextEditingController oldPassword = TextEditingController();
  TextEditingController newPassword = TextEditingController();
  TextEditingController confirmPassword = TextEditingController();
  var maskFormatter = MaskTextInputFormatter(mask: '####');
  late PassBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = BlocProvider.of<PassBloc>(context);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PassBloc, PassState>(
      builder: (context, state) {
        if (state is PassIsEmptyState) {
          return SingleChildScrollView(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
              child: Container(
                padding: EdgeInsets.only(
                  right: 20.w,
                  left: 20.w,
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topRight: Radius.circular(20.r),
                      topLeft: Radius.circular(20.r)),
                  color: Theme.of(context).cardTheme.color,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 8.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(Assets.iconsSheetPull),
                      ],
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    Text(
                      LocaleKeys.change_pin_code.tr(),
                      style: TextStyle(
                          fontSize: 20.sp,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontWeight: FontWeight.bold),
                    ),
                    SizedBox(
                      height: 6.h,
                    ),
                    Text(
                      LocaleKeys.enter_four_pin_code.tr(),
                      style: TextStyle(
                          fontSize: 16.sp,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                          fontWeight: FontWeight.normal),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    Visibility(
                      visible: !state.isEmpty,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocaleKeys.enter_old_pin_code.tr(),
                            style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.normal,
                                color: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.color),
                          ),
                          SizedBox(
                            height: 4.h,
                          ),
                          TextField(
                            obscureText: isOldVisible,
                            controller: oldPassword,
                            maxLength: 4,
                            decoration: InputDecoration(
                                enabledBorder:OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(30.r),
                                  borderSide: BorderSide(
                                    color: Theme.of(context).textTheme.bodySmall!.color!,
                                    width: 1,),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(30.r),
                                  borderSide: BorderSide(
                                    color: Theme.of(context).textTheme.bodySmall!.color!,
                                    width: 1,),
                                ),
                                prefixIcon: Container(
                                  padding: EdgeInsets.all(10.w),
                                  child: SvgPicture.asset(Assets.iconsLock2,
                                      colorFilter: ColorFilter.mode(
                                          Theme.of(context)
                                              .textTheme
                                              .bodyMedium!
                                              .color!,
                                          BlendMode.srcIn,
                                      ),width: 20.w,),
                                ),
                                suffixIcon: InkWell(
                                    onTap: () {
                                      setState(() {
                                        isOldVisible = !isOldVisible;
                                      });
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.only(right: 10.w),
                                      child: Icon(
                                        isOldVisible
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                        color: Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .color!,
                                        size:20.w,
                                      ),
                                    )),
                              counterStyle: TextStyle(color: Theme.of(context).textTheme.bodySmall!.color!)
                           ),
                            style: TextStyle(
                                color: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.color,),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    Text(
                      LocaleKeys.enter_new_pin_code.tr(),
                      style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.normal,
                          color: Theme.of(context).textTheme.bodyMedium?.color),
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    TextField(
                      obscureText: isNewVisible,
                      controller: newPassword,
                      maxLength: 4,
                      decoration: InputDecoration(
                          enabledBorder:OutlineInputBorder(
                            borderRadius: BorderRadius.circular(30.r),
                            borderSide: BorderSide(
                              color: Theme.of(context).textTheme.bodySmall!.color!,
                              width: 1,),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(30.r),
                            borderSide: BorderSide(
                              color: Theme.of(context).textTheme.bodySmall!.color!,
                              width: 1,),
                          ),
                          prefixIcon: Container(
                            padding: EdgeInsets.all(10.w),
                            child: SvgPicture.asset(Assets.iconsLock2,
                                colorFilter: ColorFilter.mode(
                                    Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .color!,
                                    BlendMode.srcIn),width: 20.w,),
                          ),
                          suffixIcon: InkWell(
                              onTap: () {
                                setState(() {
                                  isNewVisible = !isNewVisible;
                                });
                              },
                              child: Padding(
                                padding: EdgeInsets.only(right: 10.w),
                                child: Icon(
                                  isNewVisible
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .color!,
                                 size: 20.w,
                                ),
                              )),
                          counterStyle: TextStyle(color: Theme.of(context).textTheme.bodySmall!.color!)
                      ),
                      style: TextStyle(
                          color: Theme.of(context).textTheme.bodySmall?.color),


                    ),
                    Text(
                     LocaleKeys.re_enter.tr(),
                      style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.normal,
                          color: Theme.of(context).textTheme.bodyMedium?.color),
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    TextField(
                      obscureText: isConfirmVisible,
                      controller: confirmPassword,
                      maxLength: 4,
                      decoration: InputDecoration(
                        enabledBorder:OutlineInputBorder(
                          borderRadius: BorderRadius.circular(30.r),
                          borderSide: BorderSide(
                            color: Theme.of(context).textTheme.bodySmall!.color!,
                            width: 1,),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(30.r),
                          borderSide: BorderSide(
                            color: Theme.of(context).textTheme.bodySmall!.color!,
                            width: 1,),
                        ),
                        prefixIcon: Container(
                          padding: EdgeInsets.all(10.w),
                          child: SvgPicture.asset(
                            Assets.iconsLock2,
                            colorFilter: ColorFilter.mode(
                                Theme.of(context).textTheme.bodyMedium!.color!,
                                BlendMode.srcIn),
                            width: 20.w,
                          ),
                        ),
                        suffixIcon: InkWell(
                            onTap: () {
                              setState(() {
                                isConfirmVisible = !isConfirmVisible;
                              });
                            },
                            child: Padding(
                              padding: EdgeInsets.only(right: 10.w),
                              child: Icon(
                                isConfirmVisible
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .color!,
                                size: 20.w,
                              ),
                            )),
                          counterStyle: TextStyle(color: Theme.of(context).textTheme.bodySmall!.color!)

                      ),
                      style: TextStyle(
                          color: Theme.of(context).textTheme.bodySmall?.color,),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    MaterialButton(
                      height: 56.h,
                      minWidth: MediaQuery.of(context).size.width,
                      onPressed: () {
                        bloc.add(OnPressedEvent(
                          newPassword: newPassword,
                          confirmPassword: confirmPassword,
                          oldPassword: oldPassword,
                        ));
                        setState(() {});
                      },
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30.r),
                      ),
                      color: cFirstColor,
                      child: Text(
                        LocaleKeys.save.tr(),
                        style: TextStyle(color: cWhiteColor, fontSize: 18.sp),
                      ),
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    MaterialButton(
                      height: 56.h,
                      minWidth: MediaQuery.of(context).size.width,
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.r),
                          side: BorderSide(width: 1, color: Theme.of(context).textTheme.bodySmall!.color!)),
                      child: Text(
                        LocaleKeys.unnecessary.tr(),
                        style:
                            TextStyle(color: Theme.of(context).textTheme.bodySmall!.color!, fontSize: 18.sp),
                      ),
                    ),
                    SizedBox(
                      height: 10.h,
                    )
                  ],
                ),
              ),
            ),
          );
        }
        return Center(
          child: SizedBox(
              height: 310.h, child:  CupertinoActivityIndicator(color: Theme.of(context).primaryColor,)),
        );
      },
      listener: (BuildContext context, PassState state) {
        if (state is PassSuccessState) {
          return Navigator.pop(context);
        } else if (state is PassConfirmFailureState) {
          CustomToast.showToast(state.message);
          bloc.add(IsSharedPinEmpty());
        }
      },
    );
  }
}
