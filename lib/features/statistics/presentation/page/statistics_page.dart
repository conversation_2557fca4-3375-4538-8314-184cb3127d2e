import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/header_widget.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/statistics/presentation/bloc/statistic_cubit.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class StatisticsPage extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => di<StatisticCubit>(),
      child: StatisticsPage(),
    );
  }

  const StatisticsPage({super.key});

  @override
  State<StatisticsPage> createState() => _StatisticsPageState();
}

class _StatisticsPageState extends State<StatisticsPage> {
  late StatisticCubit cubit;

  @override
  void initState() {
    super.initState();
    cubit = BlocProvider.of<StatisticCubit>(context);
    cubit.getStatistics(false);
  }

  @override
  void dispose() {
    super.dispose();
    cubit.close();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeaderWidget(
        title: LocaleKeys.statistics.tr(),
        onBackTap: () {
          Navigator.pop(context);
        },
        isBackVisible: true,
      ),
      floatingActionButton: Padding(
        padding: EdgeInsets.all(15.h),
        child: SizedBox(
          height: 55.h,
          width: 55.h,
          child: FloatingActionButton(
            backgroundColor:
                Theme.of(context).floatingActionButtonTheme.backgroundColor,
            onPressed: () {
              BlocProvider.of<StatisticCubit>(context).getStatistics(true);
            },
            child: Icon(Icons.refresh, size: 22.h),
          ),
        ),
      ),
      body: BlocConsumer<StatisticCubit, StatisticState>(
        listener: (context, state) {},
        builder: (context, state) {
          if (state.status == StatisticStatus.initial) {
            return Center(
              child: CupertinoActivityIndicator(
                color: cFirstColor,
                radius: 30.r,
              ),
            );
          } else if (state.status == StatisticStatus.loading) {
            return Center(
              child: CupertinoActivityIndicator(
                color: cFirstColor,
                radius: 30.r,
              ),
            );
          } else if (state.status == StatisticStatus.success) {
            ///Works
            String newWork = state.statistic?.newWorks.toString() ?? "0";
            String sentWork =
                state.statistic?.statistics?.works?.sending.toString() ?? "0";
            String webEditWork =
                state.statistic?.statistics?.works?.webEdit.toString() ?? "0";

            ///Statistics
            double percentage = 0;
            int totalWork =
                state.statistic?.statistics?.statistic?.totalPlan ?? 0;
            int doneWork = state.statistic?.statistics?.statistic?.success ?? 0;
            int remainWork = totalWork - doneWork;
            if (totalWork != 0) {
              percentage = (doneWork / totalWork) * 100;
            }

            ///Rating
            String republic =
                state.statistic?.statistics?.reting?.republic.toString() ?? "0";
            String region =
                state.statistic?.statistics?.reting?.region.toString() ?? "0";
            String province =
                state.statistic?.statistics?.reting?.province.toString() ?? "0";

            return RefreshIndicator(
              onRefresh: () async {
                BlocProvider.of<StatisticCubit>(context).getStatistics(true);
              },
              child: SingleChildScrollView(
                padding: EdgeInsets.only(bottom: 100.h),
                physics: BouncingScrollPhysics(),
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  padding:
                      EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
                  child: Column(
                    children: [
                      SizedBox(
                        height: 20.h,
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        padding: EdgeInsets.symmetric(vertical: 20.h),
                        decoration: BoxDecoration(
                            boxShadow: [
                              boxShadow60,
                            ],
                            color: Theme.of(context).cardTheme.color,
                            borderRadius: BorderRadius.circular(20.r)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              LocaleKeys.works.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.w600),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 20.h),
                            Container(
                              height: 120.h,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Column(
                                      children: [
                                        Text(
                                          newWork,
                                          style: TextStyle(
                                              fontSize: 32.sp,
                                              color: cFirstColor),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 4.w),
                                          child: Text(
                                            LocaleKeys.new_word.tr(),
                                            style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cFirstColor),
                                            textAlign: TextAlign.center,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  SvgPicture.asset(
                                    Assets.iconsVerticalDivider,
                                    height: 70.h,
                                  ),
                                  Expanded(
                                    child: Column(
                                      children: [
                                        Text(
                                          sentWork,
                                          style: TextStyle(
                                              fontSize: 32.sp,
                                              color: cGreenColor),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 4.w),
                                          child: Text(LocaleKeys.sent.tr(),
                                              style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: cGreenColor),
                                              textAlign: TextAlign.center),
                                        )
                                      ],
                                    ),
                                  ),
                                  SvgPicture.asset(
                                    Assets.iconsVerticalDivider,
                                    height: 70.h,
                                  ),
                                  Expanded(
                                    child: Column(
                                      children: [
                                        Text(
                                          webEditWork,
                                          style: TextStyle(
                                              fontSize: 32.sp,
                                              color: cDarkYellowColor),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 4.w),
                                          child: Text(
                                            LocaleKeys.inEdit.tr(),
                                            style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cDarkYellowColor),
                                            textAlign: TextAlign.center,
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 2,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      Container(
                        height: 380.h,
                        width: MediaQuery.of(context).size.width,
                        padding: EdgeInsets.symmetric(
                            vertical: 20.h, horizontal: 24.w),
                        decoration: BoxDecoration(
                            boxShadow: [
                              boxShadow60,
                            ],
                            color: Theme.of(context).cardTheme.color,
                            borderRadius: BorderRadius.circular(20.r)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              LocaleKeys.performance_indicators.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.w600),
                            ),
                            SizedBox(height: 30.h),
                            Expanded(
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Text(totalWork.toString(),
                                            style: TextStyle(
                                                fontSize: 32.sp,
                                                color: cFirstColor),
                                            textAlign: TextAlign.center),
                                        Text(
                                          LocaleKeys.all_plan_work.tr(),
                                          style: TextStyle(
                                              fontSize: 16.sp,
                                              color: cFirstColor),
                                          textAlign: TextAlign.center,
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 2,
                                        )
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Text(
                                          doneWork.toString(),
                                          style: TextStyle(
                                              fontSize: 32.sp,
                                              color: cGreenColor),
                                        ),
                                        Text(
                                          LocaleKeys.done_work.tr(),
                                          style: TextStyle(
                                            fontSize: 16.sp,
                                            color: cGreenColor,
                                          ),
                                          textAlign: TextAlign.center,
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 2,
                                        )
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 20.h,
                            ),
                            Expanded(
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Text(
                                          remainWork.toString(),
                                          style: TextStyle(
                                              fontSize: 32.sp,
                                              color: cDarkYellowColor),
                                        ),
                                        Text(
                                          LocaleKeys.need_done_work.tr(),
                                          style: TextStyle(
                                              fontSize: 16.sp,
                                              color: cDarkYellowColor),
                                          textAlign: TextAlign.center,
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 2,
                                        )
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Text(
                                          "${percentage.toStringAsFixed(1)}%",
                                          style: TextStyle(
                                              fontSize: 32.sp,
                                              color: cFirstColor),
                                        ),
                                        Text(
                                          LocaleKeys.percentage_indicator.tr(),
                                          style: TextStyle(
                                              fontSize: 16.sp,
                                              color: cFirstColor),
                                          textAlign: TextAlign.center,
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 2,
                                        )
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        padding: EdgeInsets.symmetric(vertical: 20.h),
                        decoration: BoxDecoration(
                            boxShadow: [
                              boxShadow60,
                            ],
                            color: Theme.of(context).cardTheme.color,
                            borderRadius: BorderRadius.circular(20.r)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              LocaleKeys.rating.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.w600),
                            ),
                            SizedBox(height: 30.h),
                            Container(
                              height: 120.h,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Text(
                                          republic,
                                          style: TextStyle(
                                              fontSize: 32.sp,
                                              color: cFirstColor),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 4.w),
                                          child: Text(
                                            LocaleKeys.republic_indicator.tr(),
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                fontSize: 16.sp,
                                                color: cFirstColor),
                                            maxLines: 2,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  SvgPicture.asset(Assets.iconsVerticalDivider),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Text(
                                          province,
                                          style: TextStyle(
                                              fontSize: 32.sp,
                                              color: cFirstColor),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 4.w),
                                          child: Text(
                                              LocaleKeys.province_indicator
                                                  .tr(),
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: cFirstColor)),
                                        )
                                      ],
                                    ),
                                  ),
                                  SvgPicture.asset(Assets.iconsVerticalDivider),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Text(
                                          region,
                                          style: TextStyle(
                                              fontSize: 32.sp,
                                              color: cFirstColor),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 4.w),
                                          child: Text(
                                              LocaleKeys.city_indicator.tr(),
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                  fontSize: 16.sp,
                                                  color: cFirstColor)),
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          } else if (state.status == StatisticStatus.failure) {
            return Container(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ClipRect(
                      child: Container(
                        height: 300.h,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 20.h,
                            ),
                            Expanded(
                                child: Image.asset(
                              Assets.imagesNoConnection,
                              height: 140.h,
                            )),
                            Padding(
                                padding: EdgeInsets.only(
                                    top: 10.h,
                                    left: 30.w,
                                    right: 30.w,
                                    bottom: 10.h),
                                child: Text(
                                  state.message.toString(),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: cGrayColor1),
                                )),
                            CupertinoButton(
                                child: Text(
                                  LocaleKeys.refresh.tr(),
                                  style: TextStyle(color: cGrayColor1),
                                ),
                                color: cGrayColor1.withAlpha(80),
                                onPressed: () {
                                  BlocProvider.of<StatisticCubit>(context)
                                      .getStatistics(true);
                                }),
                            SizedBox(
                              height: 20.h,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return SizedBox();
          }
        },
      ),
    );
  }
}
