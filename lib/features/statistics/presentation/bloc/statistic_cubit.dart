import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/features/statistics/data/model/statistics.dart';
import 'package:yetakchi/features/statistics/data/model/statistics_model.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'statistic_state.dart';

class StatisticCubit extends Cubit<StatisticState> {
  final Dio dio;
  final NetworkInfo networkInfo;
  final SharedPreferences prefs;
  final IsarService isarService;

  StatisticCubit({
    required this.dio,
    required this.networkInfo,
    required this.prefs,
    required this.isarService,
  }) : super(StatisticState(status: StatisticStatus.initial));

  void getStatistics(bool refresh) async {
    if (await networkInfo.isConnected) {
      if (refresh) {
        try {
          emit(StatisticState(status: StatisticStatus.loading));
          var response = await dio.get(statisticsPath);
          Statistics statistics = Statistics.fromJson(response.data);
          StatisticsModel statisticsModel =
              StatisticsModel(statistics: statistics, newWorks: 0);
          if (response.statusCode == 200) {
            emit(StatisticState(
                status: StatisticStatus.success, statistic: statisticsModel));
            await isarService.isar.writeTxn(() async {
              isarService.isar.statistics.clear();
              isarService.isar.statistics.put(statistics);
            });
          } else {
            Statistics? statistics =
                await isarService.isar.statistics.where().findFirst();
            StatisticsModel statisticsModel =
                StatisticsModel(statistics: statistics, newWorks: 0);
            emit(StatisticState(
                status: StatisticStatus.success, statistic: statisticsModel));
          }
        } catch (e) {
          print(e);
          emit(StatisticState(
              status: StatisticStatus.failure,
              message: LocaleKeys.error_download_data.tr()));
        }
      } else {
        Statistics? statistics =
            await isarService.isar.statistics.where().findFirst();
        StatisticsModel statisticsModel =
            StatisticsModel(statistics: statistics, newWorks: 0);
        emit(StatisticState(
            status: StatisticStatus.success, statistic: statisticsModel));
      }
    } else {
      try {
        Statistics? statistics =
            await isarService.isar.statistics.where().findFirst();
        StatisticsModel statisticsModel =
            StatisticsModel(statistics: statistics, newWorks: 0);
        emit(StatisticState(
            status: StatisticStatus.success, statistic: statisticsModel));
      } catch (e) {
        emit(StatisticState(
            status: StatisticStatus.failure,
            message: LocaleKeys.check_internet_connection.tr()));
      }
    }
  }
}
