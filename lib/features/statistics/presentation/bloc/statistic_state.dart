part of 'statistic_cubit.dart';

enum StatisticStatus { initial, loading, failure, success }

class StatisticState extends Equatable {
  final StatisticStatus? status;
  final String? message;
  final StatisticsModel? statistic;

  StatisticState({required this.status, this.message, this.statistic});

  static StatisticState initial() =>
      StatisticState(status: StatisticStatus.initial, message: null);

  StatisticState copyWith({StatisticStatus? status, String? message}) =>
      StatisticState(status: status ?? this.status, message: message);

  @override
  List<Object?> get props => [status, message, statistic];
}
