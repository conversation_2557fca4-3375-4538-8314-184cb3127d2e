import 'package:isar/isar.dart';

part 'statistics.g.dart';

@collection
@Name("statistic")
class Statistics {
  Statistics({
    this.works,
    this.statistic,
    this.reting,
  });

  Statistics.fromJson(dynamic json) {
    works = json['works'] != null ? Works.fromJson(json['works']) : null;
    statistic = json['statistic'] != null
        ? Statistic.fromJson(json['statistic'])
        : null;
    reting = json['reting'] != null ? Reting.fromJson(json['reting']) : null;
  }

  Id localId = Isar.autoIncrement;
  Works? works;
  Statistic? statistic;
  Reting? reting;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (works != null) {
      map['works'] = works?.toJson();
    }
    if (statistic != null) {
      map['statistic'] = statistic?.toJson();
    }
    if (reting != null) {
      map['reting'] = reting?.toJson();
    }
    return map;
  }
}

@Embedded()
class Reting {
  Reting({
    this.republic,
    this.province,
    this.region,
  });

  Reting.fromJson(dynamic json) {
    republic = json['republic'];
    province = json['province'];
    region = json['region'];
  }

  int? republic;
  int? province;
  int? region;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['republic'] = republic;
    map['province'] = province;
    map['region'] = region;
    return map;
  }
}

@Embedded()
class Statistic {
  Statistic({
    this.totalPlan,
    this.success,
  });

  Statistic.fromJson(dynamic json) {
    totalPlan = json['totalPlan'];
    success = json['success'];
  }

  int? totalPlan;
  int? success;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['totalPlan'] = totalPlan;
    map['success'] = success;
    return map;
  }
}

@Embedded()
class Works {
  Works({
    this.sending,
    this.webEdit,
  });

  Works.fromJson(dynamic json) {
    sending = json['sending'];
    webEdit = json['webEdit'];
  }

  int? sending;
  int? webEdit;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['sending'] = sending;
    map['webEdit'] = webEdit;
    return map;
  }
}
