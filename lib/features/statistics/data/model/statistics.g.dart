// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statistics.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetStatisticsCollection on Isar {
  IsarCollection<Statistics> get statistics => this.collection();
}

const StatisticsSchema = CollectionSchema(
  name: r'statistic',
  id: 5204530403979115219,
  properties: {
    r'reting': PropertySchema(
      id: 0,
      name: r'reting',
      type: IsarType.object,
      target: r'Reting',
    ),
    r'statistic': PropertySchema(
      id: 1,
      name: r'statistic',
      type: IsarType.object,
      target: r'Statistic',
    ),
    r'works': PropertySchema(
      id: 2,
      name: r'works',
      type: IsarType.object,
      target: r'Works',
    )
  },
  estimateSize: _statisticsEstimateSize,
  serialize: _statisticsSerialize,
  deserialize: _statisticsDeserialize,
  deserializeProp: _statisticsDeserializeProp,
  idName: r'localId',
  indexes: {},
  links: {},
  embeddedSchemas: {
    r'Works': WorksSchema,
    r'Statistic': StatisticSchema,
    r'Reting': RetingSchema
  },
  getId: _statisticsGetId,
  getLinks: _statisticsGetLinks,
  attach: _statisticsAttach,
  version: '3.1.0+1',
);

int _statisticsEstimateSize(
  Statistics object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.reting;
    if (value != null) {
      bytesCount +=
          3 + RetingSchema.estimateSize(value, allOffsets[Reting]!, allOffsets);
    }
  }
  {
    final value = object.statistic;
    if (value != null) {
      bytesCount += 3 +
          StatisticSchema.estimateSize(
              value, allOffsets[Statistic]!, allOffsets);
    }
  }
  {
    final value = object.works;
    if (value != null) {
      bytesCount +=
          3 + WorksSchema.estimateSize(value, allOffsets[Works]!, allOffsets);
    }
  }
  return bytesCount;
}

void _statisticsSerialize(
  Statistics object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeObject<Reting>(
    offsets[0],
    allOffsets,
    RetingSchema.serialize,
    object.reting,
  );
  writer.writeObject<Statistic>(
    offsets[1],
    allOffsets,
    StatisticSchema.serialize,
    object.statistic,
  );
  writer.writeObject<Works>(
    offsets[2],
    allOffsets,
    WorksSchema.serialize,
    object.works,
  );
}

Statistics _statisticsDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = Statistics(
    reting: reader.readObjectOrNull<Reting>(
      offsets[0],
      RetingSchema.deserialize,
      allOffsets,
    ),
    statistic: reader.readObjectOrNull<Statistic>(
      offsets[1],
      StatisticSchema.deserialize,
      allOffsets,
    ),
    works: reader.readObjectOrNull<Works>(
      offsets[2],
      WorksSchema.deserialize,
      allOffsets,
    ),
  );
  object.localId = id;
  return object;
}

P _statisticsDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readObjectOrNull<Reting>(
        offset,
        RetingSchema.deserialize,
        allOffsets,
      )) as P;
    case 1:
      return (reader.readObjectOrNull<Statistic>(
        offset,
        StatisticSchema.deserialize,
        allOffsets,
      )) as P;
    case 2:
      return (reader.readObjectOrNull<Works>(
        offset,
        WorksSchema.deserialize,
        allOffsets,
      )) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _statisticsGetId(Statistics object) {
  return object.localId;
}

List<IsarLinkBase<dynamic>> _statisticsGetLinks(Statistics object) {
  return [];
}

void _statisticsAttach(IsarCollection<dynamic> col, Id id, Statistics object) {
  object.localId = id;
}

extension StatisticsQueryWhereSort
    on QueryBuilder<Statistics, Statistics, QWhere> {
  QueryBuilder<Statistics, Statistics, QAfterWhere> anyLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension StatisticsQueryWhere
    on QueryBuilder<Statistics, Statistics, QWhereClause> {
  QueryBuilder<Statistics, Statistics, QAfterWhereClause> localIdEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localId,
        upper: localId,
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterWhereClause> localIdNotEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterWhereClause> localIdGreaterThan(
      Id localId,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localId, includeLower: include),
      );
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterWhereClause> localIdLessThan(
      Id localId,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localId, includeUpper: include),
      );
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterWhereClause> localIdBetween(
    Id lowerLocalId,
    Id upperLocalId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalId,
        includeLower: includeLower,
        upper: upperLocalId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension StatisticsQueryFilter
    on QueryBuilder<Statistics, Statistics, QFilterCondition> {
  QueryBuilder<Statistics, Statistics, QAfterFilterCondition> localIdEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition>
      localIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition> localIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition> localIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition> retingIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reting',
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition>
      retingIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reting',
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition>
      statisticIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'statistic',
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition>
      statisticIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'statistic',
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition> worksIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'works',
      ));
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition> worksIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'works',
      ));
    });
  }
}

extension StatisticsQueryObject
    on QueryBuilder<Statistics, Statistics, QFilterCondition> {
  QueryBuilder<Statistics, Statistics, QAfterFilterCondition> reting(
      FilterQuery<Reting> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'reting');
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition> statistic(
      FilterQuery<Statistic> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'statistic');
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterFilterCondition> works(
      FilterQuery<Works> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'works');
    });
  }
}

extension StatisticsQueryLinks
    on QueryBuilder<Statistics, Statistics, QFilterCondition> {}

extension StatisticsQuerySortBy
    on QueryBuilder<Statistics, Statistics, QSortBy> {}

extension StatisticsQuerySortThenBy
    on QueryBuilder<Statistics, Statistics, QSortThenBy> {
  QueryBuilder<Statistics, Statistics, QAfterSortBy> thenByLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.asc);
    });
  }

  QueryBuilder<Statistics, Statistics, QAfterSortBy> thenByLocalIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.desc);
    });
  }
}

extension StatisticsQueryWhereDistinct
    on QueryBuilder<Statistics, Statistics, QDistinct> {}

extension StatisticsQueryProperty
    on QueryBuilder<Statistics, Statistics, QQueryProperty> {
  QueryBuilder<Statistics, int, QQueryOperations> localIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localId');
    });
  }

  QueryBuilder<Statistics, Reting?, QQueryOperations> retingProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reting');
    });
  }

  QueryBuilder<Statistics, Statistic?, QQueryOperations> statisticProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'statistic');
    });
  }

  QueryBuilder<Statistics, Works?, QQueryOperations> worksProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'works');
    });
  }
}

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const RetingSchema = Schema(
  name: r'Reting',
  id: 5170504041949165657,
  properties: {
    r'province': PropertySchema(
      id: 0,
      name: r'province',
      type: IsarType.long,
    ),
    r'region': PropertySchema(
      id: 1,
      name: r'region',
      type: IsarType.long,
    ),
    r'republic': PropertySchema(
      id: 2,
      name: r'republic',
      type: IsarType.long,
    )
  },
  estimateSize: _retingEstimateSize,
  serialize: _retingSerialize,
  deserialize: _retingDeserialize,
  deserializeProp: _retingDeserializeProp,
);

int _retingEstimateSize(
  Reting object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _retingSerialize(
  Reting object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.province);
  writer.writeLong(offsets[1], object.region);
  writer.writeLong(offsets[2], object.republic);
}

Reting _retingDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = Reting(
    province: reader.readLongOrNull(offsets[0]),
    region: reader.readLongOrNull(offsets[1]),
    republic: reader.readLongOrNull(offsets[2]),
  );
  return object;
}

P _retingDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension RetingQueryFilter on QueryBuilder<Reting, Reting, QFilterCondition> {
  QueryBuilder<Reting, Reting, QAfterFilterCondition> provinceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'province',
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> provinceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'province',
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> provinceEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'province',
        value: value,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> provinceGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'province',
        value: value,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> provinceLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'province',
        value: value,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> provinceBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'province',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> regionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'region',
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> regionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'region',
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> regionEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'region',
        value: value,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> regionGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'region',
        value: value,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> regionLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'region',
        value: value,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> regionBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'region',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> republicIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'republic',
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> republicIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'republic',
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> republicEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'republic',
        value: value,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> republicGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'republic',
        value: value,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> republicLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'republic',
        value: value,
      ));
    });
  }

  QueryBuilder<Reting, Reting, QAfterFilterCondition> republicBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'republic',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension RetingQueryObject on QueryBuilder<Reting, Reting, QFilterCondition> {}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const StatisticSchema = Schema(
  name: r'Statistic',
  id: -2618935922336680102,
  properties: {
    r'success': PropertySchema(
      id: 0,
      name: r'success',
      type: IsarType.long,
    ),
    r'totalPlan': PropertySchema(
      id: 1,
      name: r'totalPlan',
      type: IsarType.long,
    )
  },
  estimateSize: _statisticEstimateSize,
  serialize: _statisticSerialize,
  deserialize: _statisticDeserialize,
  deserializeProp: _statisticDeserializeProp,
);

int _statisticEstimateSize(
  Statistic object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _statisticSerialize(
  Statistic object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.success);
  writer.writeLong(offsets[1], object.totalPlan);
}

Statistic _statisticDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = Statistic(
    success: reader.readLongOrNull(offsets[0]),
    totalPlan: reader.readLongOrNull(offsets[1]),
  );
  return object;
}

P _statisticDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension StatisticQueryFilter
    on QueryBuilder<Statistic, Statistic, QFilterCondition> {
  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> successIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'success',
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> successIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'success',
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> successEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'success',
        value: value,
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> successGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'success',
        value: value,
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> successLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'success',
        value: value,
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> successBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'success',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> totalPlanIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalPlan',
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition>
      totalPlanIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalPlan',
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> totalPlanEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalPlan',
        value: value,
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition>
      totalPlanGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalPlan',
        value: value,
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> totalPlanLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalPlan',
        value: value,
      ));
    });
  }

  QueryBuilder<Statistic, Statistic, QAfterFilterCondition> totalPlanBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalPlan',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension StatisticQueryObject
    on QueryBuilder<Statistic, Statistic, QFilterCondition> {}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const WorksSchema = Schema(
  name: r'Works',
  id: 3358047907052474960,
  properties: {
    r'sending': PropertySchema(
      id: 0,
      name: r'sending',
      type: IsarType.long,
    ),
    r'webEdit': PropertySchema(
      id: 1,
      name: r'webEdit',
      type: IsarType.long,
    )
  },
  estimateSize: _worksEstimateSize,
  serialize: _worksSerialize,
  deserialize: _worksDeserialize,
  deserializeProp: _worksDeserializeProp,
);

int _worksEstimateSize(
  Works object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _worksSerialize(
  Works object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.sending);
  writer.writeLong(offsets[1], object.webEdit);
}

Works _worksDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = Works(
    sending: reader.readLongOrNull(offsets[0]),
    webEdit: reader.readLongOrNull(offsets[1]),
  );
  return object;
}

P _worksDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension WorksQueryFilter on QueryBuilder<Works, Works, QFilterCondition> {
  QueryBuilder<Works, Works, QAfterFilterCondition> sendingIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sending',
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> sendingIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sending',
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> sendingEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sending',
        value: value,
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> sendingGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sending',
        value: value,
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> sendingLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sending',
        value: value,
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> sendingBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sending',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> webEditIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'webEdit',
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> webEditIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'webEdit',
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> webEditEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'webEdit',
        value: value,
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> webEditGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'webEdit',
        value: value,
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> webEditLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'webEdit',
        value: value,
      ));
    });
  }

  QueryBuilder<Works, Works, QAfterFilterCondition> webEditBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'webEdit',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorksQueryObject on QueryBuilder<Works, Works, QFilterCondition> {}
