import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:facesdk_plugin/facedetection_interface.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'package:get/get.dart' hide MultipartFile, FormData hide Trans;
import 'package:http_parser/http_parser.dart';
import 'package:lottie/lottie.dart';
import 'package:native_device_orientation/native_device_orientation.dart';
import 'package:pausable_timer/pausable_timer.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:facesdk_plugin/facesdk_plugin.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/face_control/bio_lock_page.dart';
import 'package:yetakchi/features/lock/lock_switcher.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'person.dart';

// ignore: must_be_immutable
class FaceRecognitionView extends StatefulWidget {
  List<Person>? personList;
  FaceDetectionViewController? faceDetectionViewController;
  Function()? afterRecognized;

  Widget? child;
  final bool server_approved;
  final bool? debug;

  FaceRecognitionView(
      {super.key,
      this.personList,
      this.child,
      this.server_approved = true,
      this.afterRecognized,
      this.debug = false});

  @override
  State<StatefulWidget> createState() => FaceRecognitionViewState();
}

class FaceRecognitionViewState extends State<FaceRecognitionView> {
  dynamic _faces;
  double _livenessThreshold = 0;
  double _identifyThreshold = 0;
  bool _recognized = false;
  String _identifiedName = "";
  String _identifiedSimilarity = "";
  String _identifiedLiveness = "";
  String _identifiedYaw = "";
  String _identifiedRoll = "";
  String _identifiedPitch = "";
  bool invalidHolding = false;
  late PausableTimer timer;
  final StreamController<NativeDeviceOrientation> _orientationStreamController =
      StreamController<NativeDeviceOrientation>();
  late final StreamSubscription streamSubscription;
  var communicator = NativeDeviceOrientationCommunicator();
  final ValueNotifier<NativeDeviceOrientation> invalidNotifier =
      ValueNotifier<NativeDeviceOrientation>(NativeDeviceOrientation.unknown);

  ///For UI
  double _similarity = 0.0;
  double _liveness = 0.0;
  late final AppLifecycleListener _listener;
  bool _shouldRequestPermission = false;

  // ignore: prefer_typing_uninitialized_variables
  var _identifiedFace;

  // ignore: prefer_typing_uninitialized_variables
  var _enrolledFace;
  final _faceSdkPlugin = FacesdkPlugin();
  FaceDetectionViewController? faceDetectionViewController;
  SharedPreferences prefs = di();

  ///Cannot request due plugin request
  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.camera,
    ].request();

    return statuses;
  }

  // Function to check camera permission status
  Future<void> _checkCameraPermissionStatus() async {
    var status = await Permission.camera.status;
    if (status.isGranted) {
      print('Camera permission is granted');
    } else if (status.isDenied) {
      print('Camera permission is denied');
    } else if (status.isPermanentlyDenied) {
      print('Camera permission is permanently denied');
    } else if (status.isRestricted) {
      print('Camera permission is restricted');
    } else if (status.isLimited) {
      print('Camera permission is limited');
    }
  }

  Future<void> permissionWall() async {
    _checkCameraPermissionStatus();

    var status = await Permission.camera.status;

    if (status != PermissionStatus.granted) {
      if (status == PermissionStatus.permanentlyDenied) {
        print(status);

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog(context);
      } else {
        showCustomDialog(context);
      }
    } else {
      /// If everything is granted, let it continue
    }
  }

  // initInvalidNotifier() async {
  //   invalidNotifier.value = await communicator.orientation(useSensor: true);
  // }

  @override
  void initState() {
    startListeningToOrientationStream(useSensor: true);
    permissionWall();
    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );

    if (widget.debug ?? false) {
      if (widget.afterRecognized != null) {
        widget.afterRecognized!();
      }
      WidgetsBinding.instance.addPostFrameCallback((time) {
        Get.off(widget.child);
      });
    }
    loadSettings();
    initializeTimer();
    super.initState();
  }

  void startListeningToOrientationStream({bool useSensor = false}) {
    // Your logic to start listening to the orientation stream
    // For example, using the native_device_orientation package

    streamSubscription = communicator
        .onOrientationChanged(useSensor: useSensor)
        .listen((orientation) {
      // Add the orientation to the stream
      print(orientation);
      invalidNotifier.value = orientation;
      if (orientation == NativeDeviceOrientation.portraitUp ||
          orientation == NativeDeviceOrientation.portraitDown) {
        setState(() {
          invalidHolding = false;
        });
      } else {
        setState(() {
          invalidHolding = true;
        });
      }
      if (!_orientationStreamController.isClosed)
        _orientationStreamController.add(orientation);
    });
  }

  @override
  void dispose() {
    _listener.dispose();
    streamSubscription.cancel();
    _orientationStreamController.close();
    faceDetectionViewController?.stopCamera();
    super.dispose();
  }

  void _onDetached() => print('detached');

  void _onResumed() {
    if (_shouldRequestPermission) {
      permissionWall();
      _shouldRequestPermission = false;
    }
  }

  void _onInactive() => print('inactive');

  void _onHidden() => print('hidden');

  void _onPaused() async {
    _shouldRequestPermission = true;
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  Future<void> initFaceDetect() async {
    int facePluginState = -1;

    try {
      if (Platform.isAndroid) {
        await _faceSdkPlugin
            .setActivation(AppStrings.androidFaceToken)
            .then((value) => facePluginState = value ?? -1);
      } else {
        await _faceSdkPlugin
            .setActivation(AppStrings.iOSFaceToken)
            .then((value) => facePluginState = value ?? -1);
      }

      if (facePluginState == 0) {
        await _faceSdkPlugin
            .init()
            .then((value) => facePluginState = value ?? -1);
      }
    } catch (e) {}

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;
  }

  Future<void> loadSettings() async {
    if (widget.personList == null) {
      widget.personList = await loadAllPersons();
      initFaceDetect();
    }

    final prefs = await SharedPreferences.getInstance();
    String? livenessThreshold = prefs.getString("liveness_threshold");
    String? identifyThreshold = prefs.getString("identify_threshold");
    setState(() {
      _livenessThreshold = double.parse(livenessThreshold ?? MAX_LIVENESS);
      _identifyThreshold = double.parse(identifyThreshold ?? MAX_IDENTIFY);
    });
  }

  Future<void> faceRecognitionStart() async {
    var cameraLens = prefs.getInt("camera_lens");

    setState(() {
      _faces = null;
      _recognized = false;
    });

    await faceDetectionViewController?.startCamera(cameraLens ?? 1);
  }

  Future<bool> onFaceDetected(faces) async {
    if (_recognized == true) {
      return false;
    }

    if (!mounted) return false;

    setState(() {
      _faces = faces;
    });

    bool recognized = false;
    double maxSimilarity = -1;
    String maxSimilarityName = "";
    double maxLiveness = -1;
    double maxYaw = -1;
    double maxRoll = -1;
    double maxPitch = -1;
    // ignore: prefer_typing_uninitialized_variables
    var enrolledFace, identifedFace;
    if (faces.length > 0) {
      var face = faces[0];
      for (var person in widget.personList ?? []) {
        double similarity = await _faceSdkPlugin.similarityCalculation(
                face['templates'], person.templates) ??
            -1;

        if (!mounted) return false;
        setState(() {
          ///For UI
          _similarity = similarity;
          _liveness = face['liveness'];
        });

        if (maxSimilarity < similarity) {
          maxSimilarity = similarity;
          maxSimilarityName = person.name;
          maxLiveness = face['liveness'];
          maxYaw = face['yaw'];
          maxRoll = face['roll'];
          maxPitch = face['pitch'];
          identifedFace = face['faceJpg'];
          enrolledFace = person.faceJpg;
        }
      }

      if (maxSimilarity > _identifyThreshold &&
          maxLiveness > _livenessThreshold) {
        recognized = true;
      }
    }

    Future.delayed(const Duration(milliseconds: 100), () async {
      if (!mounted) return false;
      setState(() {
        _recognized = recognized;
        _identifiedName = maxSimilarityName;
        _identifiedSimilarity = maxSimilarity.toString();
        _identifiedLiveness = maxLiveness.toString();
        _identifiedYaw = maxYaw.toString();
        _identifiedRoll = maxRoll.toString();
        _identifiedPitch = maxPitch.toString();
        _enrolledFace = enrolledFace;
        _identifiedFace = identifedFace;
      });
      if (recognized) {
        // faceDetectionViewController?.stopCamera();

        timer.start();

        if (widget.afterRecognized != null) {
          widget.afterRecognized!();
        }
        setState(() {
          _faces = null;
        });

        /// Here is the FaceControl navigation logics

        bool canVibrate = await Vibrate.canVibrate;
        if (canVibrate) {
          if (Platform.isIOS)
            HapticFeedback.lightImpact();
          else {
            var _type = FeedbackType.success;
            Vibrate.feedback(_type);
          }
        }
      }
    });

    return recognized;
  }

  Future<bool> sendApprovedFace(dynamic enrolledFace) async {
    try {
      Dio dio = di();
      String userId = prefs.getString('id') ?? '';

      MultipartFile file = await MultipartFile.fromBytes(
        enrolledFace,
        filename: _identifiedName,
        contentType: MediaType('image', 'jpeg'),
      );
      var data = {'avatar': file};

      FormData formData;
      formData = FormData.fromMap(data);
      print(formData.fields);

      Options options = Options(
        receiveDataWhenStatusError: true,
        headers: {
          "Content-Type": "multipart/form-data",
          "Accept": "application/json",
        },
      );

      final response = await dio.put(
        usersImagePath + userId,
        data: formData,
        options: options,
      );

      print("=== Uploaded: $response");
      if (response.statusCode == 200) {
        print('Success: 200!');
        return true;
      } else {
        CustomToast.showToast(LocaleKeys.error_in_uploading.tr());
        return false;
      }
    } on DioException catch (e) {
      CustomToast.showToast(LocaleKeys.error_in_uploading.tr());
      print("=== Can' upload: $e");

      return false;
    } catch (e) {
      CustomToast.showToast(LocaleKeys.error_in_uploading.tr());
      print("=== Can' upload: $e");
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _orientationStreamController.close();
        faceDetectionViewController?.stopCamera();
        CustomToast.showToast(LocaleKeys.leader_not_confirm.tr());
        timer.cancel();
        return true;
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: cFirstColor,
          title: Text('Face Control',
              style: TextStyle(
                  fontSize: context.isTablet ? 16.sp : 20.sp,
                  color: cWhiteColor)),
          toolbarHeight: 70.h,
          centerTitle: true,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20.r),
              bottomRight: Radius.circular(20.r),
            ),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
        floatingActionButton: !_recognized
            ? Padding(
                padding: EdgeInsets.only(bottom: 100.h),
                child: SizedBox(
                  height: 55.h,
                  width: 55.h,
                  child: FloatingActionButton(
                    backgroundColor: cSecondColor,
                    onPressed: () async {
                      _orientationStreamController.close();
                      faceDetectionViewController?.stopCamera();
                      Get.back();
                      timer.cancel();
                      CustomToast.showToast(LocaleKeys.leader_not_confirm.tr());
                    },
                    child: SvgPicture.asset(
                      Assets.iconsBackArrowCircle,
                      height: 40.h,
                      width: 40.h,
                    ),
                  ),
                ),
              )
            : null,
        body: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            ValueListenableBuilder<NativeDeviceOrientation>(
                valueListenable: invalidNotifier,
                builder: (context, value, _) {
                  return Visibility(
                      visible: widget.debug == false && !(invalidHolding),
                      child: FaceDetectionView(faceRecognitionViewState: this));
                }),
            ValueListenableBuilder<NativeDeviceOrientation>(
                valueListenable: invalidNotifier,
                builder: (context, value, _) {
                  return Visibility(
                    visible: (invalidHolding),
                    child: Center(
                      child: RotatedBox(
                              quarterTurns: invalidNotifier.value ==
                                      NativeDeviceOrientation.landscapeLeft
                                  ? 1
                                  : invalidNotifier.value ==
                                          NativeDeviceOrientation.landscapeRight
                                      ? -1
                                      : 2,
                              child: Lottie.asset(Assets.iconsRotate,
                                  repeat: true, width: 200.h, height: 200.h))
                          .animate()
                          .fadeIn(duration: 500.ms),
                    ),
                  );
                }),
            ValueListenableBuilder<NativeDeviceOrientation>(
                valueListenable: invalidNotifier,
                builder: (context, value, _) {
                  return Visibility(
                    visible: !(invalidHolding),
                    child: SizedBox(
                      width: double.infinity,
                      height: double.infinity,
                      child: CustomPaint(
                        painter: FacePainter(
                            similarity: _similarity,
                            faces: _faces,
                            livenessThreshold: _livenessThreshold),
                      ),
                    ),
                  );
                }),
            ValueListenableBuilder<NativeDeviceOrientation>(
                valueListenable: invalidNotifier,
                builder: (context, value, _) {
                  return Visibility(
                      visible: !(invalidHolding),
                      child: cameraMask(_recognized));
                }),
            Align(
              alignment: Alignment.bottomCenter,
              child: RoundedBottomContainer(
                matchScore: _similarity,
                livenessScore: _liveness,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget faceRecognizedWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: cFourthColor,
      child: Column(children: [
        Spacer(flex: 2),
        Expanded(
          flex: 3,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              _enrolledFace != null
                  ? Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(10..r),
                          child: Image.memory(
                            _enrolledFace,
                            height: 160.h,
                            width: 160.h,
                            frameBuilder: ((context, child, frame,
                                wasSynchronouslyLoaded) {
                              if (wasSynchronouslyLoaded) return child;
                              return AnimatedSwitcher(
                                duration: const Duration(milliseconds: 200),
                                child: frame != null
                                    ? child
                                    : Center(
                                        child: SizedBox(
                                          height: 160.h,
                                          width: 160.h,
                                          child: CupertinoActivityIndicator(
                                            radius: 20.r,
                                          ),
                                        ),
                                      ),
                              );
                            }),
                          ),
                        ),
                        SizedBox(
                          height: 5.h,
                        ),
                        Text(LocaleKeys.uploaded.tr())
                      ],
                    )
                  : SizedBox(
                      height: 1.h,
                    ),
              _identifiedFace != null
                  ? Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: Image.memory(
                            _identifiedFace,
                            height: 160.h,
                            width: 160.h,
                            frameBuilder: ((context, child, frame,
                                wasSynchronouslyLoaded) {
                              if (wasSynchronouslyLoaded) return child;
                              return AnimatedSwitcher(
                                duration: const Duration(milliseconds: 200),
                                child: frame != null
                                    ? child
                                    : Center(
                                        child: SizedBox(
                                          height: 160.h,
                                          width: 160.h,
                                          child: CupertinoActivityIndicator(
                                            radius: 20.r,
                                            color:
                                                Theme.of(context).primaryColor,
                                          ),
                                        ),
                                      ),
                              );
                            }),
                          ),
                        ),
                        SizedBox(
                          height: 5.h,
                        ),
                        Text(LocaleKeys.recognised.tr())
                      ],
                    )
                  : SizedBox(
                      height: 1.h,
                    )
            ],
          ),
        ),

        Expanded(flex: 4, child: Lottie.asset(Assets.iconsDone, repeat: false)),
        SizedBox(height: 50.h)

        ///Try again button (for debugging)
        // ElevatedButton(
        //   style: ElevatedButton.styleFrom(
        //     backgroundColor:
        //         Theme.of(context).colorScheme.primaryContainer,
        //   ),
        //   onPressed: () => faceRecognitionStart(),
        //   child: const Text('Try again'),
        // ),
      ]),
    );
  }

  Widget cameraMask(bool isRecognized) {
    var size = MediaQuery.of(context).size;
    double rectWidth = 250.w;
    double rectHeight = 350.h;
    double customPadding = 100.h;

    return Stack(
      alignment: Alignment.center,
      children: [
        ///Mask view
        // HeightLightMaskView(
        //   //set view size
        //   maskViewSize: size,
        //   //set barrierColor
        //   backgroundColor: cFirstColor.withOpacity(0.6),
        //   //set color for height-light
        //   color: Colors.transparent,
        //   //set height-light shape
        //   // if width == radius, circle or rect
        //   rRect: RRect.fromRectAndRadius(
        //     Rect.fromLTWH(
        //       (size.width - rectWidth) / 2,
        //       (size.height - rectHeight) / 2.5,
        //       rectWidth,
        //       rectHeight,
        //     ),
        //     Radius.circular(200.r),
        //   ),
        // ),
        Padding(
          padding: EdgeInsets.only(bottom: customPadding),
          child: SvgPicture.asset(
            height: 250.h,
            width: 250.h,
            key: ValueKey<bool>(isRecognized),
            Assets.iconsRecognationBorder,
          ),
        ),
        AnimatedSwitcher(
            duration: const Duration(milliseconds: 500),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(child: child, opacity: animation);
            },
            child: Visibility(
              visible: isRecognized,
              child: Padding(
                padding: EdgeInsets.only(bottom: customPadding),
                child: SvgPicture.asset(
                  height: 150.h,
                  width: 150.h,
                  key: ValueKey<bool>(isRecognized),
                  Assets.iconsScanningDone,
                ).animate().scale(curve: Curves.bounceInOut),
              ),
            )),
      ],
    );
  }

  void initializeTimer() {
    timer = PausableTimer(Duration(seconds: 2), () async {
      {
        bool canVibrate = await Vibrate.canVibrate;
        if (canVibrate) {
          if (Platform.isIOS)
            HapticFeedback.lightImpact();
          else {
            var _type = FeedbackType.light;
            Vibrate.feedback(_type);
          }
        }

        if (widget.server_approved == true) {
          if (widget.child != null) {
            Get.off(widget.child);
          } else {
            prefs.setBool(local_approved, true);
            faceDetectionViewController?.stopCamera();
            Get.offAll(LockProvider());
          }
        } else {
          var isApproved = await sendApprovedFace(_enrolledFace);
          if (isApproved) {
            print('==== Photo uploaded!');
            CustomToast.showToast(LocaleKeys.picture_uploaded.tr());
            faceDetectionViewController?.stopCamera();
            prefs.setBool(server_approved, true);
            prefs.setBool(local_approved, true);
            Get.offAll(LockProvider());
          } else {
            faceDetectionViewController?.stopCamera();
            Get.back();
          }
        }
      }
    });
  }
}

// ignore: must_be_immutable
class FaceDetectionView extends StatefulWidget
    implements FaceDetectionInterface {
  final FaceRecognitionViewState faceRecognitionViewState;

  const FaceDetectionView({super.key, required this.faceRecognitionViewState});

  @override
  Future<void> onFaceDetected(faces) async {
    await faceRecognitionViewState.onFaceDetected(faces);
  }

  @override
  State<StatefulWidget> createState() => _FaceDetectionViewState();
}

class _FaceDetectionViewState extends State<FaceDetectionView> {
  @override
  Widget build(BuildContext context) {
    if (defaultTargetPlatform == TargetPlatform.android) {
      return AndroidView(
        viewType: 'facedetectionview',
        onPlatformViewCreated: _onPlatformViewCreated,
      );
    } else {
      return UiKitView(
        viewType: 'facedetectionview',
        onPlatformViewCreated: _onPlatformViewCreated,
      );
    }
  }

  void _onPlatformViewCreated(int id) async {
    final prefs = await SharedPreferences.getInstance();
    var cameraLens = prefs.getInt("camera_lens");

    widget.faceRecognitionViewState.faceDetectionViewController =
        FaceDetectionViewController(id, widget);

    await widget.faceRecognitionViewState.faceDetectionViewController
        ?.initHandler();

    int? livenessLevel = prefs.getInt("liveness_level");
    await widget.faceRecognitionViewState._faceSdkPlugin
        .setParam({'check_liveness_level': livenessLevel ?? 0});

    await widget.faceRecognitionViewState.faceDetectionViewController
        ?.startCamera(cameraLens ?? 1);
  }
}

class FacePainter extends CustomPainter {
  dynamic faces;
  double livenessThreshold;
  double similarity;

  FacePainter(
      {required this.faces,
      required this.livenessThreshold,
      required this.similarity});

  @override
  void paint(Canvas canvas, Size size) async {
    if (faces != null) {
      var paint = Paint();
      paint.color = const Color.fromARGB(0xff, 0xff, 0, 0);
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 3;

      for (var face in faces) {
        double xScale = face['frameWidth'] / size.width;
        double yScale = face['frameHeight'] / size.height;

        String title = "";
        String title1 = "";
        Color color = const Color.fromARGB(0xff, 0xff, 0, 0);
        if (face['liveness'] < livenessThreshold) {
          color = const Color.fromARGB(0xff, 0xff, 0, 0);
          title = "${LocaleKeys.liveness.tr()}: ${face['liveness']}";
        } else {
          color = const Color.fromARGB(0xff, 0, 0xff, 0);
          title = "${LocaleKeys.liveness.tr()}: ${face['liveness']}";
          title1 = "${LocaleKeys.similarity.tr()}: $similarity";
        }

        // TextSpan span =
        //     TextSpan(style: TextStyle(color: color, fontSize: 20), text: title);
        // TextPainter tp = TextPainter(
        //     text: span,
        //     textAlign: TextAlign.left,
        //     textDirection: TextDirection.ltr);
        // tp.layout();
        // tp.paint(canvas, Offset(face['x1'] / xScale, face['y1'] / yScale - 30));
        //
        // TextSpan span1 = TextSpan(
        //     style: TextStyle(color: color, fontSize: 20), text: title1);
        // TextPainter tp1 = TextPainter(
        //     text: span1,
        //     textAlign: TextAlign.left,
        //     textDirection: TextDirection.ltr);
        // tp1.layout();
        // tp1.paint(
        //     canvas, Offset(face['x1'] / xScale, face['y1'] / yScale - 60));

        paint.color = color;
        canvas.drawOval(
          Rect.fromPoints(
            Offset(face['x1'] / xScale, face['y1'] / yScale),
            Offset(face['x2'] / xScale, face['y2'] / yScale),
          ),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

// ignore: must_be_immutable
class RoundedBottomContainer extends StatelessWidget {
  double matchScore = 0.0;
  double livenessScore = 0.0;
  int? matchInt = 0;
  int? livenesInt = 0;

  RoundedBottomContainer(
      {required this.matchScore, required this.livenessScore});

  @override
  Widget build(BuildContext context) {
    matchInt = (matchScore * 100).round();
    livenesInt = (livenessScore * 100).round();

    return Container(
      width: double.infinity,
      height: 90.h, // Set the desired height
      decoration: BoxDecoration(
        gradient: cFirstGradient, // Set the desired background color
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text(
              "${LocaleKeys.similarity.tr()}: ${matchInt}%",
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
              ),
            ),
            Text(
              "${LocaleKeys.liveness.tr()}: ${livenesInt == 0 ? "${LocaleKeys.clarifying.tr()}.." : livenesInt.toString() + "%"}",
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
