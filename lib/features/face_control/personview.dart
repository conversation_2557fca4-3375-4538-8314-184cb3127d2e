import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'person.dart';
import 'bio_lock_page.dart';

// ignore: must_be_immutable
class PersonView extends StatefulWidget {
  final List<Person> personList;
  final BioLockPageState homePageState;
  final bool isServerApproved;
  final bool isLocalApproved;

  const PersonView(
      {super.key,
      required this.personList,
      required this.homePageState,
      required this.isServerApproved,
      required this.isLocalApproved});

  @override
  _PersonViewState createState() => _PersonViewState();
}

class _PersonViewState extends State<PersonView> {
  SharedPreferences prefs = di();

  deletePerson(int index) async {
    await widget.homePageState.deletePerson(index);
    widget.homePageState.setState(() {
      print('Person deleted');
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        physics: BouncingScrollPhysics(),
        itemCount: widget.personList.length,
        itemBuilder: (BuildContext context, int index) {
          return SizedBox(
              child: Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15.r),
                  ),
                  color: widget.isLocalApproved ? cFirstColor : cRedTextColor,
                  child: Container(
                    padding: EdgeInsets.all(10.h),
                    child: Row(
                      children: [
                        Expanded(
                          flex: context.isTablet ? 1 : 2,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(15.r),
                            child: SizedBox(
                              child: Image.memory(
                                widget.personList[index].faceJpg,
                                fit: BoxFit.cover,
                                frameBuilder: ((context, child, frame,
                                    wasSynchronouslyLoaded) {
                                  if (wasSynchronouslyLoaded) return child;
                                  return AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 200),
                                    child: frame != null
                                        ? child
                                        : Center(
                                            child: SizedBox(
                                              height: 110.h,
                                              width: 110.h,
                                              child: CupertinoActivityIndicator(
                                                radius: 10.r,
                                              ),
                                            ),
                                          ),
                                  );
                                }),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            widget.isLocalApproved
                                ? LocaleKeys.confirmed.tr()
                                : LocaleKeys.face_not_recognised.tr(),
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            style:
                                TextStyle(color: cWhiteColor, fontSize: 18.sp),
                          ),
                        ),
                        Visibility(
                          visible: !widget.isServerApproved,
                          child: SizedBox(
                            width: 5.w,
                          ),
                        ),
                        Expanded(
                            flex: 1,
                            child: !widget.isServerApproved
                                ? IconButton(
                                    icon: Icon(Icons.delete,
                                        color: cWhiteColor, size: 22.h),
                                    onPressed: () => deletePerson(index),
                                  )
                                : IconButton(
                                    icon: Icon(Icons.info,
                                        color: cWhiteColor, size: 25.h),
                                    onPressed: () => CustomToast.showToast(
                                        LocaleKeys.admin_can_delete.tr()),
                                  )),
                        SizedBox(
                          width: 8.w,
                        )
                      ],
                    ),
                  )));
        });
  }
}
