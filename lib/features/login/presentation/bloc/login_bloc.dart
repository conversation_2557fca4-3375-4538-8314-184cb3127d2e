import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:flutter/cupertino.dart';
import 'package:meta/meta.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/features/login/data/repositories/u_login.dart';

part 'login_event.dart';

part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginData loginData;

  LoginBloc({required this.loginData}) : super(LoginInitial()) {
    on<SendLoginEvent>(_sendLogin, transformer: sequential());
  }

  FutureOr<void> _sendLogin(
      SendLoginEvent event, Emitter<LoginState> emit) async {

    emit(LoginLoading());


      final result = await loginData(
        LoginParams(event.tel, event.password, await getDeviceId() ?? 'error'),
      );
      result.fold(
              (failure) => {
            if (failure is NoConnectionFailure)
              emit(NoConnectionLogin())
            else if (failure is ServerFailure)
              emit(LoginFailure(failure.message))
          }, (r) {
        if (r == "1") {
          emit(LoginSuccess("Success"));
        } else if (r == "0") {
          emit(NoUser());
        } else if (r == "2" || r == false) {
          emit(ServerError());
        } else if (r == "3") {
          emit(PasswordRequired());
        } else if (r == "4") {
          emit(MacLimit());
        }  else if (r) {
          emit(PasswordAccepted());
        }
      });
    }
  }