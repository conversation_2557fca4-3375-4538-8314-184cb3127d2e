import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/login/presentation/pages/login_page.dart';
import 'package:yetakchi/generated/assets.dart';

class LangSelectPage extends StatefulWidget {
  const LangSelectPage({Key? key}) : super(key: key);

  @override
  State<LangSelectPage> createState() => _LangSelectPageState();
}

class _LangSelectPageState extends State<LangSelectPage> {
  final SharedPreferences sharedPreferences = di();

  @override
  void initState() {
    autoStartWall(context, false);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 35.h),
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              languageButton(Assets.imagesFlagUz, "O'zbek", 'uz', context),
              SizedBox(
                height: 15.h,
              ),
              languageButton(
                  Assets.imagesFlagQq, "Qaraqalpaqsha", 'kk', context),
              SizedBox(
                height: 15.h,
              ),
              languageButton(Assets.imagesFlagRu, "Русский", 'ru', context),
            ],
          ),
        ),
      ),
    );
  }
}

Widget languageButton(
    String flagAsset, String language, String code, BuildContext context) {
  return InkWell(
    onTap: () {
      setLangAndNavigate(code, context);
    },
    child: Container(
      height: 56.h,
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 25.w),
      padding: EdgeInsets.only(left: 8.w, right: 20.w),
      decoration: ShapeDecoration(
        color: Theme.of(context).primaryColor.withAlpha(20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(36.r),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CircleAvatar(
            radius: 20.r,
            child: Image.asset(
              flagAsset,
              height: 200.h,
              width: 200.h,
              fit: BoxFit.fill,
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
          Align(
              alignment: Alignment.centerLeft,
              child: Text(
                language,
                style: Theme.of(context).textTheme.bodyLarge,
              )),
          Spacer(),
          SvgPicture.asset(
            Assets.iconsArrowRight,
            height: 20.h,
          )
        ],
      ),
    ),
  );
}

void setLangAndNavigate(String langCode, BuildContext context) {
  switch (langCode) {
    case "uz":
      {
        context.setLocale(Locale('uz'));
        setLanguage('uz');
        Get.updateLocale(Locale("uz"));
      }
      break;

    case "ru":
      {
        context.setLocale(Locale('ru'));
        setLanguage('ru');
        Get.updateLocale(Locale("ru"));
      }

      break;

    case "kk":
      {
        context.setLocale(Locale('kk'));
        setLanguage('kk');
        Get.updateLocale(Locale("kk"));
      }

      break;

    default:
      // Handle the default case or leave it empty if not needed
      break;
  }

  Get.off(() => LoginPage.screen());
}
