import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/core/widgets/dialog_frame.dart';
import 'package:yetakchi/core/widgets/gradient_material_button.dart';
import 'package:yetakchi/core/widgets/language_dialog.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/auth/presentation/pages/auth_page.dart';
import 'package:yetakchi/features/lock/lock_switcher.dart';
import 'package:yetakchi/features/login/presentation/bloc/login_bloc.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key, this.numberCallback}) : super(key: key);

  final String? numberCallback;

  static Widget screen({String? numberCallback}) => MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => di<LoginBloc>()),
        ],
        child: LoginPage(
          numberCallback: numberCallback,
        ),
      );

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  var maskFormatter = MaskTextInputFormatter(mask: '(##) ###-##-##');
  TextEditingController tel = TextEditingController();
  TextEditingController password = TextEditingController();
  final SharedPreferences sharedPreferences = di();
  late LoginBloc _bloc;
  bool checkAgreement = false;
  String pathPDF = "";

  @override
  void initState() {
    maskFormatter.formatEditUpdate(TextEditingValue.empty,
        TextEditingValue(text: widget.numberCallback ?? ''));
    tel = TextEditingController(text: maskFormatter.getMaskedText());
    super.initState();
    _bloc = BlocProvider.of<LoginBloc>(context);
    loadAndSetPath();
  }

  @override
  void dispose() {
    tel.dispose();
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          // FocusScope.of(context).requestFocus(FocusNode());
        },
        child: SafeArea(
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(),
            child: Container(
              padding: EdgeInsets.only(top: 10.h),
              margin: EdgeInsets.symmetric(horizontal: 25.w),
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: BlocConsumer<LoginBloc, LoginState>(
                listener: (context, state) {
                  ///TODO: Make this UI persistent visible
                  if (state is NoUser) {
                    Snack(LocaleKeys.no_user.tr(), context, cRedColor);
                  }
                  if (state is ServerError) {
                    Snack(LocaleKeys.server_error.tr(), context, cRedColor);
                  }
                  if (state is NoConnectionLogin) {
                    WidgetsBinding.instance.addPostFrameCallback((time) {
                      Snack(LocaleKeys.check_internet_connection.tr(), context,
                          cRedColor);
                    });
                  }
                  if (state is LoginFailure) {
                    WidgetsBinding.instance.addPostFrameCallback((time) {
                      Snack(LocaleKeys.login_error.tr(), context, cRedColor);
                    });
                  }

                  if (state is MacLimit) {
                    WidgetsBinding.instance.addPostFrameCallback((time) {
                      Snack('Ilovaga kirish uchun qurilmalar soni cheklangan!',
                          context, cRedColor);
                    });
                  }

                  if (state is PasswordRequired) {
                    Dio dio = di();
                    SharedPreferences prefs = di();
                    dio.options.baseUrl = demoUrl;
                    prefs.setBool(is_demo, true);
                  }
                },
                buildWhen: (previous, current) {
                  if (current is PasswordAccepted) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      Navigator.pushReplacement(
                        context,
                        CupertinoPageRoute(
                            builder: (context) => LockProvider()),
                      );
                    });
                    return false;
                  }

                  if (current is LoginSuccess) {
                    Timer(Duration(milliseconds: 500), () {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        Navigator.pushReplacement(
                          context,
                          CupertinoPageRoute(
                              builder: (context) => AuthPage.screen(
                                  tel: maskFormatter.getUnmaskedText())),
                        );
                      });
                    });
                    return false;
                  } else {
                    return true;
                  }
                },
                builder: (context, state) {
                  return Column(
                    children: [
                      SizedBox(
                        height: context.isTablet ? 30.h : 0,
                      ),
                      Align(
                          alignment: Alignment.centerRight,
                          child: IconButton(
                            onPressed: () {
                              showDialog(
                                  context: context,
                                  builder: (_) {
                                    return BackdropFilter(
                                        filter: ImageFilter.blur(
                                            sigmaX: 5.0, sigmaY: 5.0),
                                        child: LanguageDialog(
                                          onSelectCallBack: () async {
                                            Navigator.pop(context);
                                          },
                                        ));
                                  });
                            },
                            icon: Transform.scale(
                              scale: 1,
                              child: SvgPicture.asset(
                                Assets.iconsLocaleIcon,
                                height: 25.h,
                                colorFilter: ColorFilter.mode(
                                    Theme.of(context).iconTheme.color!,
                                    BlendMode.srcIn),
                              ),
                            ),
                          )),
                      SizedBox(
                        height: 80.h,
                      ),
                      Text(LocaleKeys.enter_phone_number.tr(),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              fontWeight: FontWeight.w700,
                              fontSize: 24.sp,
                              color: Theme.of(context).primaryColor)),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding: EdgeInsets.only(top: 60.h, bottom: 8.h),
                          child: Text(
                            LocaleKeys.phone_number.tr(),
                            style: TextStyle(
                                fontSize: context.isTablet ? 12.sp : 16.sp),
                          ),
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(cRadius36.r),
                          color: Theme.of(context)
                              .primaryColorLight
                              .withOpacity(0.96),
                          boxShadow: const [
                            BoxShadow(color: cGrayColor0, spreadRadius: 1),
                          ],
                        ),
                        height: 60.h,
                        margin: EdgeInsets.only(bottom: 10.h),
                        padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                        child: Center(
                          child: Row(
                            children: [
                              SizedBox(
                                width: 6.w,
                              ),
                              Text(
                                '+998',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                        fontSize: 18.sp, fontFamily: "Regular"),
                              ),
                              Expanded(
                                child: TextFormField(
                                  inputFormatters: [maskFormatter],
                                  keyboardType: TextInputType.phone,
                                  cursorColor: cFirstColor,
                                  controller: tel,
                                  autofocus: true,
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    hintText: "(__)___-__-__",
                                    hintStyle: TextStyle(
                                        fontSize: 18.sp,
                                        letterSpacing: 3.w,
                                        color: cGrayColor1,
                                        fontFamily: "Regular"),
                                    prefixIconConstraints: BoxConstraints(
                                      maxWidth: 30.w,
                                      maxHeight: 30.h,
                                      minHeight: 25.h,
                                      minWidth: 25.w,
                                    ),
                                  ),
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                          fontSize: 18.sp,
                                          fontFamily: "Regular"),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Visibility(
                        visible: (state is PasswordRequired) ? true : false,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(cRadius36.r),
                            color: Theme.of(context)
                                .primaryColorLight
                                .withOpacity(0.96),
                            boxShadow: const [
                              BoxShadow(color: cGrayColor0, spreadRadius: 1),
                            ],
                          ),
                          height: 60.h,
                          margin: EdgeInsets.only(bottom: 20.h),
                          padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                          child: Center(
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  Assets.iconsLock,
                                  width: 24.w,
                                  height: 24.h,
                                  color: cFirstColor,
                                ),
                                SizedBox(
                                  width: 6.w,
                                ),
                                Expanded(
                                  child: TextFormField(
                                    keyboardType: TextInputType.visiblePassword,
                                    cursorColor: cFirstColor,
                                    controller: password,
                                    decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: LocaleKeys.password.tr(),
                                      hintStyle: TextStyle(
                                          fontSize: 18.sp,
                                          color: cGrayColor1,
                                          fontFamily: "Regular"),
                                      prefixIconConstraints: BoxConstraints(
                                        maxWidth: 30.w,
                                        maxHeight: 30.h,
                                        minHeight: 25.h,
                                        minWidth: 25.w,
                                      ),
                                    ),
                                    style: TextStyle(
                                        fontSize: 18.sp,
                                        color: cFirstColor,
                                        fontFamily: "Regular"),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          setState(() {
                            checkAgreement = !checkAgreement;
                          });
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Transform.scale(
                              scale: context.isTablet ? 2.5 : 1,
                              child: Checkbox(
                                  value: checkAgreement,
                                  onChanged: (value) {
                                    setState(() {
                                      checkAgreement = !checkAgreement;
                                    });
                                  }),
                            ),
                            SizedBox(
                              width: context.isTablet ? 10.w : 0,
                            ),
                            Flexible(
                              child: Text.rich(TextSpan(
                                  style: TextStyle(fontSize: 14.sp),
                                  //apply style to all
                                  children: [
                                    TextSpan(
                                        text: LocaleKeys.privacy_policy.tr(),
                                        style: const TextStyle(
                                          color: cSecondColor,
                                          decoration: TextDecoration.underline,
                                        ),
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () {
                                            loadAndSetPath().then((value) {
                                              showAgreementDialog(context);
                                            });
                                          }),
                                    TextSpan(
                                      text:
                                          " ${LocaleKeys.agree_requirement.tr()}",
                                      style:
                                          const TextStyle(color: cGrayColor1),
                                    ),
                                    TextSpan(
                                      text: " *",
                                      style: const TextStyle(color: cRedColor),
                                    ),
                                  ])),
                            ),
                          ],
                        ),
                      ),
                      Spacer(
                        flex: 3,
                      ),
                      ZoomTapAnimation(
                        onTap: !checkAgreement
                            ? () {
                                CustomToast.showToast(
                                    LocaleKeys.please_accept_conditions.tr());
                              }
                            : null,
                        child: _button(state),
                      ),
                      Spacer(
                        flex: 1,
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  sendPhoneNumber() {
    var phone = maskFormatter.getUnmaskedText();
    if (phone.length > 8) {
      _bloc.add(SendLoginEvent(phone, null));
    } else {
      Snack(LocaleKeys.not_phone_number.tr(), context, cRedColor);
    }
  }

  sendPhoneWithPassword() {
    var phone = maskFormatter.getUnmaskedText();
    var passwordText = password.text;
    if (phone.length > 8) {
      if (passwordText.isNotEmpty) {
        _bloc.add(SendLoginEvent(phone, passwordText));
      } else {
        Snack(LocaleKeys.password_mismatch.tr(), context, cRedColor);
      }
    } else {
      Snack(LocaleKeys.not_phone_number.tr(), context, cRedColor);
    }
  }

  showAgreementDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 100.h),
            child: AllDialogSkeleton(
                color: Theme.of(context).cardTheme.color,
                isNoShadow: true,
                child: Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(15.w),
                    child: PDFView(
                        filePath: pathPDF,
                        fitEachPage: true,
                        fitPolicy: FitPolicy.WIDTH),
                  ),
                ),
                title: LocaleKeys.privacy_policy_word.tr(),
                fontWeight: FontWeight.bold,
                icon: Assets.iconsDocument),
          );
        });
  }

  Future<File> fromAsset(String asset, String filename) async {
    // To open from assets, you can copy them to the app storage folder, and the access them "locally"
    Completer<File> completer = Completer();

    try {
      var dir = await getApplicationDocumentsDirectory();
      File file = File("${dir.path}/$filename");
      var data = await rootBundle.load(asset);
      var bytes = data.buffer.asUint8List();
      await file.writeAsBytes(bytes, flush: true);
      completer.complete(file);
    } catch (e) {
      throw Exception('Error parsing asset file!');
    }

    return completer.future;
  }

  Future<void> loadAndSetPath() async {
    SharedPreferences prefs = di();
    String? lang = prefs.getString(language_pref);
    await fromAsset(
            lang == 'uz'
                ? Assets.documentsPrivacyUz
                : languageText() == 'kk'
                    ? Assets.documentsPrivacyRu
                    : Assets.documentsPrivacyRu,
            lang == 'uz'
                ? "privacy_uz"
                : languageText() == 'kk'
                    ? "privacy_ru"
                    : "privacy_ru")
        .then((f) {
      pathPDF = f.path;
    });
  }

  Widget _button(state) {
    if (state is LoginLoading) {
      return GradientMaterialButton(
          child: CupertinoActivityIndicator(
            color: Theme.of(context).indicatorColor,
            radius: 10.r,
          ),
          onTap: !checkAgreement
              ? null
              : () {
                  CustomToast.showToast('${LocaleKeys.wait.tr()}...');
                });
    } else {
      return GradientMaterialButton(
          title: LocaleKeys.continue_word.tr(),
          onTap: !checkAgreement
              ? null
              : () {
                  if (state is PasswordRequired) {
                    sendPhoneWithPassword();
                  } else {
                    sendPhoneNumber();
                  }
                });
    }
  }
}
