import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/generated/assets.dart';

class IntroPage extends StatefulWidget {
  final Widget child;

  const IntroPage({Key? key, required this.child}) : super(key: key);

  @override
  State<IntroPage> createState() => _IntroPageState();
}

class _IntroPageState extends State<IntroPage> {
  final SharedPreferences sharedPreferences = di();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: isDark() ? cFourthColorDark : cFirstColor,
      body: SafeArea(
        child: Container(
            padding: EdgeInsets.symmetric(vertical: 35.h),
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Align(
                    alignment: Alignment.centerLeft,
                    child: Image.asset(Assets.imagesGerbOpacityGradient, scale: context.isPhone?1.2:0.5,)
                        .animate()
                        .fadeIn(duration: 1000.ms)),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SvgPicture.asset(Assets.iconsLogoWithText,      height: 42.h).animate().scale(
                        duration: 800.ms,
                        curve: Curves.bounceInOut,
                        begin: Offset(0.9, 0.9),
                        end: Offset(1, 1)),
                    Expanded(
                      child: SvgPicture.asset(Assets.imagesYiaVector,
                          height: 60.h, color: cWhiteColor),
                    )
                        .animate(onComplete: (a) {
                          Timer(Duration(seconds: 1), () {
                            Get.off(widget.child);
                          });
                        })
                        .fadeIn(
                            duration: 2000.ms) // uses `Animate.defaultDuration`
                        .scale(
                            duration: 600.ms,
                            begin: Offset(0.7, 0.7),
                            end: Offset(1, 1)) // inherits duration from fadeIn
                        .shimmer(
                            delay: 1000.ms,
                            duration: 1500.ms,
                            color: cSecondColor),
                    SvgPicture.asset(
                      Assets.iconsCompanyLogo,
                      color: cWhiteColor,
                        height: 30.h
                    ),
                  ],
                ),
              ],
            )),
      ),
    );
  }
}
