import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/errors/failures.dart';
import '../models/user_model.dart';

abstract class LoginLocalDataSource {
  Future<bool> setDataLocal(UserTokenModel user);
}

class LoginLocalDataSourceImpl implements LoginLocalDataSource {
  final SharedPreferences sharedPreferences;

  LoginLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<bool> setDataLocal(UserTokenModel user) async {
    try {
      sharedPreferences.setString("id", user.id.toString());
      sharedPreferences.setString("name", user.name.toString());
      sharedPreferences.setString("phone", user.phoneNumber.toString());
      sharedPreferences.setString("token", user.token.toString());
      return true;
    } on LocalFailure {
      return false;
    }
  }
}
