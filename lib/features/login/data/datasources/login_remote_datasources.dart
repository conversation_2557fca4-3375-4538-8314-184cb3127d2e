import 'package:android_sms_retriever/android_sms_retriever.dart';
import 'package:dio/dio.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/utils/target.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/features/login/data/models/user_model.dart';

abstract class LoginRemoteDatasource {
  Future<dynamic> setData(String tel, String? password, String macAddress);
}

class LoginRemoteDatasourceImpl implements LoginRemoteDatasource {
  final Dio client;

  LoginRemoteDatasourceImpl({required this.client});

  @override
  Future<dynamic> setData(
      String tel, String? password, String macAddress) async {
    String? appSignature;
    if (isAndroid()) {
      appSignature = await AndroidSmsRetriever.getAppSignature() ?? 'undefined';
      // CustomToast.showToast('Mac: ' + macAddress + 'signature' + appSignature.toString());
    }

    try {
      Map<String, String> body;

      if (password == null) {
        body = {
          "phone": tel,
          "macAddress": macAddress,
          "appSignature": appSignature ?? 'undefined'
        };
      } else {
        body = {
          "phone": tel,
          "password": password,
          "macAddress": macAddress,
        };
      }

      print('MAP: ' + body.toString());

      // print(macAddress);

      final response = await client.post(loginPath, data: body);

      print(response.data);
      final data = response.data;

      if (response.statusCode == 200) {
        if ((data is Map)
            ? data.containsKey("token")
                ? data["token"] != null
                : false
            : false) {
          String token = data["token"];
          Map<String, dynamic> decodedToken = JwtDecoder.decode(token);

          // CustomToast.showToast(decodedToken.toString());

          var user = UserTokenModel(
              id: decodedToken['_id'] ?? "",
              name:
                  "${decodedToken['title']?['firstName'] ?? "x"} ${decodedToken['title']?['lastName'] ?? "y"} ${decodedToken['title']?['middleName'] ?? "z"}",
              phoneNumber: tel,
              token: token);

          return user;
        } else if (data == "appStore" ? true : false) {
          return '3';
        } else {
          return showMessage(data, '2');
        }
      } else if (response.statusCode == 201) {
        if (data.containsKey("message")
            ? data["message"] == "success"
            : false) {
          return '1';
        } else {
          return showMessage(data, '2');
        }
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          if (e.response?.statusCode == 410) {
            if (e.response?.data.containsKey("message")
                ? e.response?.data["message"] == "macAddress not limit"
                : false) {
              return showMessage(e.response?.data, '4');
            } else {
              return showMessage(e.response?.data, '2');
            }
          } else {
            return showMessage(e.response?.data, '0');
          }
        }
      }
      if (e.type == DioExceptionType.connectionError) {
        print('check your connection');
        throw (NoConnectionFailure('no connection'));
      }

      if (e.type == DioExceptionType.receiveTimeout) {
        print('unable to connect to the server');
        throw (ServerFailure('time out'));
      }

      if (e.type == DioExceptionType.unknown) {
        print('Something went wrong');
        throw (ServerFailure('unknown'));
      }
    } on InputFormatterFailure catch (e) {
      print('Input formatter error: $e');
      return "500";
    } catch (e) {
      // CustomToast.showToast(e.toString());
      print('Exception in login_remote_datasource $e');
      throw FormatException('Error on server response format!');
    }
  }
}

String showMessage(Map<dynamic, dynamic> data, String errorNo) {
  if (!data.containsKey("message")) {
    CustomToast.showToast(data.toString());
  } else {
    CustomToast.showToast(data['message'].toString());
  }
  return errorNo;
}
