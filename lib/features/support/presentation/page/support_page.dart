import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/core/widgets/header_widget.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class SupportPage extends StatelessWidget {
  final maskFormatter = MaskTextInputFormatter(mask: '+998 (##) ###-##-##');

  SupportPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeaderWidget(
        title: LocaleKeys.support_service.tr(),
        onBackTap: () {
          Navigator.pop(context);
        },
        isBackVisible: true,
      ),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                  LocaleKeys.support_contact.tr(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(),
                ),
                SizedBox(
                  height: 32.h,
                ),
                InkWell(
                  onTap: () async {
                    launchCustomUrl(TELEGRAM);
                  },
                  child: Container(
                    height: 60.h,
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "${LocaleKeys.telegram_group.tr()}:",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(),
                          ),
                          Text(
                            "@yetakchigovuz",
                            style: TextStyle(
                                color: isDark() ? cWhiteColor : cFirstColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16.sp),
                          )
                        ]),
                  ),
                ),
                Divider(
                  height: 2.h,
                  color: cGrayColor1,
                ),
                InkWell(
                  onTap: () async {
                    launchCustomUrl(MODERATOR);
                  },
                  child: Container(
                    height: 60.h,
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "${LocaleKeys.moderator.tr()}:",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(),
                          ),
                          Text(
                            "@fvhmoderator",
                            style: TextStyle(
                                color: isDark() ? cWhiteColor : cFirstColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16.sp),
                          )
                        ]),
                  ),
                ),
                Divider(
                  height: 2.h,
                  color: cGrayColor1,
                ),
                InkWell(
                  onTap: () async {
                    final Uri emailUri = Uri(
                      scheme: 'mailto',
                      path: EMAIL,
                    );
                    if (await canLaunchUrl(emailUri)) {
                      launchUrl(emailUri);
                    } else {
                      CustomToast.showToast('This action is not supported');
                    }
                  },
                  child: Container(
                    height: 60.h,
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "${LocaleKeys.email.tr()}:",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(),
                          ),
                          Text(
                            EMAIL,
                            style: TextStyle(
                                color: isDark() ? cWhiteColor : cFirstColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16.sp),
                          )
                        ]),
                  ),
                ),
                Divider(
                  height: 2.h,
                  color: cGrayColor1,
                ),
                InkWell(
                  onTap: () async {
                    launchCustomUrl(WEBSITE);
                  },
                  child: Container(
                    height: 60.h,
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "${LocaleKeys.website.tr()}:",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(),
                          ),
                          Text(
                            "premiumsoft.uz",
                            style: TextStyle(
                                color: isDark() ? cWhiteColor : cFirstColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16.sp),
                          )
                        ]),
                  ),
                ),
                Divider(
                  height: 2.h,
                  color: cGrayColor1,
                ),
                SizedBox(
                  height: 6.h,
                ),
                Container(
                  width: MediaQuery.of(context).size.width,
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "${LocaleKeys.telephone.tr()}:",
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            InkWell(
                              onTap: () async {
                                final Uri launchUri =
                                    Uri(scheme: 'tel', path: SUPPORT_TEL1);
                                if (await canLaunchUrl(launchUri)) {
                                  await launchUrl(launchUri);
                                } else {
                                  CustomToast.showToast(
                                      'This action is not supported');
                                }
                              },
                              child: Text(
                                maskFormatter.maskText(SUPPORT_TEL1),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(),
                              ),
                            ),
                            SizedBox(
                              height: 4.h,
                            ),
                            InkWell(
                              onTap: () async {
                                final Uri launchUri =
                                    Uri(scheme: 'tel', path: SUPPORT_TEL2);
                                if (await canLaunchUrl(launchUri)) {
                                  await launchUrl(launchUri);
                                } else {
                                  CustomToast.showToast(
                                      'This action is not supported');
                                }
                              },
                              child: Text(
                                maskFormatter.maskText(SUPPORT_TEL2),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(),
                              ),
                            ),
                          ],
                        )
                      ]),
                ),
              ],
            ),
            SvgPicture.asset(
              Assets.iconsCompanyLogo,
              colorFilter: ColorFilter.mode(
                isDark() ? cWhiteColor : cFirstColor,
                BlendMode.srcIn,
              ),
              height: 30.h,
            )
          ],
        ),
      ),
    );
  }
}
