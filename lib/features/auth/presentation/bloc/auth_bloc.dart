import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:meta/meta.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/features/auth/domain/usescases/auth.dart';

part 'auth_event.dart';

part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthData authData;

  AuthBloc({required this.authData}) : super(AuthInitial()) {
    on<SendSMSEvent>(_sendSms, transformer: droppable());
  }

  FutureOr<void> _sendSms(SendSMSEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    final result = await authData(
      AuthParams(event.sms, event.tel, await getDeviceId() ?? 'error'),
    );
    result.fold(
        (failure) => {
              if (failure is NoConnectionFailure)
                emit(AuthNoInternet())
              else if (failure is ServerFailure)
                emit(AuthFailure(failure.message))
              else if (failure is InputFormatterFailure)
                emit(AuthFailure(failure.message))
            },
        (r) => {
              if (r) {emit(AuthSuccess("Success"))} else {emit(AuthError())}
            });
  }
}
