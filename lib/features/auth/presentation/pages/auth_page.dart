import 'package:android_sms_retriever/android_sms_retriever.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:pinput/pinput.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/utils/target.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/core/widgets/gradient_material_button.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:yetakchi/features/lock/lock_switcher.dart';
import 'package:yetakchi/features/login/presentation/pages/login_page.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';
import 'package:get/get.dart' hide Trans;
import 'package:easy_localization/easy_localization.dart';

import '../widgets/timer.dart';

class AuthPage extends StatefulWidget {
  final String tel;

  const AuthPage(this.tel, {Key? key}) : super(key: key);

  static Widget screen({required String tel}) => BlocProvider(
        create: (context) => di<AuthBloc>(),
        child: AuthPage(tel),
      );

  @override
  _AuthPageState createState() => _AuthPageState();
}

class _AuthPageState extends State<AuthPage> {
  var maskFormatter = MaskTextInputFormatter(mask: '#   #   #   #');
  TextEditingController otpCode = TextEditingController();
  bool hasTimerStopped = false;

  late AuthBloc _bloc;

  var defaultPinTheme;

  var focusedPinTheme;

  var submittedPinTheme;

  @override
  void initState() {
    if (isAndroid()) {
      initSmsListener();
    }
    _bloc = BlocProvider.of<AuthBloc>(context);
    super.initState();
  }

  @override
  void dispose() {
    otpCode.dispose();
    if (isAndroid()) {
      AndroidSmsRetriever.stopOneTimeConsentListener();
    }
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    defaultPinTheme = PinTheme(
      width: 52.h,
      height: 56.h,
      margin: EdgeInsets.only(right: context.isTablet?5.w:0),
      textStyle: TextStyle(
          fontSize: 20.sp,
          color: Theme.of(context).textTheme.bodyMedium?.color,
          fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        border: Border.all(color: cGrayColor1),
        borderRadius: BorderRadius.circular(10.r),
      ),
    );

    focusedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        border: Border.all(color: Theme.of(context).primaryColor),
        borderRadius: BorderRadius.circular(8.r),
      ),
    );

    submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        border: Border.all(color: cGreenColor),
        color: cGreenColor.withAlpha(10),
      ),
    );

    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: BlocConsumer<AuthBloc, AuthState>(
            listener: (BuildContext context, AuthState state) {
              if (state is AuthNoInternet) {
                CustomToast.showToast(
                    LocaleKeys.check_internet_connection.tr());
              }
              if (state is AuthFailure) {
                CustomToast.showToast(state.message);
              }
              if (state is AuthError) {
                CustomToast.showToast(LocaleKeys.code_is_wrong.tr());
              }
              if (state is AuthSuccess) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Get.off(LockProvider());
                });
              }
            },
            builder: (context, state) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Spacer(),
                  Text(LocaleKeys.sms_confirmation.tr(),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontWeight: FontWeight.w700,
                          fontSize: 24.sp,
                          color: Theme.of(context).primaryColor)),
                  SizedBox(
                    height: 16.h,
                  ),
                  Container(
                    width: 274.w,
                    margin: EdgeInsets.only(bottom: 16.h),
                    child: Text(
                      LocaleKeys.this998.tr() +
                          widget.tel +
                          LocaleKeys.enter_code.tr(),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 13.sp,
                          color: Theme.of(context).primaryColor,
                          fontFamily: "Regular"),
                    ),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Pinput(
                    controller: otpCode,
                    defaultPinTheme: defaultPinTheme,
                    focusedPinTheme: focusedPinTheme,
                    submittedPinTheme: submittedPinTheme,
                    validator: (s) {
                      if (state is AuthSuccess) {
                        return s == otpCode
                            ? null
                            : LocaleKeys.code_is_wrong.tr();
                      }
                    },
                    pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                    showCursor: true,
                    onCompleted: (pin) {
                      var text = otpCode.text;
                      if (text.length > 3) {
                        _bloc.add(SendSMSEvent(text, widget.tel));
                      } else {
                        CustomToast.showToast(
                            LocaleKeys.entering_less_four.tr());
                      }
                    },
                  ),
                  SizedBox(
                    height: 6.h,
                  ),
                  Visibility(
                    visible: hasTimerStopped,
                    child: TextButton(
                      onPressed: () {
                        Get.offAll(
                            LoginPage.screen(numberCallback: widget.tel));
                      },
                      child: Text(
                        LocaleKeys.reenter_number.tr(),
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: cGrayColor1,
                        ),
                      ),
                    ),
                  ),
                  Visibility(
                    visible: !hasTimerStopped,
                    child: CountDownTimer(
                      secondsRemaining: 120,
                      whenTimeExpires: () {
                        setState(() {
                          hasTimerStopped = true;
                        });
                      },
                      countDownTimerStyle: TextStyle(
                        color: cRedColor,
                        fontSize: 17.0.sp,
                        height: 1.2.h,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 40.w),
                    child: ZoomTapAnimation(
                      child: _button(state),
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        Assets.iconsC,
                        colorFilter: ColorFilter.mode(
                          Theme.of(context).disabledColor,
                          BlendMode.srcIn,
                        ),
                        width: 10.w,
                        height: 10.w,
                      ),
                      SizedBox(
                        width: 8.w,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "PremiumSoft",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: 10.sp,
                                fontFamily: "Medium",
                                color: cGrayColor1),
                          ),
                          SizedBox(
                            height: 5.h,
                          ),
                          Text(
                            "v: " + APP_VERSION,
                            style: TextStyle(
                                fontSize: 10.sp,
                                fontFamily: "Medium",
                                color: cGrayColor1),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 28.h,
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _button(state) {
    print(state);
    if (state is AuthSuccess || state is AuthLoading) {
      return GradientMaterialButton(
          child: CupertinoActivityIndicator(
            color: Theme.of(context).indicatorColor,
            radius: 10.r,
          ),
          onTap: () {
            CustomToast.showToast('${LocaleKeys.wait.tr()}...');
          });
    } else {
      return GradientMaterialButton(
          title: LocaleKeys.continue_word.tr(),
          onTap: () {
            var text = otpCode.text;
            if (text.length > 3) {
              _bloc.add(SendSMSEvent(text, widget.tel));
            } else {
              CustomToast.showToast(LocaleKeys.entering_less_four.tr());
            }
          });
    }
  }

  void initSmsListener() async {
    String? message = await AndroidSmsRetriever.listenForSms();
    var code = '';
    if (message != null) {
      code = getCode(message);
    }
    if (code != '') {
      otpCode.text = code;
      AndroidSmsRetriever.stopOneTimeConsentListener();
      _bloc.add(SendSMSEvent(code, widget.tel));
    }
  }

  String getCode(String sms) {
    try {
      final intRegex = RegExp(r'\d+', multiLine: true);
      final code = intRegex.allMatches(sms).first.group(0);
      return code ?? '';
    } catch (e) {
      return '';
    }
  }
}
