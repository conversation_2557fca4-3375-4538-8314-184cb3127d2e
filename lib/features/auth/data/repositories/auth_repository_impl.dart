import 'package:dartz/dartz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/features/auth/data/datasources/auth_local_datasources.dart';
import 'package:yetakchi/features/auth/data/datasources/auth_remote_datasources.dart';
import 'package:yetakchi/features/auth/domain/repositories/auth_repository.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class AuthRepositoryImpl extends AuthRepository {
  final AuthRemoteDatasourceImpl authRemoteDatasource;
  final AuthLocalDataSourceImpl authLocalDatasource;
  final NetworkInfo networkInfo;

  AuthRepositoryImpl(
      {required this.authRemoteDatasource,
      required this.authLocalDatasource,
      required this.networkInfo});

  @override
  Future<Either<Failure, bool>> sendAuth(
      String code, String tel, String mac) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await authRemoteDatasource.setData(code, tel, mac);
        if (result == "0") {
          return const Right(false);
        } else if (result == "1") {
          return Left(ServerFailure(
            LocaleKeys.code_not_actual.tr(),
          ));
        } else if (result == "2") {
          return  Left(ServerFailure(LocaleKeys.unknown_server_error.tr()));
        } else {
          try {
            final resultLocal = await authLocalDatasource.setDataLocal(result);
            return Right(resultLocal);
          } on LocalFailure {
            return  Left(LocalFailure(LocaleKeys.data_not_written.tr()));
          }
        }
      } on ServerFailure {
        return const Left(ServerFailure("Server Failure"));
      } on InputFormatterFailure {
        return const Left(InputFormatterFailure("Input Formatter error"));
      }
    } else {
      return  Left(
          NoConnectionFailure(LocaleKeys.check_internet_connection.tr()));
    }
  }
}
