import 'package:dio/dio.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/features/login/data/models/user_model.dart';

abstract class AuthRemoteDatasource {
  Future<dynamic> setData(String code, String tel, String mac);
}

class AuthRemoteDatasourceImpl implements AuthRemoteDatasource {
  final Dio client;

  AuthRemoteDatasourceImpl({required this.client});

  @override
  Future<dynamic> setData(String code, String tel, String mac) async {
    try {
      var body = {"verifyCode": code, "phone": tel, "macAddress": mac};
      // CustomToast.showToast(mac);

      final response = await client.post(authPath, data: body);
      // print(response.body);
      if (response.statusCode == 200) {
        final data = response.data;
        String token = data["token"];
        Map<String, dynamic> decodedToken = JwtDecoder.decode(token);

        // CustomToast.showToast(decodedToken.toString());

        var user = UserTokenModel(
            id: decodedToken['_id'] ?? "",
            name:
                "${decodedToken['title']?['firstName'] ?? "x"} ${decodedToken['title']?['lastName'] ?? "y"} ${decodedToken['title']?['middleName'] ?? "z"}",
            phoneNumber: tel,
            token: token);

        return user;
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          if (e.response?.statusCode == 400) {
            return "0";
          } else if (e.response?.statusCode == 404) {
            return "1";
          } else {
            return "2";
          }
        }
      }
      if (e.type == DioExceptionType.connectionError) {
        print('check your connection');
        throw (NoConnectionFailure('no connection'));
      }

      if (e.type == DioExceptionType.receiveTimeout) {
        print('unable to connect to the server');
        throw (ServerFailure('time out'));
      }

      if (e.type == DioExceptionType.unknown) {
        print('Something went wrong');
        throw (ServerFailure('unknown'));
      }
    } on InputFormatterFailure catch (e) {
      return InputFormatterFailure("Input Formatter error");
    } catch (e) {
      // CustomToast.showToast(e.toString());
      print('Exception in login_remote_datasource $e');
      throw FormatException('Error on server response format!');
    }
  }
}
