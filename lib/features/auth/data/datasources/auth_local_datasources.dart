import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/features/login/data/models/user_model.dart';

abstract class AuthLocalDataSource {
  Future<bool> setDataLocal(UserTokenModel user);

}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences sharedPreferences;

  AuthLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<bool> setDataLocal(UserTokenModel user) async {
    try {
      sharedPreferences.setString("id", user.id.toString());
      sharedPreferences.setString("name", user.name.toString());
      sharedPreferences.setString("phone", user.phoneNumber.toString());
      sharedPreferences.setString("token", user.token.toString());
      return true;
    } on LocalFailure {
      return false;
    }
  }
}

