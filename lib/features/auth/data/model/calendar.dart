
import 'package:isar/isar.dart';

part 'calendar.g.dart';

@collection
@Name("Calendar")
class Calendar {
  Calendar({
      this.id, 
      this.days, 
      this.holidays, 
      this.arrivalTime, 
      this.createdAt, 
      this.updatedAt, 
      this.end, 
      this.lunchEnd, 
      this.lunchStart, 
      this.start,});

  Calendar.fromJson(dynamic json) {
    id = json['_id'];
    days = json['days'] != null ? json['days'].cast<int>() : [];
    holidays = json['holidays'] != null ? json['holidays'].cast<String>() : [];
    arrivalTime = json['arrivalTime'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    end = json['end'];
    lunchEnd = json['lunchEnd'];
    lunchStart = json['lunchStart'];
    start = json['start'];
  }

  Id localId = Isar.autoIncrement;
  String? id;
  List<int>? days;
  List<String>? holidays;
  String? arrivalTime;
  String? createdAt;
  String? updatedAt;
  String? end;
  String? lunchEnd;
  String? lunchStart;
  String? start;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['days'] = days;
    map['holidays'] = holidays;
    map['arrivalTime'] = arrivalTime;
    map['createdAt'] = createdAt;
    map['updatedAt'] = updatedAt;
    map['end'] = end;
    map['lunchEnd'] = lunchEnd;
    map['lunchStart'] = lunchStart;
    map['start'] = start;
    return map;
  }

}