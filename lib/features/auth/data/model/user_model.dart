
import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/embedded_models.dart';

part 'user_model.g.dart';

@collection
@Name("UserModel")
class UserModel {
  UserModel({
    this.inActive,
    this.demo,
    this.id,
    this.firstName,
    this.lastName,
    this.middleName,
    this.province,
    this.region,
    this.role,
    this.phone,
    this.macCount,
    this.macAddress,
    this.active,
    this.info,
    this.payments,
    this.endDate,
    this.userId,
    this.district,
    this.avatar,
    this.updatedAt,});

  UserModel.fromJson(dynamic json) {
    inActive =
    json['inActive'] != null ? InActive.fromJson(json['inActive']) : null;
    demo = json['demo'];
    id = json['_id'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    middleName = json['middleName'];
    province =
    json['province'] != null ? Province.fromJson(json['province']) : null;
    region = json['region'] != null ? Region.fromJson(json['region']) : null;
    role = json['role'];
    phone = json['phone'];
    macCount = json['macCount'];
    macAddress =
    json['macAddress'] != null ? json['macAddress'].cast<String>() : [];
    active = json['active'];
    info = json['info'] != null ? Info.fromJson(json['info']) : null;
    if (json['payments'] != null) {
      payments = [];
      json['payments'].forEach((v) {
        payments?.add(Payments.fromJson(v));
      });
    }
    endDate = json['endDate'];
    userId = json['userId'];
    district =
    json['district'] != null ? District.fromJson(json['district']) : null;
    avatar = json['avatar'];
    updatedAt = json['updatedAt'];
  }

  Id localId = Isar.autoIncrement;
  InActive? inActive;
  bool? demo;
  String? id;
  String? firstName;
  String? lastName;
  String? middleName;
  Province? province;
  Region? region;
  String? role;
  String? phone;
  int? macCount;
  List<String>? macAddress;
  bool? active;
  Info? info;
  List<Payments>? payments;
  String? endDate;
  int? userId;
  District? district;
  String? avatar;
  String? updatedAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (inActive != null) {
      map['inActive'] = inActive?.toJson();
    }
    map['demo'] = demo;
    map['_id'] = id;
    map['firstName'] = firstName;
    map['lastName'] = lastName;
    map['middleName'] = middleName;
    if (province != null) {
      map['province'] = province?.toJson();
    }
    if (region != null) {
      map['region'] = region?.toJson();
    }
    map['role'] = role;
    map['phone'] = phone;
    map['macCount'] = macCount;
    map['macAddress'] = macAddress;
    map['active'] = active;
    if (info != null) {
      map['info'] = info?.toJson();
    }
    if (payments != null) {
      map['payments'] = payments?.map((v) => v.toJson()).toList();
    }
    map['endDate'] = endDate;
    map['userId'] = userId;
    if (district != null) {
      map['district'] = district?.toJson();
    }
    map['avatar'] = avatar;
    map['updatedAt'] = updatedAt;
    return map;
  }
}