class ChptModel {
  ChptModel({
      this.id, 
      this.userId, 
      this.collection, 
      this.chats, 
      this.inToken, 
      this.outToken, 
      this.createdAt, 
      this.updatedAt,});

  ChptModel.fromJson(dynamic json) {
    id = json['_id'];
    userId = json['userId'];
    collection = json['collection'];
    if (json['chats'] != null) {
      chats = [];
      json['chats'].forEach((v) {
        chats?.add(Chats.fromJson(v));
      });
    }
    inToken = json['inToken'];
    outToken = json['outToken'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
  String? id;
  String? userId;
  String? collection;
  List<Chats>? chats;
  int? inToken;
  int? outToken;
  String? createdAt;
  String? updatedAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['userId'] = userId;
    map['collection'] = collection;
    if (chats != null) {
      map['chats'] = chats?.map((v) => v.toJson()).toList();
    }
    map['inToken'] = inToken;
    map['outToken'] = outToken;
    map['createdAt'] = createdAt;
    map['updatedAt'] = updatedAt;
    return map;
  }

}

class Chats {
  Chats({
      this.role, 
      this.content,});

  Chats.fromJson(dynamic json) {
    role = json['role'];
    content = json['content'];
  }
  String? role;
  String? content;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['role'] = role;
    map['content'] = content;
    return map;
  }

}