part of 'chat_bloc.dart';

sealed class ChatState extends Equatable {
  const ChatState();
}

final class ChatInitial extends ChatState {
  @override
  List<Object> get props => [];
}

final class ChatEmpty extends ChatState {
  @override
  List<Object> get props => [];
}

final class ChatInitialLoading extends ChatState {
  @override
  List<Object> get props => [];
}

final class ChatSendLoading extends ChatState {
  @override
  List<Object> get props => [];
}

final class ChatInitialSuccess extends ChatState {
  final List<Chats> list;

  ChatInitialSuccess({required this.list});

  @override
  List<Object> get props => [list];
}

final class ChatSendSuccess extends ChatState {
  final Chats chat;

  ChatSendSuccess({required this.chat});

  @override
  List<Object> get props => [];
}

class ChatSendError extends ChatState {
  final String message;

  ChatSendError({required this.message});

  @override
  List<Object> get props => [message];
}

class ChatError extends ChatState {
  final String message;

  ChatError({required this.message});

  @override
  List<Object> get props => [message];
}
