part of 'chat_bloc.dart';

sealed class ChatEvent extends Equatable {
  const ChatEvent();
}

class GetChatHistoryEvent extends ChatEvent {
  final String userId;

  GetChatHistoryEvent({required this.userId});

  @override
  List<Object?> get props => [userId];
}

class SendMessageEvent extends ChatEvent {
  final String userId;
  final String content;

  SendMessageEvent({required this.userId, required this.content});

  @override
  List<Object> get props => [userId, content];
}
