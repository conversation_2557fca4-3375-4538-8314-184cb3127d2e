import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:win32/win32.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/widgets/dotted_border.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/yetakchi_ai/model/chpt_model.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'chat_event.dart';

part 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final NetworkInfo networkInfo = di();
  final Dio dio = Dio(BaseOptions(
      baseUrl: "http://89.249.62.62:5001/api/",
      headers: {"x-token": "674ffbcd3f31301f303bd376"}));

  ChatBloc() : super(ChatInitial()) {
    on<GetChatHistoryEvent>(loadChatHistory);
    on<SendMessageEvent>(sendChat);
  }

  FutureOr<void> loadChatHistory(
      GetChatHistoryEvent event, Emitter<ChatState> emit) async {
    if (await networkInfo.isConnected) {
      try {
        emit(ChatInitialLoading());
        var response =
            await dio.get("site", queryParameters: {"userId": event.userId});
        if (response.statusCode == 200) {
          if (response.data == null) {
            emit(ChatEmpty());
          } else {
            ChptModel chptModel = ChptModel.fromJson(response.data);
            emit(ChatInitialSuccess(list: chptModel.chats ?? []));
          }
        } else {
          emit(ChatError(message: LocaleKeys.server_error.tr()));
        }
      } on DioException catch (e) {
        emit(ChatError(message: LocaleKeys.server_error.tr()));
      } catch (e) {
        emit(ChatError(message: LocaleKeys.server_error.tr()));
      }
    } else {
      emit(ChatError(message: LocaleKeys.check_internet_connection.tr()));
    }
  }

  FutureOr<void> sendChat(
      SendMessageEvent event, Emitter<ChatState> emit) async {
    if (await networkInfo.isConnected) {
      try {
        emit(ChatSendLoading());
        var response = await dio.post("site",
            data: {"userId": event.userId, "content": event.content});
        if (response.statusCode == 200) {
          Chats chats = Chats.fromJson(response.data);
          emit(ChatSendSuccess(chat: chats));
        } else {
          emit(ChatSendError(message: LocaleKeys.server_error.tr()));
        }
      } on DioException catch (e) {
        emit(ChatSendError(message: LocaleKeys.server_error.tr()));
      } catch (e) {
        emit(ChatSendError(message: LocaleKeys.server_error.tr()));
      }
    } else {
      emit(ChatSendError(message: LocaleKeys.server_error.tr()));
    }
  }
}
