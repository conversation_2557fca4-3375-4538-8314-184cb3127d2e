import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:rive/rive.dart' as rive;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/failure_widget.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/yetakchi_ai/model/chpt_model.dart';
import 'package:yetakchi/features/yetakchi_ai/presentation/bloc/chat_bloc.dart';
import 'package:yetakchi/features/yetakchi_ai/presentation/widget/chat_item_me.dart';
import 'package:yetakchi/features/yetakchi_ai/presentation/widget/chat_item_other.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

import '../../../../generated/assets.dart';

class YetakchiAiPage extends StatefulWidget {
  const YetakchiAiPage({super.key});

  static Widget screen() {
    return BlocProvider(
      create: (context) => ChatBloc(),
      child: YetakchiAiPage(),
    );
  }

  @override
  State<YetakchiAiPage> createState() => _YetakchiAiPageState();
}

class _YetakchiAiPageState extends State<YetakchiAiPage> {
  final TextEditingController txtController = TextEditingController();
  bool isEmpty = false;
  List<Chats> messages = [];
  final ScrollController _sc_chat = ScrollController();
  SharedPreferences sharedPreferences = di();

  void scrollDown() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_sc_chat.hasClients) {
        _sc_chat.animateTo(
          _sc_chat.position.maxScrollExtent + 10000,
          duration: const Duration(milliseconds: 600),
          curve: Curves.linear,
        );
      }
    });
  }

  @override
  void initState() {
    super.initState();
    BlocProvider.of<ChatBloc>(context).add(
        GetChatHistoryEvent(userId: sharedPreferences.getString('id') ?? ""));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Container(
              color: isDark() ? cFourthColorDark : cWhiteColor,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              height: 60.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ClipOval(
                    child: Material(
                      color:
                          isDark() ? cCardDarkColor : cFirstColor.withAlpha(20),
                      child: IconButton(
                        iconSize: 30.r,
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: SvgPicture.asset(
                          Assets.iconsArrowLeft,
                          width: 20.h,
                          height: 20.h,
                          colorFilter: ColorFilter.mode(
                              isDark() ? cPrimaryTextDark : cFirstColor,
                              BlendMode.srcIn),
                        ),
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      Image.asset(
                        Assets.imagesYetakchiAi,
                        width: 40.w,
                        height: 40.w,
                      ),
                      SizedBox(width: 4.w),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Yetakchi AI',
                              style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color:
                                      isDark() ? cWhiteColor : cFirstTextColor,
                                  fontSize: 16.sp),
                            ),
                            WidgetSpan(
                              child: Transform.translate(
                                offset: Offset(4, -4),
                                // Adjust position as needed
                                child: Text(
                                  'Beta',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: cRedColor,
                                      fontWeight: FontWeight.w600),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                  SizedBox()
                ],
              ),
            ),
            Container(
              height: 1,
              color: cGrayColor0,
            ),
            BlocConsumer<ChatBloc, ChatState>(
              listener: (context, state) {
                if (state is ChatInitialSuccess) {
                  messages = state.list;
                  scrollDown();
                } else if (state is ChatSendSuccess) {
                  setState(() {
                    messages.add(state.chat);
                    messages.removeAt(messages.length - 2);
                    scrollDown();
                  });
                } else if (state is ChatInitialSuccess ||
                    state is ChatSendSuccess ||
                    state is ChatSendError ||
                    state is ChatSendLoading) {
                  scrollDown();
                }
              },
              builder: (context, state) {
                if (state is ChatInitialLoading) {
                  return Expanded(
                      child: CupertinoActivityIndicator(
                    radius: cRadius22.r,
                    color: cFirstColor,
                  ));
                } else if (state is ChatInitialSuccess ||
                    state is ChatSendSuccess ||
                    state is ChatSendError ||
                    state is ChatSendLoading) {
                  return Expanded(
                    child: ListView.builder(
                        controller: _sc_chat,
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          if (messages[index].role == "user") {
                            return ChatItemMe(
                              message: messages[index].content ?? "",
                            );
                          } else if (messages[index].role == "loading") {
                            return Padding(
                              padding: EdgeInsets.only(bottom: 8.h),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  Image.asset(
                                    Assets.imagesYetakchiAi,
                                    width: 30.w,
                                    height: 30.w,
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(
                                      left: 10.w,
                                      right: 20.w,
                                      top: 10.h,
                                    ),
                                    child: Container(
                                      width: 100,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: cGrayColor0,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(cRadius12.r),
                                          topLeft: Radius.circular(cRadius12.r),
                                          bottomRight:
                                              Radius.circular(cRadius12.r),
                                        ),
                                      ),
                                      child: rive.RiveAnimation.asset(
                                        Assets.riveLoadingDots,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          } else {
                            return ChatItemOther(
                              message: messages[index].content ?? "",
                            );
                          }
                        }),
                  );
                } else if (state is ChatEmpty) {
                  return Expanded(
                      child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                          textAlign: TextAlign.center,
                          LocaleKeys.yetakchi_ai_description.tr()),
                    ),
                  ));
                } else if (state is ChatError) {
                  return Expanded(
                      child: FailureWidget(text: state.message, onTap: () {}));
                } else {
                  return Expanded(child: SizedBox());
                }
              },
            ),
            ClipRRect(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                height: 60.h,
                decoration: BoxDecoration(
                    color: isDark() ? cCardDarkColor : cWhiteColor,
                    boxShadow: [
                      BoxShadow(
                        color: cWhiteColor,
                        spreadRadius: 4,
                        blurRadius: 5,
                        offset: Offset(0, 4), // changes position of shadow
                      ),
                    ]),
                child: Row(
                  children: [
                    Expanded(
                        child: TextField(
                      controller: txtController,
                      decoration: InputDecoration(
                          hintText: LocaleKeys.send_message.tr(),
                          border: InputBorder.none,
                          hintStyle: TextStyle(
                              fontSize: 16.sp,
                              color: isEmpty ? cRedColor : null)),
                    )),
                    InkWell(
                        onTap: () {
                          String text = txtController.text;
                          if (text.isNotEmpty) {
                            messages.add(Chats(role: "user", content: text));
                            messages.add(Chats(role: "loading", content: text));
                            BlocProvider.of<ChatBloc>(context).add(
                                SendMessageEvent(
                                    userId:
                                        sharedPreferences.getString('id') ?? "",
                                    content: text));
                            txtController.clear();
                            scrollDown();
                          } else {
                            setState(() {
                              isEmpty = true;
                            });
                          }
                        },
                        child: SvgPicture.asset(
                          Assets.iconsSend,
                          width: 30.w,
                          height: 30.w,
                        ))
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
