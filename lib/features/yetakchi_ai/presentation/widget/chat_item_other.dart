import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/generated/assets.dart';

class ChatItemOther extends StatefulWidget {
  final String message;
  const ChatItemOther({super.key, required this.message});

  @override
  State<ChatItemOther> createState() => _ChatItemOtherState();
}

class _ChatItemOtherState extends State<ChatItemOther> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              SizedBox(width: 10.w,),
              Image.asset(
                Assets.imagesYetakchiAi,
                width: 30.w,
                height: 30.w,
              ),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 0.7.sw, minWidth: 0.4.sw),
                // Limit to 70% of screen width
                child: Container(
                  margin: EdgeInsets.only(left: 10.w,right: 10.w,top: 8.h),
                  padding: EdgeInsets.only(
                      left: 20.w, right: 20.w, top: 10.h, bottom: 5.h),
                  decoration: BoxDecoration(
                      color: cGrayColor0,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(cRadius12.r),
                        topLeft: Radius.circular(cRadius12.r),
                        bottomRight: Radius.circular(cRadius12.r),
                      ),
                      ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.message,
                        softWrap: true,
                        style: TextStyle(
                            color:
                            cBlackColor,fontSize: 14.sp,fontWeight: FontWeight.bold), // Assuming white text on green background
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );

  }
}
