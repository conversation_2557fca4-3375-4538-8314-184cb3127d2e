import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

class ChatItemMe extends StatefulWidget {
  final String message;

  const ChatItemMe({super.key, required this.message});

  @override
  State<ChatItemMe> createState() => _ChatItemMeState();
}

class _ChatItemMeState extends State<ChatItemMe> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 0.7.sw, minWidth: 0.2.sw),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
            padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(cRadius12.r),
                topLeft: Radius.circular(cRadius12.r),
                bottomLeft: Radius.circular(cRadius12.r),
              ),
              color: cFirstColor,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.message,
                  style: TextStyle(
                      color: cWhiteColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp),
                ),
                SizedBox(
                  height: 10.h,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
