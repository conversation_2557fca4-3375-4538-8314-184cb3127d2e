import 'package:fading_edge_scrollview/fading_edge_scrollview.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class PlayerPage extends StatefulWidget {
  const PlayerPage({super.key});

  @override
  State<PlayerPage> createState() => _PlayerPageState();
}

class _PlayerPageState extends State<PlayerPage> {
  late YoutubePlayerController _controller;

  @override
  void initState() {
    _controller = YoutubePlayerController(
      initialVideoId: 'MBf0cP_FcMg',
      flags: YoutubePlayerFlags(
        showLiveFullscreenButton: false,
        mute: false,
        autoPlay: true,
      ),
    );
    super.initState();
  }

  bool _isPlayerReady = false;

  void deactivate() {
// Pauses video while navigating to next page.
    _controller.pause();
    super.deactivate();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: cFirstColor,
      child: SafeArea(
        child: WillPopScope(
          onWillPop: () async {
            deactivate();
            await setVertical();
            await Future.delayed(Duration(seconds: 1));
            return true;
          },
          child: Scaffold(
            ///Add if you want this page has title
            // appBar: AppBar(
            //   title: Text("Yetakchilar madhiyasi"),
            //   elevation: 0,
            //   backgroundColor: cFirstColor,
            // ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerFloat,
            floatingActionButton: Padding(
              padding: EdgeInsets.only(bottom: 10.h),
              child: SizedBox(
                height: 50.h,
                width: 50.h,
                child: FloatingActionButton(
                  backgroundColor: cSecondColor,
                  onPressed: () async {
                    deactivate();
                    await setVertical();
                    Future.delayed(Duration(seconds: 1))
                        .then((value) => Get.back());
                  },
                  child: SvgPicture.asset(
                    Assets.iconsBackArrowCircle,
                    height: 40.h,
                    width: 40.h,
                  ),
                ),
              ),
            ),
            body: YoutubePlayerBuilder(
              onExitFullScreen: () {
                SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
                    overlays: [
                      SystemUiOverlay.top,
                      SystemUiOverlay.bottom,
                    ]);
              },
              onEnterFullScreen: () {
                ///
              },
              builder: (context, player) => Column(
                children: [
                  ClipRRect(
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(25.r),
                          bottomRight: Radius.circular(25.r)),
                      child: player),
                  Expanded(
                      child: FadingEdgeScrollView.fromSingleChildScrollView(
                    gradientFractionOnEnd: 0.5,
                    child: SingleChildScrollView(
                        padding: EdgeInsets.only(bottom: 80.h),
                        physics: BouncingScrollPhysics(),
                        controller: ScrollController(),
                        child: Padding(
                          padding: EdgeInsets.all(35.h),
                          child: Text(
                            AppStrings.MADHIYA,
                            textAlign: TextAlign.center,
                          ),
                        )),
                  ))
                ],
              ),
              player: YoutubePlayer(
                controller: _controller,
                showVideoProgressIndicator: true,
                progressIndicatorColor: cFirstColor,
                thumbnail: Icon(Icons.abc),
                onEnded: (data) async {
                  deactivate();
                  await setVertical();
                  Future.delayed(Duration(seconds: 1))
                      .then((value) => Get.back());
                },
                topActions: [
                  IconButton(
                      onPressed: () async {
                        deactivate();
                        await setVertical();
                        Future.delayed(Duration(seconds: 1))
                            .then((value) => Get.back());
                      },
                      icon: Icon(
                        Icons.arrow_back_ios,
                        color: cWhiteColor,
                      ))
                ],
                onReady: () {
                  print('Player is ready.');
                  _isPlayerReady = true;
                },
                progressColors: ProgressBarColors(
                  playedColor: cFirstColor,
                  handleColor: cSecondColor,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
