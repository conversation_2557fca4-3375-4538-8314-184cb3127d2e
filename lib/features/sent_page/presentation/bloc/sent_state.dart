part of 'sent_cubit.dart';


enum SentStatus { initial, loading, success, failure,empty}

class SentState extends Equatable {
  final SentStatus status;
  final List<SentModel>? list;
  final String? message;

  SentState({required this.status, this.list, this.message});

  static SentState initial() => SentState(
        status: SentStatus.initial,
      );

  SentState copyWith(
          {SentStatus? status, List<SentModel>? list, String? message}) =>
      SentState(
          status: status ?? this.status,
          list: list ?? this.list,
          message: message);

  @override
  List<Object?> get props => [status, list, message];
}
