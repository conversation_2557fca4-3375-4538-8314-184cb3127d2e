import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/features/sent_page/data/model/sent_model.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'sent_state.dart';

class SentCubit extends Cubit<SentState> {
  final Dio dio;
  final NetworkInfo networkInfo;
  final SharedPreferences prefs;
  final IsarService isarService;

  SentCubit(
      {required this.dio,
      required this.networkInfo,
      required this.prefs,
      required this.isarService})
      : super(SentState(status: SentStatus.initial));

  void getSentList([String? startDate, String? endDate]) async {
    emit(SentState(status: SentStatus.loading));
    if (await networkInfo.isConnected) {
      try {
        Map<String,dynamic> query = {};
        if (startDate != null && endDate != null) {
          query['startDate'] = startDate;
          query['endDate'] = endDate;
        }
        var response = await dio.get(workPath,queryParameters: query);
        if (response.statusCode == 200) {
          List<SentModel> sentModel = listFromSentModel(response.data);
          await isarService.isar.writeTxnSync(() async {
            isarService.isar.sentModels.where().deleteAllSync();
          });
          await isarService.isar.writeTxn(() async {
            isarService.isar.sentModels.putAll(sentModel);
          });
          emit(SentState(status: SentStatus.success, list: sentModel));
        } else {
          List<SentModel> list =
              await isarService.isar.sentModels.where().findAll();
          emit(SentState(status: SentStatus.success, list: list));
        }
      } catch (e) {
        print("Error:$e");
        emit(SentState(
            status: SentStatus.failure,
            message: LocaleKeys.error_download_data.tr()));
      }
    } else {
      try {
        List<SentModel> list =
            await isarService.isar.sentModels.where().findAll();
        emit(SentState(
            status: SentStatus.success,
            message: LocaleKeys.check_internet_connection.tr(),
            list: list));
      } catch (e) {
        emit(SentState(
            status: SentStatus.failure,
            message: LocaleKeys.check_internet_connection.tr()));
      }
    }
  }
}
