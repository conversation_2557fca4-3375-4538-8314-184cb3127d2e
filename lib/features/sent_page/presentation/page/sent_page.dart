import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/failure_widget.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/sent_page/data/model/sent_model.dart';
import 'package:yetakchi/features/sent_page/presentation/bloc/sent_cubit.dart';
import 'package:yetakchi/features/sent_page/presentation/widget/sent_item.dart';
import 'package:yetakchi/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class SentPage extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => di<SentCubit>(),
      child: SentPage(),
    );
  }

  const SentPage({super.key});

  @override
  State<SentPage> createState() => _SentPageState();
}

class _SentPageState extends State<SentPage> {
  var headerTimes = Map<String, dynamic>();
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
  List<SentModel> list = [];
  String? selectedDate;
  late SentCubit sentCubit;
  DateTime? _selectedStartDate;
  DateTime? _selectedEndDate;

  void _onSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    // When a single date is selected
    if (args.value is DateTime) {
      DateTime selectedDate = args.value;

      // Calculate the end date (7 days from the selected date)
      DateTime endDate = selectedDate.add(Duration(days: 6));

      // Check if the end date goes beyond the current month
      // If it does, adjust to the last day of the month
      if (endDate.month != selectedDate.month) {
        endDate = DateTime(selectedDate.year, selectedDate.month + 1, 0);
      }

      setState(() {
        _selectedStartDate = selectedDate;
        _selectedEndDate = endDate;
      });

      print('Start Date: $selectedDate');
      print('End Date: $endDate');
    }
  }

  @override
  void initState() {
    super.initState();
   sentCubit= BlocProvider.of<SentCubit>(context);
   sentCubit.getSentList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            height: 50.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 4,
                    blurRadius: 5,
                    offset: Offset(0, 4), // changes position of shadow
                  ),
                ],
                borderRadius: BorderRadius.circular(cRadius12.r),
                color: isDark() ? cCardDarkColor : cWhiteColor),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedDate == null ? LocaleKeys.select_date.tr() : selectedDate ?? "",
                  style: TextStyle(
                      color: isDark()
                          ? cPrimaryTextDark
                          : selectedDate == null
                              ? cGrayColor1
                              : cBlackColor),
                ),
                Row(
                  children: [
                    Visibility(
                      visible: selectedDate != null,
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            selectedDate = null;
                            BlocProvider.of<SentCubit>(context).getSentList();
                          });
                        },
                        child: Icon(
                          Icons.close,
                          color: isDark() ? cPrimaryTextDark : cFirstColor,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    InkWell(
                      onTap: () {
                        showDialog(
                            context: context,
                            builder: (context) {
                              return Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Dialog(
                                      shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                              cRadius12.r)),
                                      child: ClipRRect(
                                        borderRadius:
                                            BorderRadius.circular(cRadius12.r),
                                        child: SfDateRangePicker(
                                          headerStyle:
                                              DateRangePickerHeaderStyle(
                                                  backgroundColor: isDark()
                                                      ? cFirstTextColor
                                                          .withAlpha(50)
                                                      : cGrayColor1
                                                          .withAlpha(50)),
                                          cancelText: LocaleKeys.cancelling1.tr(),
                                          confirmText: 'Ok',
                                          selectionColor:isDark()?cWhiteColor : Colors.blue,
                                          // startRangeSelectionColor: Colors.blue,
                                          // endRangeSelectionColor: Colors.blue,
                                          onSelectionChanged:
                                              _onSelectionChanged,
                                          maxDate: DateTime.now(),
                                          backgroundColor: isDark()
                                              ? cCardDarkColor
                                              : cWhiteColor,
                                          showActionButtons: true,
                                          enablePastDates: true,
                                          selectionMode:
                                              DateRangePickerSelectionMode
                                                  .single,
                                          monthViewSettings:
                                              DateRangePickerMonthViewSettings(
                                            weekNumberStyle:
                                                DateRangePickerWeekNumberStyle(
                                              backgroundColor:
                                                  Colors.blue.withOpacity(0.1),
                                            ),
                                          ),
                                          onSubmit: (value) {
                                            if (value != null) {
                                              setState(() {
                                                selectedDate = formatterDate
                                                    .format(value as DateTime);
                                              });
                                              sentCubit.getSentList(selectedDate,selectedDate);}
                                            Navigator.pop(context);
                                          },
                                          onCancel: () {
                                            Navigator.pop(context);
                                          },
                                        ),
                                      )),
                                ],
                              );
                            });
                      },
                      child: SvgPicture.asset(
                        Assets.iconsCalendar,
                        width: 24.h,
                        height: 24.h,
                        color: isDark() ? cPrimaryTextDark : cFirstColor,
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
          Expanded(
            child: BlocConsumer<SentCubit, SentState>(
              listener: (context, state) {
                if (state.status == SentStatus.success) {
                  list = sortSentModelsByUploadTime(state.list ?? []);
                }
              },
              builder: (context, state) {
                if (state.status == SentStatus.loading) {
                  return Center(
                    child: CupertinoActivityIndicator(
                      color: Theme.of(context).primaryColor,
                      radius: 30.r,
                    ),
                  );
                } else if (state.status == SentStatus.success) {
                  return list.isEmpty == true
                      ? EmptyListWidget(
                          onTap: () {
                            BlocProvider.of<SentCubit>(context).getSentList();
                          },
                          title: LocaleKeys.notExistSentWork.tr(),
                        )
                      : RefreshIndicator(
                          onRefresh: () async {
                            setState(() {
                              selectedDate = null;
                              BlocProvider.of<SentCubit>(context).getSentList();
                            });
                          },
                          child: ListView.builder(
                              padding: EdgeInsets.only(bottom: 120.h),
                              itemCount: list.length,
                              physics: AlwaysScrollableScrollPhysics(
                                  parent: BouncingScrollPhysics()),
                              itemBuilder: (BuildContext context, int index) {
                                SentModel sentModel = list[index];
                                String time = formatterDate.format(
                                    DateTime.parse(
                                        sentModel.uploadTime.toString()));
                                String id = sentModel.id ?? "-1";
                                if ((!headerTimes.containsKey(time)) ||
                                    headerTimes.containsValue(id)) {
                                  headerTimes[time] = id;
                                  return SentItem(
                                    sentModel: list[index],
                                    visible: true,
                                  );
                                } else {
                                  return SentItem(
                                    sentModel: list[index],
                                    visible: false,
                                  );
                                }
                              }),
                        );
                } else if (state.status == SentStatus.failure) {
                  return Center(
                    child: FailureWidget(
                      text: state.message ?? "",
                      onTap: () {
                        BlocProvider.of<SentCubit>(context).getSentList();
                      },
                    ),
                  );
                } else {
                  return Center(
                    child: FailureWidget(
                      text: state.message ?? "",
                      onTap: () {
                        BlocProvider.of<SentCubit>(context).getSentList();
                      },
                    ),
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  List<SentModel> sortSentModelsByUploadTime(List<SentModel> sentModels) {
    sentModels.sort((a, b) {
      // Parse the uploadTime strings to DateTime objects
      DateTime aUploadTime = DateTime.parse(a.uploadTime!);
      DateTime bUploadTime = DateTime.parse(b.uploadTime!);

      // Compare the DateTime objects
      return bUploadTime.compareTo(aUploadTime);
    });

    return sentModels;
  }
}
