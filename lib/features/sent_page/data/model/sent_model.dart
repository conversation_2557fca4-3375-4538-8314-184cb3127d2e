import 'package:isar/isar.dart';

part 'sent_model.g.dart';

List<SentModel> listFromSentModel(
  data,
) =>
    List<SentModel>.from(data.map((e) => SentModel.fromJson(e)));

@collection
@Name("SentModel")
class SentModel {
  SentModel({
    this.user,
    this.id,
    this.title,
    this.desc,
    this.category,
    this.subCategory,
    this.province,
    this.region,
    this.district,
    this.media,
    this.video,
    this.rate,
    this.rateComment,
    this.rateTime,
    this.public,
    this.uploadTime,
    this.postTime,
    this.workId,
    this.telegPostTime,
    this.telegLink,
    this.telegStatus,
    this.workStatus,
    this.evalutionRole,
    this.createdAt,
    this.updatedAt,
  });

  SentModel.fromJson(dynamic json) {
    user = json['user'] != null ? SentUser.fromJson(json['user']) : null;
    id = json['_id'];
    title = json['title'];
    desc = json['desc'];
    category = json['category'];
    subCategory = json['subCategory'] != null
        ? WorkSubCategory.fromJson(json['subCategory'])
        : null;
    province = json['province'];
    region = json['region'];
    district = json['district'];
    if (json['media'] != null) {
      media = [];
      json['media'].forEach((v) {
        media?.add(Media.fromJson(v));
      });
    }
    video = json['video'] != null ? json['video'].cast<String>() : [];
    rate = json['rate'];
    rateComment = json['rateComment'];
    rateTime = json['rateTime'];
    public = json['public'];
    uploadTime = json['uploadTime'];
    postTime = json['postTime'];
    workId = json['workId'];
    telegPostTime = json['telegPostTime'];
    telegLink = json['telegLink'];
    telegStatus = json['telegStatus'];
    workStatus = json['workStatus'];
    evalutionRole = json['evalutionRole'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Id localId = Isar.autoIncrement;
  SentUser? user;
  String? id;
  String? title;
  String? desc;
  String? category;
  WorkSubCategory? subCategory;
  String? province;
  String? region;
  String? district;
  List<Media>? media;
  List<String>? video;
  int? rate;
  String? rateComment;
  String? rateTime;
  int? public;
  String? uploadTime;
  String? postTime;
  int? workId;
  String? telegPostTime;
  String? telegLink;
  bool? telegStatus;
  int? workStatus;
  String? evalutionRole;
  String? createdAt;
  String? updatedAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (user != null) {
      map['user'] = user?.toJson();
    }
    map['_id'] = id;
    map['title'] = title;
    map['desc'] = desc;
    map['category'] = category;
    if (subCategory != null) {
      map['subCategory'] = subCategory?.toJson();
    }
    map['province'] = province;
    map['region'] = region;
    map['district'] = district;
    if (media != null) {
      map['media'] = media?.map((v) => v.toJson()).toList();
    }
    map['video'] = video;
    map['rate'] = rate;
    map['rateComment'] = rateComment;
    map['rateTime'] = rateTime;
    map['public'] = public;
    map['uploadTime'] = uploadTime;
    map['postTime'] = postTime;
    map['workId'] = workId;
    map['telegPostTime'] = telegPostTime;
    map['telegLink'] = telegLink;
    map['telegStatus'] = telegStatus;
    map['workStatus'] = workStatus;
    map['evalutionRole'] = evalutionRole;
    map['createdAt'] = createdAt;
    map['updatedAt'] = updatedAt;
    return map;
  }
}

@embedded
class Media {
  Media({
    this.image,
    this.thumb,
    this.lat,
    this.lng,
    this.time,
    this.id,
  });

  Media.fromJson(dynamic json) {
    image = json['image'];
    thumb = json['thumb'];
    lat = json['lat'];
    lng = json['lng'];
    time = json['time'];
    id = json['_id'];
  }

  String? image;
  String? thumb;
  double? lat;
  double? lng;
  String? time;
  String? id;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['image'] = image;
    map['thumb'] = thumb;
    map['lat'] = lat;
    map['lng'] = lng;
    map['time'] = time;
    map['_id'] = id;
    return map;
  }
}

@embedded
class WorkSubCategory {
  WorkSubCategory({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,
  });

  WorkSubCategory.fromJson(dynamic json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
  }

  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['titleQQ'] = titleQQ;
    return map;
  }
}

@embedded
class SentUser {
  SentUser({
    this.id,
    this.firstName,
    this.lastName,
    this.middleName,
  });

  SentUser.fromJson(dynamic json) {
    id = json['_id'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    middleName = json['middleName'];
  }

  String? id;
  String? firstName;
  String? lastName;
  String? middleName;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['firstName'] = firstName;
    map['lastName'] = lastName;
    map['middleName'] = middleName;
    return map;
  }
}
