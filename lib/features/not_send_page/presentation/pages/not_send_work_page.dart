import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide FormData, MultipartFile, Trans;
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as p;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sn_progress_dialog/sn_progress_dialog.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/alert_dialog.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/core/widgets/dialog_frame.dart';
import 'package:yetakchi/core/widgets/failure_widget.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/home/<USER>/navigation.dart';
import 'package:yetakchi/features/not_send_page/presentation/bloc/not_sent_bloc/not_send_cubit.dart';
import 'package:yetakchi/features/not_send_page/presentation/widget/fab_widget.dart';
import 'package:yetakchi/features/send_data/models/not_send_model.dart';
import 'package:yetakchi/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class NotSendWorkPage extends StatefulWidget {
  static Widget screen() {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => di<NotSendWorkCubit>(),
        ),
      ],
      child: NotSendWorkPage(),
    );
  }

  const NotSendWorkPage({super.key});

  @override
  State<NotSendWorkPage> createState() => _NotSendWorkPageState();
}

class _NotSendWorkPageState extends State<NotSendWorkPage> {
  late ProgressDialog pd;
  int selectedIndex = -1;
  bool showed = false;
  List<NotSendModel>? data = [];
  Dio dio = di();
  SharedPreferences prefs = di();
  final IsarService isarService = di();
  var ancestralState;
  NotSendModel? currentItem;
  TextEditingController editController = TextEditingController();

  @override
  void initState() {
    super.initState();

    BlocProvider.of<NotSendWorkCubit>(context).getNotSendWorks();
    pd = ProgressDialog(context: context);
  }

  @override
  Widget build(BuildContext context) {
    ancestralState =
        context.findAncestorStateOfType<State<BottomNavigationPage>>();

    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: FabWidget<NotSendModel>(
        sendButtonFunction: sendData,
        selectedIndex: selectedIndex,
        isEditable: true,
        onEditTap: () async {
          currentItem = data?[selectedIndex];
          editController.text = currentItem?.desc ?? '...';

          showDialog(
              context: context,
              builder: (context) => AllDialogSkeleton(
                  color: Theme.of(context).cardTheme.color,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      SizedBox(height: 15.h),
                      TextField(
                        style: Theme.of(context).textTheme.bodyMedium,
                        maxLines: 5,
                        decoration: InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                              vertical: 10.h, horizontal: 10.w),
                          focusedBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10.r)),
                              borderSide: BorderSide(
                                  width: 1.w,
                                  color: Theme.of(context).primaryColor)),
                          enabledBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10.r)),
                              borderSide: BorderSide(
                                  width: 1.w,
                                  color: Theme.of(context).primaryColor)),
                        ),
                        controller: editController,
                      ),
                      SizedBox(height: 15.h),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15.r),
                          ),
                          backgroundColor: cFirstColor,
                          minimumSize: Size.fromHeight(40.h), // NEW
                        ),
                        onPressed: () {
                          editItem(currentItem ?? NotSendModel(),
                              editController.text);
                          Navigator.pop(context);
                        },
                        child: Text(
                          LocaleKeys.editing.tr(),
                          style: TextStyle(
                              fontSize: context.isTablet ? 12.sp : 16.sp),
                        ),
                      )
                    ],
                  ),
                  title: LocaleKeys.editing.tr(),
                  icon: Assets.iconsEdit));
        },
        onDeleteTap: () async {
          var result = await showAlert(context) ?? false;
          if (result) {
            deleteItem(data?[selectedIndex]);
          }
        },
        isMessageShowed: showed,
        notSendList: data,
        messageShowedStatus: (isShowed) {
          showed = isShowed;
        },
      ),
      body: BlocBuilder<NotSendWorkCubit, NotSendWorkState>(
        builder: (context, state) {
          if (state.status == NotSendWorkStatus.loading) {
            return Expanded(
                child: CupertinoActivityIndicator(
                    radius: 30.r, color: Theme.of(context).primaryColor));
          } else if (state.status == NotSendWorkStatus.failure) {
            return FailureWidget(
              text: LocaleKeys.error.tr(),
              onTap: () {
                BlocProvider.of<NotSendWorkCubit>(context).getNotSendWorks();
              },
            );
          } else if (state.status == NotSendWorkStatus.success) {
            data = state.list;
            List<NotSendModel>? list = state.list;

            return ListView.builder(
                itemCount: state.list?.length,
                padding: EdgeInsets.only(bottom: 120.h),
                physics: BouncingScrollPhysics(),
                itemBuilder: (BuildContext context, int index) {
                  // NotSendModel? notSendModel = state.list?[index];

                  var media = list?[index].mediaList?.first.image ?? '';

                  return ZoomTapAnimation(
                    onTap: () {
                      if (selectedIndex == index) {
                        setState(() {
                          selectedIndex = -1;
                        });
                      } else {
                        if (index != 0) {
                          CustomToast.showToast(
                              LocaleKeys.send_from_first.tr());
                        }
                        setState(() {
                          selectedIndex = index;
                        });
                      }
                    },
                    child: Container(
                      margin: EdgeInsets.only(
                          left: 15.w, right: 15.w, bottom: 10.h),
                      padding: EdgeInsets.all(3.w),
                      decoration: BoxDecoration(
                          boxShadow: [boxShadow20],
                          color: selectedIndex == index
                              ? cFirstColor
                              : Theme.of(context).cardTheme.color!,
                          borderRadius: BorderRadius.circular(15.r)),
                      child: Row(
                        children: [
                          Container(
                            margin: EdgeInsets.only(
                                left: 5.w, top: 5.h, bottom: 5.h),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10.r)),
                            height: 80.h,
                            width: 80.h,
                            child: ClipRRect(
                                borderRadius: BorderRadius.circular(10.r),
                                child: Image.memory(
                                  base64Decode(p.extension(media) != '.mp4'
                                      ? media
                                      : ''),
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, a, b) {
                                    return Container(
                                      decoration:
                                          BoxDecoration(color: cFourthColor),
                                      child: Center(
                                        child: SvgPicture.asset(
                                          Assets.iconsCamera1,
                                          height: 35.h,
                                        ),
                                      ),
                                    );
                                  },
                                )),
                          ),
                          Expanded(
                            child: Column(
                              children: [
                                Container(
                                  child: Text(
                                    list?[index].subCategoryTitle ?? "...",
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        color: selectedIndex == index
                                            ? cWhiteColor
                                            : Theme.of(context)
                                                .textTheme
                                                .bodyMedium
                                                ?.color,
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'SemiBold'),
                                    maxLines: 2,
                                  ),
                                  margin: EdgeInsets.only(
                                    left: 10.w,
                                    right: 6.w,
                                    top: 10.h,
                                    bottom: 1.h,
                                  ),
                                ),
                                Container(
                                  child: Text(
                                    list?[index].desc ?? "...",
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        color: selectedIndex == index
                                            ? cWhiteColor
                                            : Theme.of(context)
                                                .textTheme
                                                .bodyMedium
                                                ?.color,
                                        fontSize: 10.sp,
                                        fontFamily: 'SemiBold'),
                                    maxLines: 2,
                                  ),
                                  margin: EdgeInsets.only(
                                    left: 10.w,
                                    right: 6.w,
                                    top: 6.h,
                                    bottom: 2.h,
                                  ),
                                ),
                                Container(
                                  child: Text(
                                    list?[index].mediaList?.first.sana ?? "...",
                                    style: TextStyle(
                                        color: selectedIndex == index
                                            ? cWhiteColor
                                            : Theme.of(context)
                                                .textTheme
                                                .bodyMedium
                                                ?.color,
                                        fontSize: 9.sp,
                                        fontFamily: 'Regular'),
                                    maxLines: 2,
                                  ),
                                  margin: EdgeInsets.only(
                                      left: 10.w,
                                      right: 16.w,
                                      top: 6.h,
                                      bottom: 10.h),
                                ),
                              ],
                              crossAxisAlignment: CrossAxisAlignment.start,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                });
          } else if (state.status == NotSendWorkStatus.emptyList) {
            return EmptyListWidget(
              onTap: () {
                BlocProvider.of<NotSendWorkCubit>(context).getNotSendWorks();
              },
              title: LocaleKeys.not_exist_not_sent_work.tr(),
            );
          } else {
            return Center(
              child: CupertinoActivityIndicator(
                color: Theme.of(context).primaryColor,
                radius: 30.r,
              ),
            );
          }
        },
      ),
    );
  }

  deleteItem(NotSendModel? model) async {
    var mediaList = model?.mediaList ?? [];
    var localId = model?.localId ?? -1;

    try {
      for (var item in mediaList) {
        var media = item.image ?? '';

        if (p.extension(media) == '.mp4') {
          deleteFileFromInternalStorage(media);
        }
      }

      await isarService.isar.writeTxnSync(() {
        var result = isarService.isar.notSendModels.deleteSync(localId);
        return result;
      });
      BlocProvider.of<NotSendWorkCubit>(context).getNotSendWorks();
      ancestralState.setState(() {});
      CustomToast.showToast(LocaleKeys.deleted.tr());
      setState(() {
        selectedIndex = -1;
      });
    } catch (e) {
      CustomToast.showToast("${LocaleKeys.error1.tr()}: $e");
      return false;
    }
  }

  editItem(NotSendModel work, String text) async {
    try {
      await isarService.isar.writeTxn(() {
        work.desc = text;
        var result = isarService.isar.notSendModels.put(work);
        return result;
      });
      BlocProvider.of<NotSendWorkCubit>(context).getNotSendWorks();
      ancestralState.setState(() {});
      CustomToast.showToast(LocaleKeys.edited.tr());
      setState(() {
        selectedIndex = -1;
      });
    } catch (e) {
      CustomToast.showToast("${LocaleKeys.error1.tr()}: $e");
      return false;
    }
  }

  void sendData(NotSendModel notSendModel) async {
    pd.show(
        max: 100,
        msg: "${LocaleKeys.data_uploading.tr()}...",
        msgFontSize: context.isTablet ? 11.sp : 15.sp,
        valueFontSize: context.isTablet ? 11.sp : 15.sp,
        borderRadius: 15.r,
        cancel: Cancel(
            autoHidden: true,
            cancelImageColor: Theme.of(context).textTheme.bodyMedium?.color),
        msgColor: Theme.of(context).textTheme.bodyMedium!.color!,
        valueColor: Theme.of(context).textTheme.bodyMedium!.color!,
        backgroundColor: Theme.of(context).cardTheme.color!);

    var mediaList = notSendModel.mediaList ?? [];

    ///Getting location and date (no need for olds)

    var lat = mediaList.last.latLang!.split(",").first;
    var lng = mediaList.last.latLang!.split(",").last;
    var now = DateTime.now();
    var time =
        mediaList.last.sana ?? (DateFormat('yyyy-MM-dd HH:mm:ss').format(now));
    var videoName;

    List<MultipartFile> files = [];

    for (var item in mediaList) {
      var media = item.image ?? '';

      if (p.extension(media) == '.mp4') {
        videoName = media;
      }

      MultipartFile file = await MultipartFile.fromBytes(
        p.extension(media) != '.mp4'
            ? base64Decode(media)
            : File(media).readAsBytesSync(),
        filename: p.extension(media) != '.mp4' ? media.substring(0, 8) : media,
        contentType: p.extension(media) != '.mp4'
            ? MediaType('image', 'jpeg')
            : MediaType('video', 'mp4'),
      );

      ///For separating video
      // if (lookupMimeType('', headerBytes: base64Decode(item.image!)) !=
      //     'image/jpeg') {
      //   video = file;
      // } else {
      //   files.add(file);
      // }

      files.add(file);
    }

    FormData formData;

    var data = {
      'lat': lat,
      'lng': lng,
      'files': files,
      // 'video': video,
      'sound': notSendModel.sound,
      'individual': notSendModel.individual,
      'public': notSendModel.publicCount,
      'desc': notSendModel.desc,
      'category': notSendModel.categoryId,
      'subCategory': notSendModel.subCategoryId,
      'supportPerson': notSendModel.supportedPerson,
      'supportType': notSendModel.supportType,
      'status': notSendModel.status,
      'uploadTime': time,
    };

    ///TODO: Find alternative
    data.removeWhere((key, value) => value.isNull);

    formData = FormData.fromMap(data);
    print(formData.fields);

    try {
      Options options = Options(
        receiveDataWhenStatusError: true,
        headers: {
          "Content-Type": "multipart/form-data",
          "Accept": "application/json",
        },
      );

      final response = await dio.post(
        workPath,
        data: formData,
        options: options,
        onSendProgress: (int sent, int total) {
          print('$sent/$total');
          ///TODO: Test
          pd.update(value: ((sent / total) * 100).round() - 1);
        },
      );

      // print(response.data);
      if (response.statusCode == 200) {
        pd.close();

        CustomToast.showToast(LocaleKeys.data_uploaded.tr());

        BlocProvider.of<NotSendWorkCubit>(context)
            .getNotSendWorks(id: notSendModel.localId);

        ///Refresh the badge
        ancestralState?.setState(() {
          print("Badge is refreshed");
        });

        setState(() {
          selectedIndex = -1;
        });

        if (videoName != null) {
          deleteFileFromInternalStorage(videoName);
        }
      } else {
        pd.close();
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        pd.close();
        if (e.response != null) {
          pd.close();
          try {
            CustomToast.showToast(
                "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}");
          } catch (e) {
            CustomToast.showToast('Parsing error of RAW data');
          }
        }
        return;
      }
      if (e.type == DioExceptionType.connectionTimeout) {
        pd.close();
        CustomToast.showToast("Connection timeout");
        print('check your connection');
        return;
      }

      if (e.type == DioExceptionType.receiveTimeout) {
        pd.close();
        CustomToast.showToast("Receive timeout");
        print('unable to connect to the server');
        return;
      }

      if (e.type == DioExceptionType.unknown) {
        pd.close();
        CustomToast.showToast("Something went wrong");
        print('Something went wrong');
        return;
      }
      pd.close();
    } catch (e) {
      CustomToast.showToast(
          "${LocaleKeys.error_in_uploading.tr()}.. ${LocaleKeys.server_form_error.tr()}");
      pd.close();
    }
  }
}
