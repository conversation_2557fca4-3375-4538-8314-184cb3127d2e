import 'dart:convert';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans, MultipartFile, FormData;
import 'package:http_parser/http_parser.dart';
import 'package:sn_progress_dialog/sn_progress_dialog.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/alert_dialog.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/core/widgets/failure_widget.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/home/<USER>/navigation.dart';
import 'package:yetakchi/features/not_send_page/presentation/bloc/not_sent_bloc/not_send_cubit.dart';
import 'package:yetakchi/features/not_send_page/presentation/widget/fab_widget.dart';
import 'package:yetakchi/features/not_send_page/presentation/widget/not_send_task_widget.dart';
import 'package:yetakchi/features/task_send/data/model/task_img_model.dart';
import 'package:yetakchi/features/tasks/data/model/user.dart';
import 'package:yetakchi/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class NotSendTaskPage extends StatefulWidget {
  static Widget screen() {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => di<NotSendTaskCubit>(),
        ),
      ],
      child: NotSendTaskPage(),
    );
  }

  const NotSendTaskPage({super.key});

  @override
  State<NotSendTaskPage> createState() => _NotSendTaskPageState();
}

class _NotSendTaskPageState extends State<NotSendTaskPage> {
  late ProgressDialog pd;
  int selectedIndex = -1;
  bool showed = false;
  List<TaskImgModel>? data = [];
  var ancestralState;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<NotSendTaskCubit>(context).getNotSendTasks();
    pd = ProgressDialog(context: context);
  }

  @override
  Widget build(BuildContext context) {
    ancestralState =
        context.findAncestorStateOfType<State<BottomNavigationPage>>();
    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: FabWidget<TaskImgModel>(
        sendButtonFunction: sendTask,
        selectedIndex: selectedIndex,
        isEditable: false,
        onEditTap: () {
          CustomToast.showToast('Cannot edit :(');
        },
        onDeleteTap: () async {
          var result = await showAlert(context) ?? false;
          if (result) {
            deleteItem(data?[selectedIndex].localId ?? -1);
          }
        },
        isMessageShowed: showed,
        notSendList: data,
        messageShowedStatus: (isShowed) {
          showed = isShowed;
        },
      ),
      body: BlocBuilder<NotSendTaskCubit, NotSendTaskState>(
        builder: (context, state) {
          if (state.status == NotSendTaskStatus.loading) {
            return Expanded(
                child: CupertinoActivityIndicator(
                    radius: 30.r, color: Theme.of(context).primaryColor));
          } else if (state.status == NotSendTaskStatus.success) {
            data = state.list;
            List<TaskImgModel>? list = state.list;
            return ListView.builder(
                itemCount: state.list?.length,
                padding: EdgeInsets.only(bottom: 120.h),
                physics: BouncingScrollPhysics(),
                itemBuilder: (BuildContext context, int index) {
                  TaskImgModel? taskImgModel = state.list?[index];
                  return NotSendTaskWidget(
                    withPhoto: taskImgModel?.withPhoto ?? false,
                    withVideo: taskImgModel?.withVideo ?? false,
                    date: taskImgModel?.recieveDate ?? "1970-01-01",
                    withText: taskImgModel?.withText ?? false,
                    title: taskImgModelTitle(taskImgModel: state.list?[index]),
                    desc: taskImgModel?.text ?? "",
                    onTap: () {
                      if (selectedIndex == index) {
                        setState(() {
                          selectedIndex = -1;
                        });
                      } else {
                        if (index != 0) {
                          CustomToast.showToast(
                              LocaleKeys.send_from_first.tr());
                        }
                        setState(() {
                          selectedIndex = index;
                        });
                      }
                    },
                    color: selectedIndex == index
                        ? cFirstColor
                        : Theme.of(context).cardTheme.color!,
                    selected: selectedIndex == index,
                    user: taskImgModel?.user ?? User(),
                  );
                });
          } else if (state.status == NotSendTaskStatus.emptyList) {
            return EmptyListWidget(
              onTap: () {
                BlocProvider.of<NotSendTaskCubit>(context).getNotSendTasks();
              },
              title: LocaleKeys.not_exist_not_sent_task.tr(),
            );
          } else if (state.status == NotSendTaskStatus.failure) {
            return FailureWidget(
              text: LocaleKeys.error.tr(),
              onTap: () {
                BlocProvider.of<NotSendTaskCubit>(context).getNotSendTasks();
              },
            );
          } else {
            return SizedBox();
          }
        },
      ),
    );
  }

  deleteItem(int id) {
    try {
      BlocProvider.of<NotSendTaskCubit>(context).deleteNotSendTask(id);
      setState(() {
        selectedIndex = -1;
      });
      ancestralState.setState(() {});
      CustomToast.showToast(LocaleKeys.deleted.tr());
    } catch (e) {
      CustomToast.showToast("${LocaleKeys.error1.tr()}: $e");
      return false;
    }
  }

  void sendTask(TaskImgModel taskImgModel) async {
    pd.show(
        max: 100,
        msg: "${LocaleKeys.data_uploading.tr()}...",
        msgFontSize: context.isTablet ? 13.sp : 17.sp,
        valueFontSize: context.isTablet ? 11.sp : 15.sp,
        borderRadius: 15.r,
        cancel: Cancel(
            autoHidden: true,
            cancelImageColor: Theme.of(context).textTheme.bodyMedium?.color),
        msgColor: Theme.of(context).textTheme.bodyMedium!.color!,
        valueColor: Theme.of(context).textTheme.bodyMedium!.color!,
        backgroundColor: Theme.of(context).cardTheme.color!);
    Dio dio = di();
    FormData formData;

    var data = <String, dynamic>{
      "uploadTime": taskImgModel.sendDate,
      "lat": taskImgModel.lat,
      "lng": taskImgModel.lng,
      "specialTask": taskImgModel.taskId
    };

    if (taskImgModel.image != null) {
      List<int> bytes = base64Decode(taskImgModel.image!);

      MultipartFile imageFile = MultipartFile.fromBytes(
        Uint8List.fromList(bytes),
        filename: taskImgModel.image!.substring(0, 8),
        contentType: MediaType(
            'image', 'jpeg'), // Set the content type based on your requirements
      );

      data["files"] = imageFile;
    }

    if (taskImgModel.video != null) {
      List<int> bytes = base64Decode(taskImgModel.video!);

      MultipartFile videoFile = MultipartFile.fromBytes(
          Uint8List.fromList(bytes),
          filename: taskImgModel.video!.substring(0, 8),
          contentType: MediaType('video', 'mp4'));
      data["files"] = videoFile;
    }

    if (taskImgModel.text != null && taskImgModel.text?.isNotEmpty == true) {
      data['desc'] = taskImgModel.text;
    }
    formData = FormData.fromMap(data);
    try {
      Options options = Options(
        receiveDataWhenStatusError: true,
        headers: {
          "Content-Type": "multipart/form-data",
          "Accept": "application/json",
        },
        receiveTimeout: Duration(seconds: 60),
        sendTimeout: Duration(seconds: 60),
      );
      final response = await dio.post(specialWorkPath, data: formData,
          onSendProgress: (int sent, int total) {
        pd.update(value: (sent / total * 100).round() - 1);
      }, options: options);

      if (response.statusCode == 200) {
        pd.close();
        CustomToast.showToast(LocaleKeys.data_uploaded.tr());
        BlocProvider.of<NotSendTaskCubit>(context)
            .getNotSendTasks(id: taskImgModel.taskId!);
        setState(() {
          selectedIndex = -1;
        });
      } else {
        pd.close();
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        pd.close();
        if (e.response != null) {
          pd.close();
          try {
            CustomToast.showToast(
                "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}");
          } catch (e) {
            CustomToast.showToast('Parsing error of RAW data');
          }
        }
        return;
      }
      if (e.type == DioExceptionType.connectionTimeout) {
        pd.close();
        CustomToast.showToast("Connection timeout");
        print('check your connection');
        return;
      }
      if (e.type == DioExceptionType.receiveTimeout) {
        pd.close();
        CustomToast.showToast("Receive timeout");
        print('unable to connect to the server');
        return;
      }

      if (e.type == DioExceptionType.unknown) {
        pd.close();
        CustomToast.showToast("Something went wrong");
        print('Something went wrong');
        return;
      }

      if (e.response?.statusCode == 400) {
        try {
          CustomToast.showToast("${e.response?.data['message']}");
        } catch (e) {
          CustomToast.showToast("Something went wrong");
          print('Something went wrong');
        }
      }
      pd.close();
    } catch (e) {
      CustomToast.showToast(
          "${LocaleKeys.error_download_data.tr()}.. ${LocaleKeys.server_form_error.tr()}");
      pd.close();
    }
  }
}
