
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/payments/payment_res.dart';
import 'package:yetakchi/features/payments/payments.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

///TODO: Loading for image.memory

class FabWidget<T> extends StatefulWidget {
  final int selectedIndex;
  final VoidCallback onEditTap;
  final VoidCallback onDeleteTap;
  final bool isMessageShowed;
  final Function(bool) messageShowedStatus;
  final void Function(T) sendButtonFunction;
  final List<T>? notSendList;
  final bool isEditable;

  FabWidget({
    required this.selectedIndex,
    required this.onEditTap,
    required this.onDeleteTap,
    required this.isMessageShowed,
    required this.sendButtonFunction,
    required this.notSendList,
    required this.messageShowedStatus,
    required this.isEditable,
  });

  @override
  _FabWidgetState createState() => _FabWidgetState<T>();
}

class _FabWidgetState<T> extends State<FabWidget<T>> {
  @override
  void initState() {
    checkPaymentInitFunc();
    // checkPaymentInitDirectFunc();
    super.initState();
  }

  late Future<dynamic> _paymentStatusInit;
  final Dio dio = di();
  SharedPreferences prefs = di();
  final NetworkInfo networkInfo = di();

  Future<bool> direct() async {
    return Future.value(true);
  }

  checkPaymentInitFunc() {
    setState(() {
      _paymentStatusInit = checkUserPayment();
    });
  }

  checkPaymentInitDirectFunc() {
    setState(() {
      _paymentStatusInit = direct();
    });
  }

  showPaymentMessages(dynamic value) {
    OverlayState? state = Overlay.of(context);
    PaymentRes? paymentStatus;
    if (value.runtimeType == PaymentRes) {
      paymentStatus = value;

      var status = paymentStatus?.status ?? false;

      if (paymentStatus != null && !status) {
        WidgetsBinding.instance.addPostFrameCallback((time) {
          showTopSnackBar(
            state,
            CustomSnackBar.error(
              textScaleFactor: context.isTablet ? 1.5 : 1,
              message: payResModelTitle(paymentRes: paymentStatus),
            ),
          );
        });
      }
    }
    if (value.runtimeType == bool) {
      if (value == false) {
        WidgetsBinding.instance.addPostFrameCallback((time) {
          showTopSnackBar(
            state,
            CustomSnackBar.error(
              textScaleFactor: context.isTablet ? 1.5 : 1,
              message: LocaleKeys.check_internet_connection.tr(),
            ),
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 20.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AnimatedScale(
            scale: widget.selectedIndex != -1 ? 1 : 0,
            duration: const Duration(milliseconds: 200),
            child: Row(
              children: [
                Visibility(
                  visible: widget.isEditable,
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: Padding(
                      padding: EdgeInsets.only(right: 10.w),
                      child: SizedBox(
                        width: 45.h,
                        height: 45.h,
                        child: FloatingActionButton(
                            heroTag: "btn1",
                            onPressed: widget.onEditTap,
                            backgroundColor: cWhiteColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(14.r),
                            ),
                            child: SvgPicture.asset(
                              Assets.iconsEdit,
                              color: cFirstColor,
                              height: 22.h,
                              width: 22.h,
                            )),
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.bottomRight,
                  child: SizedBox(
                    width: 45.h,
                    height: 45.h,
                    child: FloatingActionButton(
                        heroTag: "btn2",
                        onPressed: widget.onDeleteTap,
                        backgroundColor: cWhiteColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14.r),
                        ),
                        child: SvgPicture.asset(
                          Assets.iconsRecycle,
                          color: cRedColor,
                          height: 22.h,
                          width: 22.h,
                        )),
                  ),
                ),
              ],
            ),
          ),
          AnimatedScale(
            scale: widget.selectedIndex == 0 ? 1 : 0,
            duration: const Duration(milliseconds: 200),
            child: Align(
              alignment: Alignment.bottomRight,
              child: SizedBox(
                width: 120.w,
                height: 45.h,
                child: FutureBuilder<dynamic>(
                  future: _paymentStatusInit,
                  builder: (context, snapshot) {
                    PaymentRes? paymentStatus;
                    bool? hasInternet = true;
                    if (snapshot.data.runtimeType == PaymentRes) {
                      paymentStatus = snapshot.data;
                    }

                    if (snapshot.data.runtimeType == bool) {
                      hasInternet = snapshot.data;
                    }

                    switch (snapshot.connectionState) {
                      case ConnectionState.waiting:
                        {
                          return FloatingActionButton(
                              heroTag: "btn3",
                              onPressed: () {},
                              backgroundColor: cFirstColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(14.r),
                              ),
                              child: Center(
                                  child: CupertinoActivityIndicator(
                                color: Theme.of(context).indicatorColor,
                              )));
                        }
                      default:
                        if (snapshot.hasError)
                          return FloatingActionButton(
                              heroTag: "btn4",
                              onPressed: () {
                                launchCustomUrl(SUPPORT_TG);
                              },
                              backgroundColor: cRedColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(14.r),
                              ),
                              child: Text(
                                "Error!",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    color: cWhiteColor,
                                    fontSize: 18.sp,
                                    fontFamily: 'Regular'),
                              ));
                        else {
                          if (!widget.isMessageShowed) {
                            showPaymentMessages(snapshot.data);
                            widget.messageShowedStatus(true);
                          }
                          return FloatingActionButton(
                              heroTag: "btn5",
                              onPressed: paymentStatus != null &&
                                      paymentStatus.status == true
                                  ? () async {
                                      if (await networkInfo.isConnected) {
                                        if ((widget.notSendList?.isNotEmpty ??
                                                false) &&
                                            widget.selectedIndex != -1) {
                                          widget.sendButtonFunction(
                                              widget.notSendList![
                                                  widget.selectedIndex]);
                                        } else {
                                          debugPrint("Empty data");
                                        }
                                      } else {
                                        CustomToast.showToast(LocaleKeys
                                            .check_internet_connection
                                            .tr());
                                      }
                                    }
                                  : paymentStatus == null && hasInternet == true
                                      ? () async {
                                          launchCustomUrl(SUPPORT_TG);
                                        }
                                      : hasInternet == false
                                          ? () async {
                                              checkPaymentInitFunc();
                                            }
                                          : () async {
                                              Navigator.push(
                                                context,
                                                CupertinoPageRoute(
                                                    builder: (context) =>
                                                        const PaymentsPage()),
                                              ).then((value) =>
                                                  checkPaymentInitFunc());
                                            },
                              backgroundColor: paymentStatus != null &&
                                      paymentStatus.status == true
                                  ? cFirstColor
                                  : cPurpleColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(14.r),
                              ),
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 8.w),
                                child: Text(
                                  paymentStatus != null &&
                                          paymentStatus.status == true
                                      ? LocaleKeys.sending.tr()
                                      : paymentStatus == null &&
                                              hasInternet == true
                                          ? "Error (Admin)"
                                          : hasInternet == false
                                              ? LocaleKeys.refresh.tr()
                                              : LocaleKeys.make_payment.tr(),
                                  maxLines: 2,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: cWhiteColor,
                                      fontSize: 14.sp,
                                      fontFamily: 'Regular'),
                                ),
                              ));
                        }
                    }
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
