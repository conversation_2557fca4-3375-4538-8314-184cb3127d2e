part of 'not_send_cubit.dart';

enum NotSendTaskStatus { initial, loading, success, failure, emptyList }

class NotSendTaskState extends Equatable {
  final NotSendTaskStatus status;
  final List<TaskImgModel>? list;

  NotSendTaskState({required this.status, this.list});

  static NotSendTaskState initial() =>
      NotSendTaskState(status: NotSendTaskStatus.initial, list: null);

  NotSendTaskState copyWith({
    NotSendTaskStatus? status,
    List<TaskImgModel>? list,
  }) =>
      NotSendTaskState(
        status: status ?? this.status,
        list: list ?? this.list,
      );

  @override
  List<Object?> get props => [status, list];
}

///

enum NotSendWorkStatus { initial, loading, success, failure, emptyList }

class NotSendWorkState extends Equatable {
  final NotSendWorkStatus status;
  final List<NotSendModel>? list;

  NotSendWorkState({required this.status, this.list});

  static NotSendWorkState initial() =>
      NotSendWorkState(status: NotSendWorkStatus.initial, list: null);

  NotSendWorkState copyWith({
    NotSendWorkStatus? status,
    List<NotSendModel>? list,
  }) =>
      NotSendWorkState(
        status: status ?? this.status,
        list: list ?? this.list,
      );

  @override
  List<Object?> get props => [status, list];
}
