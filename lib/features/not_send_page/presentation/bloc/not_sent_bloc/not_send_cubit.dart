import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/features/send_data/models/not_send_model.dart';
import 'package:yetakchi/features/task_send/data/model/task_img_model.dart';

part 'not_send_state.dart';

class NotSendTaskCubit extends Cubit<NotSendTaskState> {
  final IsarService isarService;

  NotSendTaskCubit({required this.isarService})
      : super(NotSendTaskState(
          status: NotSendTaskStatus.initial,
        ));

  void deleteNotSendTask(int id) async {
    List<TaskImgModel> list = [];

    try {
      await isarService.isar.writeTxnSync(() async {
        isarService.isar.taskImgModels
            .filter()
            .localIdEqualTo(id)
            .deleteAllSync();
      });
      list = await isarService.isar.taskImgModels.where().findAll();

      if (list.isEmpty) {
        emit(NotSendTaskState(status: NotSendTaskStatus.emptyList));
      } else {
        emit(NotSendTaskState(status: NotSendTaskStatus.success, list: list));
      }
    } catch (e) {
      emit(NotSendTaskState(status: NotSendTaskStatus.failure));
    }
  }

  void getNotSendTasks({String? id}) async {
    List<TaskImgModel> list = [];
    emit(NotSendTaskState.initial());
    try {
      if (id != null) {
        await isarService.isar.writeTxnSync(() async {
          isarService.isar.taskImgModels
              .filter()
              .taskIdEqualTo(id)
              .deleteAllSync();
        });
      }
      list = await isarService.isar.taskImgModels.where().findAll();
      print(list);
      if (list.isEmpty) {
        emit(NotSendTaskState(status: NotSendTaskStatus.emptyList));
      } else {
        emit(NotSendTaskState(status: NotSendTaskStatus.success, list: list));
      }
    } catch (e) {
      emit(NotSendTaskState(status: NotSendTaskStatus.failure));
    }
  }
}

class NotSendWorkCubit extends Cubit<NotSendWorkState> {
  final IsarService isarService;

  NotSendWorkCubit({required this.isarService})
      : super(NotSendWorkState(
    status: NotSendWorkStatus.initial,
  ));

  void getNotSendWorks({int? id}) async {
    List<NotSendModel> list = [];
    emit(NotSendWorkState.initial());
    try {
      if (id != null) {
        await isarService.isar.writeTxnSync(() async {
          isarService.isar.notSendModels
              .filter()
              .localIdEqualTo(id)
              .deleteAllSync();
        });
      }

      list = await isarService.isar.notSendModels.where().findAll();

      if (list.isEmpty) {
        emit(NotSendWorkState(status: NotSendWorkStatus.emptyList));
      } else {
        emit(NotSendWorkState(status: NotSendWorkStatus.success, list: list));
      }
    } catch (e, s) {
      print("catch: $e");
      CustomToast.showToast(e.toString());
      FirebaseCrashlytics.instance.recordError(e, s, fatal: true);
      emit(NotSendWorkState(status: NotSendWorkStatus.failure));
    }
  }
}
