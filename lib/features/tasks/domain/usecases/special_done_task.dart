import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/usescases/usecase.dart';
import 'package:yetakchi/features/tasks/data/model/special_done_task.dart';
import 'package:yetakchi/features/tasks/domain/repository/special_done_task_repository.dart';

class SpecialDoneTaskUseCase
    extends UseCase<SpecialDoneTask, SpecialDoneTaskParams> {
  final SpecialDoneTaskRepository specialDoneTaskRepository;

  SpecialDoneTaskUseCase({required this.specialDoneTaskRepository});

  @override
  Future<Either<Failure, SpecialDoneTask>> call(SpecialDoneTaskParams params) {
    return specialDoneTaskRepository.getSpecialNewTasks(
        pageKey: params.pageKey, refresh: params.refresh);
  }
}

class SpecialDoneTaskParams extends Equatable {
  final int pageKey;
  final bool refresh;

  SpecialDoneTaskParams({required this.pageKey,required this.refresh});

  @override
  List<Object?> get props => [pageKey,refresh];
}