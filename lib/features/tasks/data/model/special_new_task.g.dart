// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'special_new_task.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetSpecialNewTaskItemCollection on Isar {
  IsarCollection<SpecialNewTaskItem> get specialNewTaskItems =>
      this.collection();
}

const SpecialNewTaskItemSchema = CollectionSchema(
  name: r'SpecialNewTask',
  id: -6296954074254393707,
  properties: {
    r'createdAt': PropertySchema(
      id: 0,
      name: r'createdAt',
      type: IsarType.string,
    ),
    r'date': PropertySchema(
      id: 1,
      name: r'date',
      type: IsarType.string,
    ),
    r'desc': PropertySchema(
      id: 2,
      name: r'desc',
      type: IsarType.string,
    ),
    r'file': PropertySchema(
      id: 3,
      name: r'file',
      type: IsarType.string,
    ),
    r'forReference': PropertySchema(
      id: 4,
      name: r'forReference',
      type: IsarType.bool,
    ),
    r'id': PropertySchema(
      id: 5,
      name: r'id',
      type: IsarType.string,
    ),
    r'role': PropertySchema(
      id: 6,
      name: r'role',
      type: IsarType.string,
    ),
    r'telegramGroup': PropertySchema(
      id: 7,
      name: r'telegramGroup',
      type: IsarType.bool,
    ),
    r'titleQQ': PropertySchema(
      id: 8,
      name: r'titleQQ',
      type: IsarType.string,
    ),
    r'titleRU': PropertySchema(
      id: 9,
      name: r'titleRU',
      type: IsarType.string,
    ),
    r'titleUZ': PropertySchema(
      id: 10,
      name: r'titleUZ',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 11,
      name: r'updatedAt',
      type: IsarType.string,
    ),
    r'user': PropertySchema(
      id: 12,
      name: r'user',
      type: IsarType.object,
      target: r'User',
    ),
    r'withFile': PropertySchema(
      id: 13,
      name: r'withFile',
      type: IsarType.bool,
    ),
    r'withPhoto': PropertySchema(
      id: 14,
      name: r'withPhoto',
      type: IsarType.bool,
    ),
    r'withText': PropertySchema(
      id: 15,
      name: r'withText',
      type: IsarType.bool,
    ),
    r'withVideo': PropertySchema(
      id: 16,
      name: r'withVideo',
      type: IsarType.bool,
    )
  },
  estimateSize: _specialNewTaskItemEstimateSize,
  serialize: _specialNewTaskItemSerialize,
  deserialize: _specialNewTaskItemDeserialize,
  deserializeProp: _specialNewTaskItemDeserializeProp,
  idName: r'localId',
  indexes: {},
  links: {},
  embeddedSchemas: {r'User': UserSchema},
  getId: _specialNewTaskItemGetId,
  getLinks: _specialNewTaskItemGetLinks,
  attach: _specialNewTaskItemAttach,
  version: '3.1.0+1',
);

int _specialNewTaskItemEstimateSize(
  SpecialNewTaskItem object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.createdAt;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.date;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.desc;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.file;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.id;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.role;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleQQ;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleRU;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleUZ;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.updatedAt;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.user;
    if (value != null) {
      bytesCount +=
          3 + UserSchema.estimateSize(value, allOffsets[User]!, allOffsets);
    }
  }
  return bytesCount;
}

void _specialNewTaskItemSerialize(
  SpecialNewTaskItem object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.createdAt);
  writer.writeString(offsets[1], object.date);
  writer.writeString(offsets[2], object.desc);
  writer.writeString(offsets[3], object.file);
  writer.writeBool(offsets[4], object.forReference);
  writer.writeString(offsets[5], object.id);
  writer.writeString(offsets[6], object.role);
  writer.writeBool(offsets[7], object.telegramGroup);
  writer.writeString(offsets[8], object.titleQQ);
  writer.writeString(offsets[9], object.titleRU);
  writer.writeString(offsets[10], object.titleUZ);
  writer.writeString(offsets[11], object.updatedAt);
  writer.writeObject<User>(
    offsets[12],
    allOffsets,
    UserSchema.serialize,
    object.user,
  );
  writer.writeBool(offsets[13], object.withFile);
  writer.writeBool(offsets[14], object.withPhoto);
  writer.writeBool(offsets[15], object.withText);
  writer.writeBool(offsets[16], object.withVideo);
}

SpecialNewTaskItem _specialNewTaskItemDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = SpecialNewTaskItem(
    createdAt: reader.readStringOrNull(offsets[0]),
    date: reader.readStringOrNull(offsets[1]),
    desc: reader.readStringOrNull(offsets[2]),
    file: reader.readStringOrNull(offsets[3]),
    forReference: reader.readBoolOrNull(offsets[4]),
    id: reader.readStringOrNull(offsets[5]),
    role: reader.readStringOrNull(offsets[6]),
    telegramGroup: reader.readBoolOrNull(offsets[7]),
    titleQQ: reader.readStringOrNull(offsets[8]),
    titleRU: reader.readStringOrNull(offsets[9]),
    titleUZ: reader.readStringOrNull(offsets[10]),
    updatedAt: reader.readStringOrNull(offsets[11]),
    user: reader.readObjectOrNull<User>(
      offsets[12],
      UserSchema.deserialize,
      allOffsets,
    ),
    withFile: reader.readBoolOrNull(offsets[13]),
    withPhoto: reader.readBoolOrNull(offsets[14]),
    withText: reader.readBoolOrNull(offsets[15]),
    withVideo: reader.readBoolOrNull(offsets[16]),
  );
  object.localId = id;
  return object;
}

P _specialNewTaskItemDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readBoolOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readBoolOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readObjectOrNull<User>(
        offset,
        UserSchema.deserialize,
        allOffsets,
      )) as P;
    case 13:
      return (reader.readBoolOrNull(offset)) as P;
    case 14:
      return (reader.readBoolOrNull(offset)) as P;
    case 15:
      return (reader.readBoolOrNull(offset)) as P;
    case 16:
      return (reader.readBoolOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _specialNewTaskItemGetId(SpecialNewTaskItem object) {
  return object.localId;
}

List<IsarLinkBase<dynamic>> _specialNewTaskItemGetLinks(
    SpecialNewTaskItem object) {
  return [];
}

void _specialNewTaskItemAttach(
    IsarCollection<dynamic> col, Id id, SpecialNewTaskItem object) {
  object.localId = id;
}

extension SpecialNewTaskItemQueryWhereSort
    on QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QWhere> {
  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterWhere>
      anyLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension SpecialNewTaskItemQueryWhere
    on QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QWhereClause> {
  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterWhereClause>
      localIdEqualTo(Id localId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localId,
        upper: localId,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterWhereClause>
      localIdNotEqualTo(Id localId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterWhereClause>
      localIdGreaterThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localId, includeLower: include),
      );
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterWhereClause>
      localIdLessThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localId, includeUpper: include),
      );
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterWhereClause>
      localIdBetween(
    Id lowerLocalId,
    Id upperLocalId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalId,
        includeLower: includeLower,
        upper: upperLocalId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension SpecialNewTaskItemQueryFilter
    on QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QFilterCondition> {
  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'createdAt',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      createdAtIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'createdAt',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'date',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'date',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      dateIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'date',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'desc',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'desc',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'desc',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'desc',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'desc',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      descIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'desc',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'file',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'file',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'file',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'file',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'file',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      fileIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'file',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      forReferenceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'forReference',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      forReferenceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'forReference',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      forReferenceEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'forReference',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      localIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      localIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      localIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      localIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'role',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'role',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'role',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'role',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'role',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      roleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'role',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      telegramGroupIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'telegramGroup',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      telegramGroupIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'telegramGroup',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      telegramGroupEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'telegramGroup',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleQQ',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleQQ',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleQQ',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleQQ',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleQQ',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleQQIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleQQ',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleRU',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleRU',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRULessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleRU',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleRU',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleRU',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleRUIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleRU',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleUZ',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleUZ',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleUZ',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleUZ',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleUZ',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      titleUZIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleUZ',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'updatedAt',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      updatedAtIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'updatedAt',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      userIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'user',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      userIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'user',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withFileIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withFile',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withFileIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withFile',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withFileEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withFile',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withPhotoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withPhoto',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withPhotoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withPhoto',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withPhotoEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withPhoto',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withTextIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withText',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withTextIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withText',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withTextEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withText',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withVideoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withVideo',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withVideoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withVideo',
      ));
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      withVideoEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withVideo',
        value: value,
      ));
    });
  }
}

extension SpecialNewTaskItemQueryObject
    on QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QFilterCondition> {
  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterFilterCondition>
      user(FilterQuery<User> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'user');
    });
  }
}

extension SpecialNewTaskItemQueryLinks
    on QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QFilterCondition> {}

extension SpecialNewTaskItemQuerySortBy
    on QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QSortBy> {
  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByDescDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'file', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'file', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByForReference() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'forReference', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByForReferenceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'forReference', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByRole() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'role', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByRoleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'role', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByTelegramGroup() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'telegramGroup', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByTelegramGroupDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'telegramGroup', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByTitleQQ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByTitleQQDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByTitleRU() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByTitleRUDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByTitleUZ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByTitleUZDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByWithFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withFile', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByWithFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withFile', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByWithPhoto() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByWithPhotoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByWithText() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByWithTextDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByWithVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      sortByWithVideoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.desc);
    });
  }
}

extension SpecialNewTaskItemQuerySortThenBy
    on QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QSortThenBy> {
  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByDescDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'file', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'file', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByForReference() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'forReference', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByForReferenceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'forReference', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByLocalIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByRole() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'role', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByRoleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'role', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByTelegramGroup() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'telegramGroup', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByTelegramGroupDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'telegramGroup', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByTitleQQ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByTitleQQDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByTitleRU() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByTitleRUDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByTitleUZ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByTitleUZDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByWithFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withFile', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByWithFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withFile', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByWithPhoto() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByWithPhotoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByWithText() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByWithTextDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.desc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByWithVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.asc);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QAfterSortBy>
      thenByWithVideoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.desc);
    });
  }
}

extension SpecialNewTaskItemQueryWhereDistinct
    on QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct> {
  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByCreatedAt({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByDate({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'date', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByDesc({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'desc', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByFile({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'file', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByForReference() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'forReference');
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct> distinctById(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByRole({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'role', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByTelegramGroup() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'telegramGroup');
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByTitleQQ({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleQQ', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByTitleRU({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleRU', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByTitleUZ({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleUZ', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByUpdatedAt({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByWithFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withFile');
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByWithPhoto() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withPhoto');
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByWithText() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withText');
    });
  }

  QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QDistinct>
      distinctByWithVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withVideo');
    });
  }
}

extension SpecialNewTaskItemQueryProperty
    on QueryBuilder<SpecialNewTaskItem, SpecialNewTaskItem, QQueryProperty> {
  QueryBuilder<SpecialNewTaskItem, int, QQueryOperations> localIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localId');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations> dateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'date');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations> descProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'desc');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations> fileProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'file');
    });
  }

  QueryBuilder<SpecialNewTaskItem, bool?, QQueryOperations>
      forReferenceProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'forReference');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations> roleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'role');
    });
  }

  QueryBuilder<SpecialNewTaskItem, bool?, QQueryOperations>
      telegramGroupProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'telegramGroup');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations>
      titleQQProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleQQ');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations>
      titleRUProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleRU');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations>
      titleUZProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleUZ');
    });
  }

  QueryBuilder<SpecialNewTaskItem, String?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<SpecialNewTaskItem, User?, QQueryOperations> userProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'user');
    });
  }

  QueryBuilder<SpecialNewTaskItem, bool?, QQueryOperations> withFileProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withFile');
    });
  }

  QueryBuilder<SpecialNewTaskItem, bool?, QQueryOperations>
      withPhotoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withPhoto');
    });
  }

  QueryBuilder<SpecialNewTaskItem, bool?, QQueryOperations> withTextProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withText');
    });
  }

  QueryBuilder<SpecialNewTaskItem, bool?, QQueryOperations>
      withVideoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withVideo');
    });
  }
}
