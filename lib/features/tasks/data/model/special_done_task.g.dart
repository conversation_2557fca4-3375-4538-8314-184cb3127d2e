// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'special_done_task.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetSpecialDoneTaskItemCollection on Isar {
  IsarCollection<SpecialDoneTaskItem> get specialDoneTaskItems =>
      this.collection();
}

const SpecialDoneTaskItemSchema = CollectionSchema(
  name: r'SpecialDoneTask',
  id: 1132290538296517259,
  properties: {
    r'createdAt': PropertySchema(
      id: 0,
      name: r'createdAt',
      type: IsarType.string,
    ),
    r'date': PropertySchema(
      id: 1,
      name: r'date',
      type: IsarType.string,
    ),
    r'desc': PropertySchema(
      id: 2,
      name: r'desc',
      type: IsarType.string,
    ),
    r'file': PropertySchema(
      id: 3,
      name: r'file',
      type: IsarType.string,
    ),
    r'forReference': PropertySchema(
      id: 4,
      name: r'forReference',
      type: IsarType.bool,
    ),
    r'id': PropertySchema(
      id: 5,
      name: r'id',
      type: IsarType.string,
    ),
    r'role': PropertySchema(
      id: 6,
      name: r'role',
      type: IsarType.string,
    ),
    r'telegramGroup': PropertySchema(
      id: 7,
      name: r'telegramGroup',
      type: IsarType.bool,
    ),
    r'titleQQ': PropertySchema(
      id: 8,
      name: r'titleQQ',
      type: IsarType.string,
    ),
    r'titleRU': PropertySchema(
      id: 9,
      name: r'titleRU',
      type: IsarType.string,
    ),
    r'titleUZ': PropertySchema(
      id: 10,
      name: r'titleUZ',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 11,
      name: r'updatedAt',
      type: IsarType.string,
    ),
    r'user': PropertySchema(
      id: 12,
      name: r'user',
      type: IsarType.object,
      target: r'User',
    ),
    r'withPhoto': PropertySchema(
      id: 13,
      name: r'withPhoto',
      type: IsarType.bool,
    ),
    r'withText': PropertySchema(
      id: 14,
      name: r'withText',
      type: IsarType.bool,
    ),
    r'withVideo': PropertySchema(
      id: 15,
      name: r'withVideo',
      type: IsarType.bool,
    )
  },
  estimateSize: _specialDoneTaskItemEstimateSize,
  serialize: _specialDoneTaskItemSerialize,
  deserialize: _specialDoneTaskItemDeserialize,
  deserializeProp: _specialDoneTaskItemDeserializeProp,
  idName: r'localId',
  indexes: {},
  links: {},
  embeddedSchemas: {r'User': UserSchema},
  getId: _specialDoneTaskItemGetId,
  getLinks: _specialDoneTaskItemGetLinks,
  attach: _specialDoneTaskItemAttach,
  version: '3.1.0+1',
);

int _specialDoneTaskItemEstimateSize(
  SpecialDoneTaskItem object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.createdAt;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.date;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.desc;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.file;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.id;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.role;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleQQ;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleRU;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleUZ;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.updatedAt;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.user;
    if (value != null) {
      bytesCount +=
          3 + UserSchema.estimateSize(value, allOffsets[User]!, allOffsets);
    }
  }
  return bytesCount;
}

void _specialDoneTaskItemSerialize(
  SpecialDoneTaskItem object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.createdAt);
  writer.writeString(offsets[1], object.date);
  writer.writeString(offsets[2], object.desc);
  writer.writeString(offsets[3], object.file);
  writer.writeBool(offsets[4], object.forReference);
  writer.writeString(offsets[5], object.id);
  writer.writeString(offsets[6], object.role);
  writer.writeBool(offsets[7], object.telegramGroup);
  writer.writeString(offsets[8], object.titleQQ);
  writer.writeString(offsets[9], object.titleRU);
  writer.writeString(offsets[10], object.titleUZ);
  writer.writeString(offsets[11], object.updatedAt);
  writer.writeObject<User>(
    offsets[12],
    allOffsets,
    UserSchema.serialize,
    object.user,
  );
  writer.writeBool(offsets[13], object.withPhoto);
  writer.writeBool(offsets[14], object.withText);
  writer.writeBool(offsets[15], object.withVideo);
}

SpecialDoneTaskItem _specialDoneTaskItemDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = SpecialDoneTaskItem(
    createdAt: reader.readStringOrNull(offsets[0]),
    date: reader.readStringOrNull(offsets[1]),
    desc: reader.readStringOrNull(offsets[2]),
    file: reader.readStringOrNull(offsets[3]),
    forReference: reader.readBoolOrNull(offsets[4]),
    id: reader.readStringOrNull(offsets[5]),
    role: reader.readStringOrNull(offsets[6]),
    telegramGroup: reader.readBoolOrNull(offsets[7]),
    titleQQ: reader.readStringOrNull(offsets[8]),
    titleRU: reader.readStringOrNull(offsets[9]),
    titleUZ: reader.readStringOrNull(offsets[10]),
    updatedAt: reader.readStringOrNull(offsets[11]),
    user: reader.readObjectOrNull<User>(
      offsets[12],
      UserSchema.deserialize,
      allOffsets,
    ),
    withPhoto: reader.readBoolOrNull(offsets[13]),
    withText: reader.readBoolOrNull(offsets[14]),
    withVideo: reader.readBoolOrNull(offsets[15]),
  );
  object.localId = id;
  return object;
}

P _specialDoneTaskItemDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readBoolOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readBoolOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readObjectOrNull<User>(
        offset,
        UserSchema.deserialize,
        allOffsets,
      )) as P;
    case 13:
      return (reader.readBoolOrNull(offset)) as P;
    case 14:
      return (reader.readBoolOrNull(offset)) as P;
    case 15:
      return (reader.readBoolOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _specialDoneTaskItemGetId(SpecialDoneTaskItem object) {
  return object.localId;
}

List<IsarLinkBase<dynamic>> _specialDoneTaskItemGetLinks(
    SpecialDoneTaskItem object) {
  return [];
}

void _specialDoneTaskItemAttach(
    IsarCollection<dynamic> col, Id id, SpecialDoneTaskItem object) {
  object.localId = id;
}

extension SpecialDoneTaskItemQueryWhereSort
    on QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QWhere> {
  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterWhere>
      anyLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension SpecialDoneTaskItemQueryWhere
    on QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QWhereClause> {
  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterWhereClause>
      localIdEqualTo(Id localId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localId,
        upper: localId,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterWhereClause>
      localIdNotEqualTo(Id localId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterWhereClause>
      localIdGreaterThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localId, includeLower: include),
      );
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterWhereClause>
      localIdLessThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localId, includeUpper: include),
      );
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterWhereClause>
      localIdBetween(
    Id lowerLocalId,
    Id upperLocalId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalId,
        includeLower: includeLower,
        upper: upperLocalId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension SpecialDoneTaskItemQueryFilter on QueryBuilder<SpecialDoneTaskItem,
    SpecialDoneTaskItem, QFilterCondition> {
  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'createdAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'createdAt',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      createdAtIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'createdAt',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'date',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'date',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      dateIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'date',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'desc',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'desc',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'desc',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'desc',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'desc',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      descIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'desc',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'file',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'file',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'file',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'file',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'file',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'file',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      fileIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'file',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      forReferenceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'forReference',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      forReferenceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'forReference',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      forReferenceEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'forReference',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      localIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      localIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      localIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      localIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'role',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'role',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'role',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'role',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'role',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'role',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      roleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'role',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      telegramGroupIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'telegramGroup',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      telegramGroupIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'telegramGroup',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      telegramGroupEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'telegramGroup',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleQQ',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleQQ',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleQQ',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleQQ',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleQQ',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleQQIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleQQ',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleRU',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleRU',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRULessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleRU',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleRU',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleRU',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleRUIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleRU',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleUZ',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleUZ',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleUZ',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleUZ',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleUZ',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      titleUZIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleUZ',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'updatedAt',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'updatedAt',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      updatedAtIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'updatedAt',
        value: '',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      userIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'user',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      userIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'user',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      withPhotoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withPhoto',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      withPhotoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withPhoto',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      withPhotoEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withPhoto',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      withTextIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withText',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      withTextIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withText',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      withTextEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withText',
        value: value,
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      withVideoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withVideo',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      withVideoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withVideo',
      ));
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      withVideoEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withVideo',
        value: value,
      ));
    });
  }
}

extension SpecialDoneTaskItemQueryObject on QueryBuilder<SpecialDoneTaskItem,
    SpecialDoneTaskItem, QFilterCondition> {
  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterFilterCondition>
      user(FilterQuery<User> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'user');
    });
  }
}

extension SpecialDoneTaskItemQueryLinks on QueryBuilder<SpecialDoneTaskItem,
    SpecialDoneTaskItem, QFilterCondition> {}

extension SpecialDoneTaskItemQuerySortBy
    on QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QSortBy> {
  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByDescDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'file', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'file', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByForReference() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'forReference', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByForReferenceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'forReference', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByRole() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'role', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByRoleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'role', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByTelegramGroup() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'telegramGroup', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByTelegramGroupDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'telegramGroup', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByTitleQQ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByTitleQQDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByTitleRU() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByTitleRUDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByTitleUZ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByTitleUZDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByWithPhoto() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByWithPhotoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByWithText() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByWithTextDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByWithVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      sortByWithVideoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.desc);
    });
  }
}

extension SpecialDoneTaskItemQuerySortThenBy
    on QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QSortThenBy> {
  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByDescDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'file', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'file', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByForReference() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'forReference', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByForReferenceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'forReference', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByLocalIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByRole() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'role', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByRoleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'role', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByTelegramGroup() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'telegramGroup', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByTelegramGroupDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'telegramGroup', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByTitleQQ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByTitleQQDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByTitleRU() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByTitleRUDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByTitleUZ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByTitleUZDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByWithPhoto() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByWithPhotoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByWithText() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByWithTextDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.desc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByWithVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.asc);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QAfterSortBy>
      thenByWithVideoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.desc);
    });
  }
}

extension SpecialDoneTaskItemQueryWhereDistinct
    on QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct> {
  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByCreatedAt({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByDate({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'date', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByDesc({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'desc', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByFile({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'file', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByForReference() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'forReference');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByRole({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'role', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByTelegramGroup() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'telegramGroup');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByTitleQQ({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleQQ', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByTitleRU({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleRU', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByTitleUZ({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleUZ', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByUpdatedAt({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByWithPhoto() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withPhoto');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByWithText() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withText');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QDistinct>
      distinctByWithVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withVideo');
    });
  }
}

extension SpecialDoneTaskItemQueryProperty
    on QueryBuilder<SpecialDoneTaskItem, SpecialDoneTaskItem, QQueryProperty> {
  QueryBuilder<SpecialDoneTaskItem, int, QQueryOperations> localIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localId');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations> dateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'date');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations> descProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'desc');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations> fileProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'file');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, bool?, QQueryOperations>
      forReferenceProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'forReference');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations> roleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'role');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, bool?, QQueryOperations>
      telegramGroupProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'telegramGroup');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations>
      titleQQProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleQQ');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations>
      titleRUProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleRU');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations>
      titleUZProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleUZ');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, String?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, User?, QQueryOperations> userProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'user');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, bool?, QQueryOperations>
      withPhotoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withPhoto');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, bool?, QQueryOperations>
      withTextProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withText');
    });
  }

  QueryBuilder<SpecialDoneTaskItem, bool?, QQueryOperations>
      withVideoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withVideo');
    });
  }
}
