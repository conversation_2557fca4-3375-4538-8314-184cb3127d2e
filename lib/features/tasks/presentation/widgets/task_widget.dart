import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/features/tasks/data/model/special_new_task.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class TaskWidget extends StatefulWidget {
  final VoidCallback onTap;
  final SpecialNewTaskItem specialNewTaskItem;

  TaskWidget({super.key, required this.onTap, required this.specialNewTaskItem});

  @override
  State<TaskWidget> createState() => _TaskWidgetState();
}

class _TaskWidgetState extends State<TaskWidget> {
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 6.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          color: Theme.of(context).cardTheme.color,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 4,
              blurRadius: 5,
              offset: Offset(0, 4), // changes position of shadow
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: Container(
                      alignment: Alignment.center,
                      width: 84.h,
                      height: 84.h,
                      padding: EdgeInsets.all(10.h),
                      decoration: BoxDecoration(
                        color: cFirstColor.withAlpha(30),
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Center(child: pictureContent(widget.specialNewTaskItem)),
                    ),
                  ),
                  SizedBox(
                    width: 14.w,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Text(SpecialNewTaskTitle(specialNewTaskItem: widget.specialNewTaskItem),
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(fontSize: 16.sp)),
                        SizedBox(
                          height: 8.h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("${LocaleKeys.given.tr()}:",
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(fontSize: 16.sp)),
                            SizedBox(
                              width: 10.w,
                            ),
                            Flexible(
                              child: Text(
                                fullName(widget.specialNewTaskItem),
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                  fontSize: 15.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            )
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "${LocaleKeys.given_date.tr()}:",
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(fontSize: 16.sp),
                            ),
                            Text(
                                formatterDate.format(DateTime.parse(
                                    widget.specialNewTaskItem.date ??
                                        "1970-01-01")),
                                style: TextStyle(
                                  fontSize: 15.sp,
                                  fontWeight: FontWeight.w500,
                                ))
                          ],
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SizedBox(
                        height: 8.h,
                      ),
                    ],
                  )
                ],
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
            SvgPicture.asset(
              Assets.iconsDashDivider,
              width: MediaQuery.of(context).size.width,
              colorFilter: ColorFilter.mode(
                  isDark() ? cWhiteColor : cGrayColor1, BlendMode.srcIn),
            ),
            SizedBox(
              height: 12.h,
            ),
            Text(
              widget.specialNewTaskItem.desc ?? "Empty",
              textAlign: TextAlign.left,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  overflow: TextOverflow.ellipsis,
                  fontSize: 16.sp),
              maxLines: 2,
            )
          ],
        ),
      ),
    );
  }

  Widget pictureContent(SpecialNewTaskItem specialNewTaskItem) {
    bool? withPhoto = specialNewTaskItem.withPhoto;
    bool? withVideo = specialNewTaskItem.withVideo;
    bool? withText = specialNewTaskItem.withText;
    // bool? withPhoto = false;
    // bool? withVideo = false;
    // bool? withText = true;

    if (specialNewTaskItem.forReference == true) {
      return SvgPicture.asset(Assets.iconsCheck, width: 40.h, height: 40.h);
    } else if (withPhoto == true && withVideo == true && withText == true) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            children: [
              SvgPicture.asset(Assets.iconsCamera1, width: 20.h, height: 20.h),
              SvgPicture.asset(Assets.iconsImageUpload,
                  width: 20.h, height: 20.h),
            ],
          ),
          SvgPicture.asset(Assets.iconsText, width: 20.h, height: 20.h),
        ],
      );
    } else if (withPhoto == false && withVideo == true && withText == true) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(Assets.iconsText, width: 20.h, height: 20.h),
          SvgPicture.asset(Assets.iconsCamera1, width: 20.h, height: 20.h),
        ],
      );
    } else if (withPhoto == true && withVideo == false && withText == true) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(Assets.iconsText, width: 20.h, height: 20.h),
          SvgPicture.asset(Assets.iconsImageUpload, width: 20.h, height: 20.h),
        ],
      );
    } else if (withPhoto == true && withVideo == true && withText == false) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(Assets.iconsCamera1, width: 20.h, height: 20.h),
          SvgPicture.asset(Assets.iconsImageUpload, width: 20.h, height: 20.h),
        ],
      );
    } else if (withPhoto == true && withVideo == false && withText == false) {
      return SvgPicture.asset(
        Assets.iconsImageUpload,
        width: 40.h,
        height: 40.h,
      );
    } else if (withPhoto == false && withVideo == true && withText == false) {
      return SvgPicture.asset(
        Assets.iconsCamera1,
        width: 40.h,
        height: 40.h,
      );
    } else if (withPhoto == false && withVideo == false && withText == true) {
      return SvgPicture.asset(
        Assets.iconsText,
        width: 40.h,
        height: 40.h,
      );
    }

    return SizedBox();
  }

  String fullName(SpecialNewTaskItem specialNewTaskItem) {
    String? middleName = specialNewTaskItem.user?.middleName;
    String? lastName = specialNewTaskItem.user?.lastName;
    String? firstName = specialNewTaskItem.user?.firstName;
    if (middleName != null && lastName != null && firstName != null) {
      return "$lastName $firstName $middleName";
    } else if (middleName == null && lastName != null && firstName != null) {
      return "$lastName $firstName";
    } else if (middleName != null && lastName == null && firstName != null) {
      return "$firstName $middleName";
    } else if(middleName == null && lastName == null && firstName != null){
      return "$lastName";
    }
    else if(middleName == null && lastName != null && firstName == null){
      return "$lastName";
    }
    else if(middleName != null && lastName == null && firstName == null){
      return "$middleName";
    }
    else {
      return "loading...";
    }
  }
}
