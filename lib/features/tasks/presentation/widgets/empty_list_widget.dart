import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class EmptyListWidget extends StatelessWidget {
  final VoidCallback onTap;
  final String title;

  const EmptyListWidget({super.key, required this.onTap, required this.title});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ClipRect(
              child: Container(
                height: 300.h,
                child: Column(
                  children: [
                    SizedBox(
                      height: 20.h,
                    ),
                    Expanded(
                        child: Image.asset(
                      Assets.iconsEmpty,
                      height: 140.h,
                    )),
                    Padding(
                        padding: EdgeInsets.only(
                            top: 10.h, left: 30.w, right: 30.w, bottom: 10.h),
                        child: Text(
                          title,
                          textAlign: TextAlign.center,
                          style: TextStyle(color: cGrayColor1),
                        )),
                    CupertinoButton(
                        child: Text(
                          LocaleKeys.refresh.tr(),
                          style: TextStyle(color: cGrayColor1),
                        ),
                        color: cGrayColor1.withAlpha(80),
                        onPressed: onTap),
                    SizedBox(
                      height: 20.h,
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
