import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/tasks/data/model/special_done_task.dart';
import 'package:yetakchi/features/tasks/data/model/special_new_task.dart';
import 'package:yetakchi/features/tasks/presentation/bloc/tasks_done_bloc/task_done_cubit.dart';
import 'package:yetakchi/features/tasks/presentation/pages/task_detail_page.dart';
import 'package:yetakchi/features/tasks/presentation/widgets/task_widget.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

import '../widgets/empty_list_widget.dart';

class DoneTaskPage extends StatefulWidget {
  DoneTaskPage({super.key});

  static Widget screen() {
    return BlocProvider(
      create: (context) => di<TaskDoneCubit>(),
      child: DoneTaskPage(),
    );
  }

  @override
  State<DoneTaskPage> createState() => _DoneTaskPageState();
}

class _DoneTaskPageState extends State<DoneTaskPage> {
  final PagingController<int, SpecialDoneTaskItem> _pagingController =
      PagingController(firstPageKey: 1);
  List<SpecialDoneTaskItem> list = [];
  bool refresh = false;

  handleRefresh(bool refresh) {
    this.refresh = refresh;
    _pagingController.refresh();
  }

  @override
  void initState() {
    super.initState();

    if (refresh) {
      ///First event when online
      _pagingController.notifyPageRequestListeners(1);
    } else {
      ///First event when offline
      BlocProvider.of<TaskDoneCubit>(context)
          .getTasks(pageKey: 1, refresh: true);
    }
    _pagingController.addPageRequestListener((pageKey) {
      if (pageKey == 1) {
        BlocProvider.of<TaskDoneCubit>(context)
            .getTasks(pageKey: 1, refresh: true);
      } else {
        BlocProvider.of<TaskDoneCubit>(context)
            .getTasks(pageKey: pageKey, refresh: false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TaskDoneCubit, SpecialTaskDoneState>(
      listener: (context, state) {
        if (state.status == TaskDoneStatus.success) {
          list = state.specialDoneTask?.docs ?? [];
          int currentPage = state.specialDoneTask?.page ?? 1;
          bool isLastPage = state.specialDoneTask?.totalPages == currentPage;
          int next = currentPage + 1;
          if (isLastPage) {
            _pagingController.appendLastPage(list);
          } else {
            _pagingController.appendPage(list, next);
          }
        }
      },
      builder: (context, state) {
        if (state.status == TaskDoneStatus.loading ||
            state.status == TaskDoneStatus.initial) {
          return Center(
            child: CupertinoActivityIndicator(
              color: Theme.of(context).primaryColor,
              radius: 30.r,
            ),
          );
        } else if (state.status == TaskDoneStatus.success) {
          return RefreshIndicator(
            onRefresh: () async {
              handleRefresh(true);
            },
            child: PagedListView(
                physics: BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                padding: EdgeInsets.symmetric(vertical: 4.h),
                pagingController: _pagingController,
                builderDelegate: PagedChildBuilderDelegate<SpecialDoneTaskItem>(
                    noItemsFoundIndicatorBuilder: (context) {
                  return EmptyListWidget(
                    onTap: () {
                      handleRefresh(true);
                    },
                    title: LocaleKeys.new_task_not_exist.tr(),
                  );
                }, itemBuilder: (context, item, index) {
                  SpecialNewTaskItem specialNewTaskItem = SpecialNewTaskItem(
                      id: item.id,
                      titleUZ: item.titleUZ,
                      titleRU: item.titleRU,
                      titleQQ: item.titleQQ,
                      desc: item.desc,
                      forReference: item.forReference,
                      withPhoto: item.withPhoto,
                      withVideo: item.withVideo,
                      withText: item.withText,
                      telegramGroup: item.telegramGroup,
                      file: item.file,
                      user: item.user,
                      role: item.role,
                      date: item.date,
                      createdAt: item.createdAt,
                      updatedAt: item.updatedAt);
                  return TaskWidget(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (_) => TaskDetailPage.screen(
                                    specialNewTaskItem,false))).then((value) {
                          handleRefresh(true);
                        });
                      },
                      specialNewTaskItem: specialNewTaskItem);
                })),
          );
        } else if (state.status == TaskDoneStatus.failure) {
          return Container(
            color: Theme.of(context).cardTheme.color,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ClipRect(
                    child: Container(
                      height: 300.h,
                      child: Column(
                        children: [
                          SizedBox(
                            height: 20.h,
                          ),
                          Expanded(
                              child: SvgPicture.asset(
                            Assets.iconsWarning,
                            height: 140.h,
                          )),
                          Padding(
                              padding: EdgeInsets.only(
                                  top: 10.h,
                                  left: 30.w,
                                  right: 30.w,
                                  bottom: 10.h),
                              child: Text(
                                state.message.toString(),
                                textAlign: TextAlign.center,
                                style: TextStyle(color: cGrayColor1),
                              )),
                          CupertinoButton(
                              child: Text(
                                LocaleKeys.refresh.tr(),
                                style: TextStyle(color: cGrayColor1),
                              ),
                              color: cGrayColor1.withAlpha(80),
                              onPressed: () {
                                handleRefresh(true);
                              }),
                          SizedBox(
                            height: 20.h,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          return SizedBox();
        }
      },
    );
  }
}
