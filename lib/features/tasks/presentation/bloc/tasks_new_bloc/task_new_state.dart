part of 'task_new_cubit.dart';

enum TaskNewStatus {
  initial,
  loading,
  success,
  failure,
  emptyList,
}

class SpecialNewTaskNewState extends Equatable {
  final TaskNewStatus? status;
  final SpecialNewTask? specialNewTask;
  final String? message;

  SpecialNewTaskNewState({
    required this.status,
    this.specialNewTask,
    this.message,
  });

  static SpecialNewTaskNewState initial() => SpecialNewTaskNewState(
      status: TaskNewStatus.initial, specialNewTask: null);

  SpecialNewTaskNewState copyWith({
    TaskNewStatus? status,
    SpecialNewTask? specialNewTask,
    String? message,
  }) =>
      SpecialNewTaskNewState(
        status: status ?? this.status,
        specialNewTask: specialNewTask ?? this.specialNewTask,
        message: message,
      );

  @override
  List<Object?> get props => [
        status,
        specialNewTask,
        message,
      ];
}
