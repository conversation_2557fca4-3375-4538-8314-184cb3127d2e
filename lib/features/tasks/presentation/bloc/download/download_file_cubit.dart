import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:meta/meta.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart';

part 'download_file_state.dart';

class DownloadFileCubit extends Cubit<DownloadFileState> {
  DownloadFileCubit() : super(DownloadFileInitial(isFileExist: false));
  Dio dio = di();

  download(String originalUrl, String fakeId) async {
    try {
      emit(DownloadFileLoading(fakeId: fakeId));
      var response = await dio.download(originalUrl ?? "",
          '${androidDownloadPath}${originalUrl.split("/").last}');
      if (response.statusCode == 200) {
        emit(DownloadFileSuccess(fakeId: fakeId));
      }
    } on DioException catch (e) {
      emit(DownloadFileError(fakeId: fakeId));
    }
  }

  emitInitialState(String fileName) async {
    String url = "${androidDownloadPath}${fileName.split("/").last}";
    if (await File(url).exists()) {
      emit(DownloadFileInitial(isFileExist: true));
    } else {
      emit(DownloadFileInitial(isFileExist: false));
    }
  }
}
