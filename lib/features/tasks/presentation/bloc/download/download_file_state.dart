part of 'download_file_cubit.dart';

@immutable
abstract class DownloadFileState {}

class DownloadFileInitial extends DownloadFileState {
  final bool isFileExist;

  DownloadFileInitial({required this.isFileExist});
}

class DownloadFileLoading extends DownloadFileState {
  final String fakeId;

  DownloadFileLoading({required this.fakeId});
}

class DownloadFileSuccess extends DownloadFileState {
  final String fakeId;

  DownloadFileSuccess({required this.fakeId});
}

class DownloadFileError extends DownloadFileState {
  final String fakeId;

  DownloadFileError({required this.fakeId});
}
