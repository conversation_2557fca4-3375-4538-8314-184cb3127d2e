import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

part 'check_file_exist_state.dart';

class CheckFileExistCubit extends Cubit<CheckFileExistState> {
  CheckFileExistCubit() : super(CheckFileExitInitial());

  checkFileExist(String? fileName) async {
    String url = "${androidDownloadPath}${fileName?.split("/").last}";
    print(url);
    if (await File(url).exists()) {
      emit(CheckFileExist(isFileExist: true));
    } else {
      emit(CheckFileExist(isFileExist: false));
    }
  }
}
