part of 'confirm_cubit.dart';

enum ConfirmStatus { initial, loading, failure, success }

class ConfirmState extends Equatable {
  final ConfirmStatus? status;
  final String? message;

  ConfirmState({required this.status, this.message});

  static ConfirmState initial() =>
      ConfirmState(status: ConfirmStatus.initial, message: null);

  ConfirmState copyWith({ConfirmStatus? status, String? message}) =>
      ConfirmState(status: status ?? this.status, message: message);

  @override
  List<Object?> get props => [message,status];
}
