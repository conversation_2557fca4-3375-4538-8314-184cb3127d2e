import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'confirm_state.dart';

class ConfirmCubit extends Cubit<ConfirmState> {
  ConfirmCubit() : super(ConfirmState(status: ConfirmStatus.initial));
  Dio dio = di();
  NetworkInfo networkInfo = di();

  confirm(String id) async {
    if (await networkInfo.isConnected) {
      try {
        emit(state.copyWith(status: ConfirmStatus.loading));
        var response =
            await dio.post(specialWorkPath, data: {"specialTask": id});
        if (response.statusCode == 200) {
          emit(state.copyWith(status: ConfirmStatus.success));
        }
        print("tanishdim");
      } on DioError catch (e) {
        // Handle DioError
        if (e.type == DioErrorType.badResponse) {
          emit(state.copyWith(
              status: ConfirmStatus.failure, message: "Bad Response"));
        } else if (e.type == DioErrorType.connectionTimeout) {
          emit(state.copyWith(
              status: ConfirmStatus.failure, message: "Time Out"));
        }
      } catch (e) {
        emit(state.copyWith(
            status: ConfirmStatus.failure, message: "Unexpected Error"));
      }
    } else {
      // Handle no internet connection
      print("else");
      emit(state.copyWith(status: ConfirmStatus.failure, message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
