part of 'task_done_cubit.dart';

enum TaskDoneStatus {
  initial,
  loading,
  success,
  failure,
  emptyList,
}

class SpecialTaskDoneState extends Equatable {
  final TaskDoneStatus? status;
  final SpecialDoneTask? specialDoneTask;
  final String? message;

  SpecialTaskDoneState({
    required this.status,
    this.specialDoneTask,
    this.message,
  });

  static SpecialTaskDoneState initial() => SpecialTaskDoneState(
      status: TaskDoneStatus.initial, specialDoneTask: null);

  SpecialTaskDoneState copyWith({
    TaskDoneStatus? status,
    SpecialDoneTask? specialNewTask,
    String? message,
  }) =>
      SpecialTaskDoneState(
        status: status ?? this.status,
        specialDoneTask: specialNewTask ?? this.specialDoneTask,
        message: message,
      );

  @override
  List<Object?> get props => [
        status,
    specialDoneTask,
        message,
      ];
}
