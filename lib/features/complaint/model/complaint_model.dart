import 'package:isar/isar.dart';
part 'complaint_model.g.dart';

List<ComplaintModel> complaintList<PERSON>rom<PERSON>son(dynamic json) {
  return List<ComplaintModel>.from(json.map((e) => ComplaintModel.fromJson(e)));
}
@collection
@Name("ComplaintModel")
class ComplaintModel {
  ComplaintModel({
    this.id,
    this.text,
    this.province,
    this.region,
    this.readed,
    this.createdAt
  });

  ComplaintModel.fromJson(dynamic json) {
    id = json['_id'];
    text = json['text'];
    province = json['province'];
    region = json['region'];
    readed = json['readed'];
    createdAt=json['createdAt'];
  }
  Id localId = Isar.autoIncrement;
  String? id;
  String? text;
  String? province;
  String? region;
  bool? readed;
  String? createdAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['text'] = text;
    map['province'] = province;
    map['region'] = region;
    map['readed'] = readed;
    return map;
  }
}
