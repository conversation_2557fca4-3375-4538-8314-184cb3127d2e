import 'package:easy_localization/easy_localization.dart';
import 'package:fading_edge_scrollview/fading_edge_scrollview.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/features/complaint/presentation/bloc/complaint_read/complain_read_cubit.dart';
import 'package:yetakchi/features/complaint/presentation/widget/complaint_widget.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class ReadComplaintPage extends StatefulWidget {
  const ReadComplaintPage({super.key});

  @override
  State<ReadComplaintPage> createState() => _ReadComplaintPageState();
}

class _ReadComplaintPageState extends State<ReadComplaintPage> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<ComplaintReadCubit>(context).getComplaint(true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(

      body: BlocConsumer<ComplaintReadCubit, ComplaintReadState>(
        listener: (context, state) {
          // TODO: implement listener
        },
        builder: (context, state) {
          if (state.status == ComplaintReadStatus.loading) {
            return Center(
              child: CupertinoActivityIndicator(
            color: Theme.of(context).primaryColor,
            radius: 30.r,
              ),
            );
          } else if (state.status == ComplaintReadStatus.success) {
            if (state.list?.isEmpty == true || state.list == null) {
              return Container(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ClipRect(
                        child: Container(
                          height: 300.h,
                          child: Column(
                            children: [
                              SizedBox(
                                height: 20.h,
                              ),
                              Expanded(
                                  child: Image.asset(
                                    Assets.iconsEmpty,
                                    height: 140.h,
                                  )),
                              Padding(
                                  padding: EdgeInsets.only(
                                      top: 10.h,
                                      left: 30.w,
                                      right: 30.w,
                                      bottom: 10.h),
                                  child: Text(
                                    LocaleKeys.complaintEmpty.tr(),
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color: cGrayColor1),
                                  )),
                              CupertinoButton(
                                  child: Text(
                                    LocaleKeys.refresh.tr(),
                                    style: TextStyle(color: cGrayColor1),
                                  ),
                                  color: cGrayColor1.withAlpha(80),
                                  onPressed: () {
                                    BlocProvider.of<ComplaintReadCubit>(context)
                                        .getComplaint(false);
                                  }),
                              SizedBox(
                                height: 20.h,
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            } else {
              return RefreshIndicator(
                onRefresh: () async {
                  BlocProvider.of<ComplaintReadCubit>(context).getComplaint(true);
                },
                child: FadingEdgeScrollView.fromScrollView(
                  child: ListView.builder(
                      controller: ScrollController(),
                      physics: BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      itemCount: state.list!.length,
                      padding: EdgeInsets.only(bottom: 100.h,top: 10.h),
                      itemBuilder: (BuildContext context, int index) {
                        return ComplaintWidget(
                          complaintModel: state.list![index],
                        );
                      }),
                ),
              );
            }
          } else if (state.status == ComplaintReadStatus.failure) {
            return Container(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ClipRect(
                      child: Container(
                        height: 300.h,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 20.h,
                            ),
                            Expanded(
                                child: SvgPicture.asset(
                                  Assets.iconsWarning,
                                  height: 140.h,
                                )),
                            Padding(
                                padding: EdgeInsets.only(
                                    top: 10.h,
                                    left: 30.w,
                                    right: 30.w,
                                    bottom: 10.h),
                                child: Text(
                                  state.message.toString(),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: cGrayColor1),
                                )),
                            CupertinoButton(
                                child: Text(
                                  LocaleKeys.refresh.tr(),
                                  style: TextStyle(color: cGrayColor1),
                                ),
                                color: cGrayColor1.withAlpha(80),
                                onPressed: () {}),
                            SizedBox(
                              height: 20.h,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return SizedBox();
          }
        },
      ),
    );
  }
}
