import 'dart:ui';

import 'package:animated_loading_border/animated_loading_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/complaint/presentation/bloc/complaint_send/complaint_send_cubit.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class ComplaintSheet extends StatefulWidget {
  static Widget screen() {
    return MultiBlocProvider(providers: [
      BlocProvider(create: (context) => di<ComplaintSendCubit>())
    ], child: ComplaintSheet());
  }

  const ComplaintSheet({super.key});

  @override
  State<ComplaintSheet> createState() => _ComplaintSheetState();
}

class _ComplaintSheetState extends State<ComplaintSheet> {
  AnimationController? controllerText;
  TextEditingController text = TextEditingController();
  int count = 0;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ComplaintSendCubit, ComplaintSendState>(
      listener: (context, state) {
        if (state.status == ComplaintSendStatus.success) {
          Navigator.pop(context);
        } else if (state.status == ComplaintSendStatus.failure) {
          CustomToast.showToast(state.message.toString());
        } else {}
      },
      builder: (context, state) {
        return SingleChildScrollView(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
            child: Container(
              padding: EdgeInsets.only(
                right: 20.w,
                left: 20.w,
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topRight: Radius.circular(20.r),
                      topLeft: Radius.circular(20.r)),
                  color: Theme.of(context).cardTheme.color),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [SizedBox(
                    height: 8.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(Assets.iconsSheetPull),
                    ],
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  Text(
                    LocaleKeys.complaint_content.tr(),
                    style: TextStyle(
                        fontSize: 20.sp,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                        fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        "$count/700",
                        style: TextStyle(color: count>0?cBlueColor:cRedColor, fontSize: 14.sp),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 6.h,
                  ),
                  AnimatedLoadingBorder(
                    controller: (control) {
                      controllerText = control;
                      control.reset();
                    },
                    borderColor: cFirstColor,
                    trailingBorderColor: cSecondColor,
                    duration: Duration(milliseconds: 800),
                    borderWidth: 4,
                    cornerRadius: cRadius12.r,
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      height: 240.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius12.r),
                        color: isDark()
                            ? cFourthColorDark.withAlpha(100)
                            : cGrayColor0.withAlpha(100),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 2.h, horizontal: 10.w),
                        child: TextField(
                          controller: text,
                          maxLines: null,
                          maxLength: 700,
                          decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: LocaleKeys.text.tr(),
                              helperStyle: TextStyle(color: cSecondTextColor),
                              hintStyle: TextStyle(
                                  color: cGrayColor1, fontSize: 16.sp),
                              counter: SizedBox()),
                          style: TextStyle(
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                              fontSize: 16.sp),
                          onChanged: (value) {
                            if (value.length == 700) {
                              CustomToast.showToast(
                                  LocaleKeys.complaint_length.tr());
                            }
                            setState(() {
                              count = value.length;
                            });
                          },
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  MaterialButton(
                      height: 56.h,
                      minWidth: MediaQuery.of(context).size.width,
                      onPressed: () {
                        if (text.text.isNotEmpty) {
                          BlocProvider.of<ComplaintSendCubit>(context)
                              .sendComplaint(text.text);
                        } else {
                          CustomToast.showToast(
                              LocaleKeys.cannot_send_empty_data.tr());
                        }
                      },
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30.r),
                      ),
                      color: cFirstColor,
                      child: _button(state)),
                  SizedBox(
                    height: 40.h,
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _button(state) {
    if (state.status == ComplaintSendStatus.loading) {
      return const CupertinoActivityIndicator(color: cWhiteColor);
    } else {
      return Text(
        LocaleKeys.sending.tr(),
        style: TextStyle(fontSize: 18.sp, color: cWhiteColor),
      );
    }
  }
}
