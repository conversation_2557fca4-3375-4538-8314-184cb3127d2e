import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/features/complaint/model/complaint_model.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class ComplaintWidget extends StatefulWidget {
  final ComplaintModel complaintModel;

  const ComplaintWidget({super.key, required this.complaintModel});

  @override
  State<ComplaintWidget> createState() => _ComplaintWidgetState();
}

class _ComplaintWidgetState extends State<ComplaintWidget> {
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
  final DateFormat formatterHour = DateFormat('HH:mm');

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      onTap: () {},
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 6.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          color: Theme.of(context).cardTheme.color,
          boxShadow: [
            boxShadow10,
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.complaintModel.text ?? LocaleKeys.content_empty.tr(),
              style: TextStyle(fontSize: 16.sp),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(
              height: 20.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SvgPicture.asset(
                  Assets.iconsCalendar,
                  colorFilter: ColorFilter.mode(
                      Theme.of(context).iconTheme.color ?? Colors.transparent,
                      BlendMode.srcIn),
                ),
                SizedBox(
                  width: 4.w,
                ),
                Text(
                  formatterDate.format(DateTime.parse(
                      widget.complaintModel.createdAt ?? "1970-01-01")),
                  style: TextStyle(fontSize: 14.sp),
                ),
                SizedBox(
                  width: 10.w,
                ),
                SvgPicture.asset(
                  Assets.iconsClock,
                  colorFilter: ColorFilter.mode(
                      Theme.of(context).iconTheme.color ?? Colors.transparent,
                      BlendMode.srcIn),
                ),
                SizedBox(
                  width: 4.w,
                ),
                Text(
                    formatterHour.format(DateTime.parse(
                        widget.complaintModel.createdAt ?? "00:00")),
                  style: TextStyle(fontSize: 14.sp),
                ),
                Visibility(
                  visible: widget.complaintModel.readed ?? false,
                  child: Row(
                    children: [
                      SizedBox(
                        width: 4.w,
                      ),
                      SvgPicture.asset(Assets.iconsTick,
                          colorFilter: ColorFilter.mode(
                              isDark() ? cPrimaryTextDark : cFirstColor,
                              BlendMode.srcIn)),
                    ],
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
