part of 'complain_read_cubit.dart';

enum ComplaintReadStatus {
  initial,
  loading,
  failure,
  success,
}

class ComplaintReadState extends Equatable {
  final ComplaintReadStatus status;
  final List<ComplaintModel>? list;
  final String? message;

  ComplaintReadState({required this.status, this.list, this.message});

  static ComplaintReadState initial() =>
      ComplaintReadState(status: ComplaintReadStatus.initial);

  ComplaintReadState copyWith(
      {ComplaintReadStatus? status, List<ComplaintModel>? list, String? message}) {
    return ComplaintReadState(
        status: status ?? this.status,
        list: list ?? this.list,
        message: message ?? this.message);
  }

  @override
  List<Object?> get props => [status, list, message];
}
