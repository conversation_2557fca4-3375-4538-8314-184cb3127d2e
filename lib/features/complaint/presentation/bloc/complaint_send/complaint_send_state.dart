part of 'complaint_send_cubit.dart';

enum ComplaintSendStatus {
  initial,
  loading,
  failure,
  success,
}

class ComplaintSendState extends Equatable {
  final ComplaintSendStatus status;
  final String? text;
  final String? message;

  ComplaintSendState({required this.status, this.text, this.message});

  static ComplaintSendState initial() =>
      ComplaintSendState(status: ComplaintSendStatus.initial);

  ComplaintSendState copyWith(
      {ComplaintSendStatus? status, String? text, String? message}) {
    return ComplaintSendState(
        status: status ?? this.status,
        text: text ?? this.text,
        message: message ?? this.message);
  }

  @override
  List<Object?> get props => [status, text, message];
}
