part of 'complaint_cubit.dart';

enum ComplaintStatus {
  initial,
  loading,
  failure,
  success,
}

class ComplaintState extends Equatable {
  final ComplaintStatus status;
  final List<ComplaintModel>? list;
  final String? message;

  ComplaintState({required this.status, this.list, this.message});

  static ComplaintState initial() =>
      ComplaintState(status: ComplaintStatus.initial);

  ComplaintState copyWith(
      {ComplaintStatus? status, List<ComplaintModel>? list, String? message}) {
    return ComplaintState(
        status: status ?? this.status,
        list: list ?? this.list,
        message: message ?? this.message);
  }

  @override
  List<Object?> get props => [status, list, message];
}
