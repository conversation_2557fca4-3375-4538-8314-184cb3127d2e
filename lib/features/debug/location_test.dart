// import 'package:flutter/material.dart';
// import 'package:flutter/scheduler.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:isar/isar.dart';
// import 'package:yetakchi/back_service/model/tracked_location.dart';
// import 'package:yetakchi/core/database/isar_service.dart';
// import 'package:yetakchi/di/dependency_injection.dart';
//
// class LocationTest extends StatefulWidget {
//   const LocationTest({super.key});
//
//   @override
//   State<LocationTest> createState() => _LocationTestState();
// }
//
// class _LocationTestState extends State<LocationTest> {
//   IsarService isar = di();
//
//   Future<List<TrackedLocation?>> getLocation() async {
//     return await isar.isar.trackedLocations
//         .where(sort: Sort.desc)
//         .anyLocalId()
//         .findAll();
//   }
//
//   late Stream<List<TrackedLocation?>> getStream;
//   final ScrollController _sc = ScrollController();
//
//   initQuery() async {
//     Query<TrackedLocation?> query =
//         isar.isar.trackedLocations.where().sortByDateTime().build();
//     getStream = query.watch();
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     initQuery();
//   }
//
//   void scrollUp() {
//     SchedulerBinding.instance.addPostFrameCallback((_) {
//       _sc.animateTo(_sc.position.maxScrollExtent,
//           duration: const Duration(seconds: 2), curve: Curves.fastOutSlowIn);
//       // _sc.jumpTo(_sc.);
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text("Location Debug"),
//       ),
//       body: StreamBuilder(
//         stream: getStream,
//         builder: (BuildContext context,
//             AsyncSnapshot<List<TrackedLocation?>> snapshot) {
//           scrollUp();
//           if (snapshot.hasData) {
//             return ListView.builder(
//                 controller: _sc,
//                 itemCount: snapshot.data?.length,
//                 itemBuilder: (BuildContext context, int index) {
//                   return Card(
//                     margin:
//                         EdgeInsets.symmetric(horizontal: 10.w, vertical: 20.h),
//                     child: Column(
//                       children: [
//                         Text("#${snapshot.data?[index]?.localId}"),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text("dateTime"),
//                             Text(snapshot.data?[index]?.dateTime ?? "")
//                           ],
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text("lat"),
//                             Text(snapshot.data?[index]?.lat.toString() ?? "")
//                           ],
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text("lng"),
//                             Text(snapshot.data?[index]?.lng.toString() ?? "")
//                           ],
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text("locChange"),
//                             Text(snapshot.data?[index]?.locChange.toString() ??
//                                 "")
//                           ],
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text("offside"),
//                             Text(
//                                 snapshot.data?[index]?.offside.toString() ?? "")
//                           ],
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text("timeChange"),
//                             Text(snapshot.data?[index]?.timeChange.toString() ??
//                                 "")
//                           ],
//                         ),
//                       ],
//                     ),
//                   );
//                   // return Text(snapshot.data?[index].toString() ?? "noma'lum");
//                 });
//           } else {
//             return Center(
//               child: CircularProgressIndicator(),
//             );
//           }
//         },
//       ),
//       // body: FutureBuilder(
//       //   future: getLocation(),
//       //   builder: (BuildContext context,
//       //       AsyncSnapshot<List<TrackedLocation?>> snapshot) {
//       //     if (snapshot.connectionState == ConnectionState.waiting) {
//       //       return Center(child: CircularProgressIndicator());
//       //     } else {
//       //       if (snapshot.data?.isNotEmpty == true) {
//       //         return ListView.builder(
//       //             itemCount: snapshot.data?.length,
//       //             itemBuilder: (BuildContext context, int index) {
//       //               return Card(
//       //                 margin: EdgeInsets.symmetric(
//       //                     horizontal: 10.w, vertical: 20.h),
//       //                 child: Column(
//       //                   children: [
//       //                     Text("#${index}"),
//       //                     Row(
//       //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       //                       children: [
//       //                         Text("dateTime"),
//       //                         Text(snapshot.data?[index]?.dateTime ?? "")
//       //                       ],
//       //                     ),
//       //                     Row(
//       //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       //                       children: [
//       //                         Text("lat"),
//       //                         Text(snapshot.data?[index]?.lat.toString() ?? "")
//       //                       ],
//       //                     ),
//       //                     Row(
//       //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       //                       children: [
//       //                         Text("lng"),
//       //                         Text(snapshot.data?[index]?.lng.toString() ?? "")
//       //                       ],
//       //                     ),
//       //                     Row(
//       //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       //                       children: [
//       //                         Text("locChange"),
//       //                         Text(
//       //                             snapshot.data?[index]?.locChange.toString() ??
//       //                                 "")
//       //                       ],
//       //                     ),
//       //                     Row(
//       //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       //                       children: [
//       //                         Text("offside"),
//       //                         Text(snapshot.data?[index]?.offside.toString() ??
//       //                             "")
//       //                       ],
//       //                     ),
//       //                     Row(
//       //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       //                       children: [
//       //                         Text("timeChange"),
//       //                         Text(snapshot.data?[index]?.timeChange
//       //                             .toString() ??
//       //                             "")
//       //                       ],
//       //                     ),
//       //                   ],
//       //                 ),
//       //               );
//       //               // return Text(snapshot.data?[index].toString() ?? "noma'lum");
//       //             });
//       //       } else {
//       //         return Center(
//       //           child: Text("Empty"),
//       //         );
//       //       }
//       //     }
//       //   },
//       // ),
//     );
//   }
// }
