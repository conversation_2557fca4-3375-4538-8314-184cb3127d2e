// ignore_for_file: must_be_immutable

part of 'pass_bloc.dart';

@immutable
abstract class PassState {}

class PassInitial extends PassState {}

class PassIsEmptyState extends PassState {
  bool isEmpty;

  PassIsEmptyState({required this.isEmpty});
}

class PassSuccessState extends PassState {}

class PassConfirmFailureState extends PassState {
  String message = "";

  PassConfirmFailureState({required this.message});
}
