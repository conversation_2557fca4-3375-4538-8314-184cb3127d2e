part of 'pass_bloc.dart';

@immutable
abstract class PassEvent {}

// ignore: must_be_immutable
class OnPressedEvent extends PassEvent {
  TextEditingController newPassword = TextEditingController();
  TextEditingController oldPassword = TextEditingController();
  TextEditingController confirmPassword = TextEditingController();

  OnPressedEvent({
    required this.oldPassword,
    required this.newPassword,
    required this.confirmPassword,
  });
}

// ignore: must_be_immutable
class IsSharedPinEmpty extends PassEvent {
  IsSharedPinEmpty();
}
