import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/features/labor_discipline/data/labor_model.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class LaborDisciplineItem extends StatefulWidget {
  final LaborModel? laborModel;

  const LaborDisciplineItem({super.key, required this.laborModel});

  @override
  State<LaborDisciplineItem> createState() => _LaborDisciplineItemState();
}

class _LaborDisciplineItemState extends State<LaborDisciplineItem> {
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
  final DateFormat formatterHour = DateFormat('HH:mm');

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      onTap: () {},
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 6.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          color: Theme.of(context).cardTheme.color,
          boxShadow: [
            boxShadow10,
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  LocaleKeys.date.tr(),
                  style: TextStyle(fontSize: 16.sp),
                ),
                Text(
                    formatterDate
                        .format(DateTime.parse(widget.laborModel?.date ?? "")),
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                        fontSize: 16.sp))
              ],
            ),
            Visibility(
              visible: widget.laborModel?.late == null ? false : true,
              child: Column(
                children: [
                  SizedBox(
                    height: 10.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        LocaleKeys.time.tr(),
                        style: TextStyle(fontSize: 16.sp),
                      ),
                      Text(
                        formatterHour.format(DateTime.parse(
                          widget.laborModel?.date ?? "",
                        )),
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                            fontSize: 16.sp),
                      )
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            causeWidget(widget.laborModel)
          ],
        ),
      ),
    );
  }

  String timeFormat(int lateTime) {
    int hour = lateTime ~/ 60;
    int minutes = lateTime % 60;
    String time = "";
    if (hour > 0) {
      time = "$hour ${LocaleKeys.hour.tr()}";
    }
    if (minutes > 0) {
      time = time + " " + "$minutes ${LocaleKeys.minute.tr()}";
    }
    return time;
  }

  Widget causeWidget(LaborModel? laborModel) {
    if (laborModel?.late != null) {
      int lateTime = laborModel?.late ?? 0;
      if (lateTime == 0) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
                  child: Text(
                    LocaleKeys.arrive_work_in_time.tr(),
                    style: TextStyle(color: cWhiteColor, fontSize: 16.sp),
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: cMintColor,
                  ),
                ),
              ],
            ),
          ],
        );
      } else {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
                  child: Text(
                    LocaleKeys.arrive_late.tr(),
                    style: TextStyle(color: cWhiteColor, fontSize: 16.sp),
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: cYellowColor,
                  ),
                ),
                Text(
                  timeFormat(laborModel?.late),
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                )
              ],
            ),
          ],
        );
      }
    } else {
      if (laborModel?.cause == null) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
              child: Text(
                LocaleKeys.unAttended_causeless.tr(),
                style: TextStyle(color: cWhiteColor, fontSize: 16.sp),
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: cCarrotColor,
              ),
            ),
          ],
        );
      } else {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
              child: Text(
                LocaleKeys.unAttended_cause.tr(),
                style: TextStyle(color: cWhiteColor, fontSize: 16.sp),
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: cYellowColor,
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            Text(
              laborModel?.cause,
              style: TextStyle(
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                  fontSize: 16.sp),
            )
          ],
        );
      }
    }
  }
}
