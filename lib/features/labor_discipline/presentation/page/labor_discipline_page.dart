import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/header_widget.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/labor_discipline/presentation/cubit/labor_discipline_cubit.dart';
import 'package:yetakchi/features/labor_discipline/presentation/widgets/labor_discipline_item.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';


class LaborDisciplinePage extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => di<LaborDisciplineCubit>(),
      child: LaborDisciplinePage(),
    );
  }

  const LaborDisciplinePage({super.key});

  @override
  State<LaborDisciplinePage> createState() => _LaborDisciplinePageState();
}

class _LaborDisciplinePageState extends State<LaborDisciplinePage> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<LaborDisciplineCubit>(context).loadLabourDiscipline();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeaderWidget(
        title: LocaleKeys.labor_discipline.tr( ),
        onBackTap: () {
          Navigator.pop(context);
        },
        isBackVisible: true,
      ),
      body: BlocConsumer<LaborDisciplineCubit, LaborDisciplineState>(
        listener: (context, state) {
          // TODO: implement listener
        },
        builder: (context, state) {
          if (state.status == LaborDisciplineStatus.success) {
            return RefreshIndicator(
              onRefresh: () async {
                BlocProvider.of<LaborDisciplineCubit>(context)
                    .loadLabourDiscipline();
              },
              child: ListView.builder(
                  padding: EdgeInsets.symmetric(vertical: 10.h),
                  physics: BouncingScrollPhysics(
                      parent: AlwaysScrollableScrollPhysics()),
                  itemCount: state.laborModel?.length,
                  itemBuilder: (context, index) {
                    return LaborDisciplineItem(
                      laborModel: state.laborModel?[index],
                    );
                  }),
            );
          } else if (state.status == LaborDisciplineStatus.loading ||
              state.status == LaborDisciplineStatus.initial) {
            return Center(
              child: CupertinoActivityIndicator(
                radius: 20.r,
              ),
            );
          } else if (state.status == LaborDisciplineStatus.failure) {
            return Container(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ClipRect(
                      child: Container(
                        height: 300.h,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 20.h,
                            ),
                            Expanded(
                                child: SvgPicture.asset(
                              Assets.iconsWarning,
                              height: 140.h,
                            )),
                            Padding(
                                padding: EdgeInsets.only(
                                    top: 10.h,
                                    left: 30.w,
                                    right: 30.w,
                                    bottom: 10.h),
                                child: Text(
                                  state.message.toString(),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: cGrayColor1, fontSize: 16.sp),
                                )),
                            CupertinoButton(
                                child: Text(
                                  LocaleKeys.refresh.tr(),
                                  style: TextStyle(
                                      color: cGrayColor1, fontSize: 16.sp),
                                ),
                                color: cGrayColor1.withAlpha(80),
                                onPressed: () {
                                  BlocProvider.of<LaborDisciplineCubit>(context)
                                      .loadLabourDiscipline();
                                }),
                            SizedBox(
                              height: 20.h,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else if (state.status == LaborDisciplineStatus.empty) {
            return Container(
              color: Theme.of(context).cardTheme.color,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ClipRect(
                      child: Container(
                        height: 300.h,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 20.h,
                            ),
                            Expanded(
                                child: Image.asset(
                              Assets.iconsEmpty,
                              height: 140.h,
                            )),
                            Padding(
                                padding: EdgeInsets.only(
                                    top: 10.h,
                                    left: 30.w,
                                    right: 30.w,
                                    bottom: 10.h),
                                child: Text(
                                  state.message.toString(),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: cGrayColor1, fontSize: 16.sp),
                                )),
                            CupertinoButton(
                                child: Text(
                                  LocaleKeys.refresh.tr(),
                                  style: TextStyle(
                                      color: cGrayColor1, fontSize: 16.sp),
                                ),
                                color: cGrayColor1.withAlpha(80),
                                onPressed: () {
                                  BlocProvider.of<LaborDisciplineCubit>(context)
                                      .loadLabourDiscipline();
                                }),
                            SizedBox(
                              height: 20.h,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return SizedBox();
          }
        },
      ),
    );
  }
}
