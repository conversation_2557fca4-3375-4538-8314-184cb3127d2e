import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/features/labor_discipline/data/labor_model.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'labor_discipline_state.dart';

class LaborDisciplineCubit extends Cubit<LaborDisciplineState> {
  final Dio dio;
  final NetworkInfo networkInfo;

  LaborDisciplineCubit({required this.dio, required this.networkInfo})
      : super(LaborDisciplineState.initial());

  void loadLabourDiscipline() async {
    emit(LaborDisciplineState(status: LaborDisciplineStatus.loading));
    if (await networkInfo.isConnected) {
      try {
        var response = await dio.get(getLaborDisciplinePath);
        if (response.statusCode == 200) {
          List<LaborModel> list = laborListFromJson(response.data);
          if (list.isNotEmpty) {
            emit(LaborDisciplineState(
                status: LaborDisciplineStatus.success, laborModel: list));
          } else {
            emit(LaborDisciplineState(
                status: LaborDisciplineStatus.empty,
                message: LocaleKeys.empty_data.tr()));
          }
        } else {
          emit(LaborDisciplineState(
              status: LaborDisciplineStatus.failure,
              message: LocaleKeys.unknown_error_retry.tr()));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(LaborDisciplineState(
              status: LaborDisciplineStatus.failure,
              message: "Timeout exception"));
        } else {
          emit(LaborDisciplineState(
              status: LaborDisciplineStatus.failure,
              message: LocaleKeys.unknown_error_retry.tr()));
        }
      } catch (e) {
        emit(LaborDisciplineState(
            status: LaborDisciplineStatus.failure,
            message: LocaleKeys.unknown_error_retry.tr()));
      }
    } else {
      emit(LaborDisciplineState(
          status: LaborDisciplineStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
