import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/usescases/usecase.dart';
import 'package:yetakchi/features/home/<USER>/repositories/home_repository.dart';

class UHome extends UseCase<dynamic, GetHomeParams> {
  final HomeRepository homeRepository;

  UHome({required this.homeRepository});

  @override
  Future<Either<Failure, dynamic>> call(GetHomeParams params) {
    return homeRepository.getCategory(params.refresh!);
  }
}

class GetHomeParams extends Equatable {
  final bool? refresh;

  GetHomeParams(this.refresh);

  @override
  List<Object?> get props => [refresh];
}
