// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'support_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetSupportModelCollection on Isar {
  IsarCollection<SupportModel> get supportModels => this.collection();
}

const SupportModelSchema = CollectionSchema(
  name: r'SupportModel',
  id: 2935545765965697631,
  properties: {
    r'id': PropertySchema(
      id: 0,
      name: r'id',
      type: IsarType.string,
    ),
    r'titleQQ': PropertySchema(
      id: 1,
      name: r'titleQQ',
      type: IsarType.string,
    ),
    r'titleRU': PropertySchema(
      id: 2,
      name: r'titleRU',
      type: IsarType.string,
    ),
    r'titleUZ': PropertySchema(
      id: 3,
      name: r'titleUZ',
      type: IsarType.string,
    )
  },
  estimateSize: _supportModelEstimateSize,
  serialize: _supportModelSerialize,
  deserialize: _supportModelDeserialize,
  deserializeProp: _supportModelDeserializeProp,
  idName: r'localId',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _supportModelGetId,
  getLinks: _supportModelGetLinks,
  attach: _supportModelAttach,
  version: '3.1.0+1',
);

int _supportModelEstimateSize(
  SupportModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.id;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleQQ;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleRU;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleUZ;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _supportModelSerialize(
  SupportModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.id);
  writer.writeString(offsets[1], object.titleQQ);
  writer.writeString(offsets[2], object.titleRU);
  writer.writeString(offsets[3], object.titleUZ);
}

SupportModel _supportModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = SupportModel(
    id: reader.readStringOrNull(offsets[0]),
    titleQQ: reader.readStringOrNull(offsets[1]),
    titleRU: reader.readStringOrNull(offsets[2]),
    titleUZ: reader.readStringOrNull(offsets[3]),
  );
  object.localId = id;
  return object;
}

P _supportModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _supportModelGetId(SupportModel object) {
  return object.localId;
}

List<IsarLinkBase<dynamic>> _supportModelGetLinks(SupportModel object) {
  return [];
}

void _supportModelAttach(
    IsarCollection<dynamic> col, Id id, SupportModel object) {
  object.localId = id;
}

extension SupportModelQueryWhereSort
    on QueryBuilder<SupportModel, SupportModel, QWhere> {
  QueryBuilder<SupportModel, SupportModel, QAfterWhere> anyLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension SupportModelQueryWhere
    on QueryBuilder<SupportModel, SupportModel, QWhereClause> {
  QueryBuilder<SupportModel, SupportModel, QAfterWhereClause> localIdEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localId,
        upper: localId,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterWhereClause> localIdNotEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterWhereClause>
      localIdGreaterThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localId, includeLower: include),
      );
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterWhereClause> localIdLessThan(
      Id localId,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localId, includeUpper: include),
      );
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterWhereClause> localIdBetween(
    Id lowerLocalId,
    Id upperLocalId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalId,
        includeLower: includeLower,
        upper: upperLocalId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension SupportModelQueryFilter
    on QueryBuilder<SupportModel, SupportModel, QFilterCondition> {
  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      localIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      localIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      localIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      localIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleQQ',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleQQ',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleQQ',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleQQ',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleQQ',
        value: '',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleQQIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleQQ',
        value: '',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleRU',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleRU',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRULessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleRU',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleRU',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleRU',
        value: '',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleRUIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleRU',
        value: '',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleUZ',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleUZ',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleUZ',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleUZ',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleUZ',
        value: '',
      ));
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterFilterCondition>
      titleUZIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleUZ',
        value: '',
      ));
    });
  }
}

extension SupportModelQueryObject
    on QueryBuilder<SupportModel, SupportModel, QFilterCondition> {}

extension SupportModelQueryLinks
    on QueryBuilder<SupportModel, SupportModel, QFilterCondition> {}

extension SupportModelQuerySortBy
    on QueryBuilder<SupportModel, SupportModel, QSortBy> {
  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> sortByTitleQQ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.asc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> sortByTitleQQDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.desc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> sortByTitleRU() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.asc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> sortByTitleRUDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.desc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> sortByTitleUZ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.asc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> sortByTitleUZDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.desc);
    });
  }
}

extension SupportModelQuerySortThenBy
    on QueryBuilder<SupportModel, SupportModel, QSortThenBy> {
  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenByLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.asc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenByLocalIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.desc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenByTitleQQ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.asc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenByTitleQQDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.desc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenByTitleRU() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.asc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenByTitleRUDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.desc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenByTitleUZ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.asc);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QAfterSortBy> thenByTitleUZDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.desc);
    });
  }
}

extension SupportModelQueryWhereDistinct
    on QueryBuilder<SupportModel, SupportModel, QDistinct> {
  QueryBuilder<SupportModel, SupportModel, QDistinct> distinctById(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QDistinct> distinctByTitleQQ(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleQQ', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QDistinct> distinctByTitleRU(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleRU', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<SupportModel, SupportModel, QDistinct> distinctByTitleUZ(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleUZ', caseSensitive: caseSensitive);
    });
  }
}

extension SupportModelQueryProperty
    on QueryBuilder<SupportModel, SupportModel, QQueryProperty> {
  QueryBuilder<SupportModel, int, QQueryOperations> localIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localId');
    });
  }

  QueryBuilder<SupportModel, String?, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<SupportModel, String?, QQueryOperations> titleQQProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleQQ');
    });
  }

  QueryBuilder<SupportModel, String?, QQueryOperations> titleRUProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleRU');
    });
  }

  QueryBuilder<SupportModel, String?, QQueryOperations> titleUZProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleUZ');
    });
  }
}
