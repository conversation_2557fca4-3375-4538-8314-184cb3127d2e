import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/embedded_models.dart';

part 'today_works.g.dart';

@collection
@Name("TodayWorks")
class TodayWorks {
  TodayWorks({
    this.total,
    this.success,
    this.docs,
  });

  TodayWorks.fromJson(dynamic json) {
    total = json['total'];
    success = json['success'];
    if (json['docs'] != null) {
      docs = [];
      json['docs'].forEach((v) {
        docs?.add(DocsToday.fromJson(v));
      });
    }
  }

  Id localId = Isar.autoIncrement;
  int? total;
  int? success;
  List<DocsToday>? docs;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['total'] = total;
    map['success'] = success;
    if (docs != null) {
      map['docs'] = docs?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}
