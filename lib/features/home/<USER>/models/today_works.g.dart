// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'today_works.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetTodayWorksCollection on Isar {
  IsarCollection<TodayWorks> get todayWorks => this.collection();
}

const TodayWorksSchema = CollectionSchema(
  name: r'TodayWorks',
  id: 641442487858469800,
  properties: {
    r'docs': PropertySchema(
      id: 0,
      name: r'docs',
      type: IsarType.objectList,
      target: r'DocsToday',
    ),
    r'success': PropertySchema(
      id: 1,
      name: r'success',
      type: IsarType.long,
    ),
    r'total': PropertySchema(
      id: 2,
      name: r'total',
      type: IsarType.long,
    )
  },
  estimateSize: _todayWorksEstimateSize,
  serialize: _todayWorksSerialize,
  deserialize: _todayWorksDeserialize,
  deserializeProp: _todayWorksDeserializeProp,
  idName: r'localId',
  indexes: {},
  links: {},
  embeddedSchemas: {
    r'DocsToday': DocsTodaySchema,
    r'SubCategory': SubCategorySchema
  },
  getId: _todayWorksGetId,
  getLinks: _todayWorksGetLinks,
  attach: _todayWorksAttach,
  version: '3.1.0+1',
);

int _todayWorksEstimateSize(
  TodayWorks object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final list = object.docs;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        final offsets = allOffsets[DocsToday]!;
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount +=
              DocsTodaySchema.estimateSize(value, offsets, allOffsets);
        }
      }
    }
  }
  return bytesCount;
}

void _todayWorksSerialize(
  TodayWorks object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeObjectList<DocsToday>(
    offsets[0],
    allOffsets,
    DocsTodaySchema.serialize,
    object.docs,
  );
  writer.writeLong(offsets[1], object.success);
  writer.writeLong(offsets[2], object.total);
}

TodayWorks _todayWorksDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = TodayWorks(
    docs: reader.readObjectList<DocsToday>(
      offsets[0],
      DocsTodaySchema.deserialize,
      allOffsets,
      DocsToday(),
    ),
    success: reader.readLongOrNull(offsets[1]),
    total: reader.readLongOrNull(offsets[2]),
  );
  object.localId = id;
  return object;
}

P _todayWorksDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readObjectList<DocsToday>(
        offset,
        DocsTodaySchema.deserialize,
        allOffsets,
        DocsToday(),
      )) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _todayWorksGetId(TodayWorks object) {
  return object.localId;
}

List<IsarLinkBase<dynamic>> _todayWorksGetLinks(TodayWorks object) {
  return [];
}

void _todayWorksAttach(IsarCollection<dynamic> col, Id id, TodayWorks object) {
  object.localId = id;
}

extension TodayWorksQueryWhereSort
    on QueryBuilder<TodayWorks, TodayWorks, QWhere> {
  QueryBuilder<TodayWorks, TodayWorks, QAfterWhere> anyLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension TodayWorksQueryWhere
    on QueryBuilder<TodayWorks, TodayWorks, QWhereClause> {
  QueryBuilder<TodayWorks, TodayWorks, QAfterWhereClause> localIdEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localId,
        upper: localId,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterWhereClause> localIdNotEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterWhereClause> localIdGreaterThan(
      Id localId,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localId, includeLower: include),
      );
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterWhereClause> localIdLessThan(
      Id localId,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localId, includeUpper: include),
      );
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterWhereClause> localIdBetween(
    Id lowerLocalId,
    Id upperLocalId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalId,
        includeLower: includeLower,
        upper: upperLocalId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension TodayWorksQueryFilter
    on QueryBuilder<TodayWorks, TodayWorks, QFilterCondition> {
  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> docsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'docs',
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> docsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'docs',
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> docsLengthEqualTo(
      int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'docs',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> docsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'docs',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> docsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'docs',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition>
      docsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'docs',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition>
      docsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'docs',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> docsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'docs',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> localIdEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition>
      localIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> localIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> localIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> successIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'success',
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition>
      successIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'success',
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> successEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'success',
        value: value,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition>
      successGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'success',
        value: value,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> successLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'success',
        value: value,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> successBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'success',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> totalIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'total',
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> totalIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'total',
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> totalEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'total',
        value: value,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> totalGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'total',
        value: value,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> totalLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'total',
        value: value,
      ));
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> totalBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'total',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension TodayWorksQueryObject
    on QueryBuilder<TodayWorks, TodayWorks, QFilterCondition> {
  QueryBuilder<TodayWorks, TodayWorks, QAfterFilterCondition> docsElement(
      FilterQuery<DocsToday> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'docs');
    });
  }
}

extension TodayWorksQueryLinks
    on QueryBuilder<TodayWorks, TodayWorks, QFilterCondition> {}

extension TodayWorksQuerySortBy
    on QueryBuilder<TodayWorks, TodayWorks, QSortBy> {
  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> sortBySuccess() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'success', Sort.asc);
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> sortBySuccessDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'success', Sort.desc);
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> sortByTotal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'total', Sort.asc);
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> sortByTotalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'total', Sort.desc);
    });
  }
}

extension TodayWorksQuerySortThenBy
    on QueryBuilder<TodayWorks, TodayWorks, QSortThenBy> {
  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> thenByLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.asc);
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> thenByLocalIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.desc);
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> thenBySuccess() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'success', Sort.asc);
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> thenBySuccessDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'success', Sort.desc);
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> thenByTotal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'total', Sort.asc);
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QAfterSortBy> thenByTotalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'total', Sort.desc);
    });
  }
}

extension TodayWorksQueryWhereDistinct
    on QueryBuilder<TodayWorks, TodayWorks, QDistinct> {
  QueryBuilder<TodayWorks, TodayWorks, QDistinct> distinctBySuccess() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'success');
    });
  }

  QueryBuilder<TodayWorks, TodayWorks, QDistinct> distinctByTotal() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'total');
    });
  }
}

extension TodayWorksQueryProperty
    on QueryBuilder<TodayWorks, TodayWorks, QQueryProperty> {
  QueryBuilder<TodayWorks, int, QQueryOperations> localIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localId');
    });
  }

  QueryBuilder<TodayWorks, List<DocsToday>?, QQueryOperations> docsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'docs');
    });
  }

  QueryBuilder<TodayWorks, int?, QQueryOperations> successProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'success');
    });
  }

  QueryBuilder<TodayWorks, int?, QQueryOperations> totalProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'total');
    });
  }
}
