import 'package:isar/isar.dart';

part 'support_model.g.dart';

@collection
@Name("SupportModel")
class SupportModel {
  SupportModel({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,
  });

  SupportModel.fromJson(dynamic json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
  }

  Id localId = Isar.autoIncrement;
  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['titleQQ'] = titleQQ;
    return map;
  }
}
