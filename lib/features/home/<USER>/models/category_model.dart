import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/embedded_models.dart';

part 'category_model.g.dart';

@collection
@Name("CategoryModel")
class CategoryModel {
  CategoryModel({
    this.value,
    this.subCategorys,
    this.category,
  });

  CategoryModel.fromJson(dynamic json) {
    value = json['value'];
    if (json['subCategorys'] != null) {
      subCategorys = [];
      json['subCategorys'].forEach((v) {
        subCategorys?.add(SubCategorys.fromJson(v));
      });
    }
    category =
        json['category'] != null ? Category.fromJson(json['category']) : null;
  }

  Id localId = Isar.autoIncrement;
  int? value;
  List<SubCategorys>? subCategorys;
  Category? category;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['value'] = value;
    if (subCategorys != null) {
      map['subCategorys'] = subCategorys?.map((v) => v.toJson()).toList();
    }
    if (category != null) {
      map['category'] = category?.toJson();
    }
    return map;
  }
}
