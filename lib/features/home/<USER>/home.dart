import 'dart:async';

import 'package:action_slider/action_slider.dart';
import 'package:awesome_circular_chart/awesome_circular_chart.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:fading_edge_scrollview/fading_edge_scrollview.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart' hide Trans;
import 'package:get_storage/get_storage.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:isar/isar.dart';
import 'package:page_transition/page_transition.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/back_service/back_service.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/utils/target.dart';
import 'package:yetakchi/core/widgets/custom_action_button.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/core/widgets/rounded_bottom_navigation_bar.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/auth/data/model/calendar.dart';
import 'package:yetakchi/features/auth/data/model/user_model.dart';
import 'package:yetakchi/features/electron_library/book_list/presentation/pages/electron_library_page.dart';
import 'package:yetakchi/features/face_control/facedetectionview.dart';
import 'package:yetakchi/features/home/<USER>/bloc/category_bloc.dart';
import 'package:yetakchi/features/home/<USER>/response_model.dart';
import 'package:yetakchi/features/home/<USER>/sub_categories.dart';
import 'package:yetakchi/features/home/<USER>/sub_categories_today.dart';
import 'package:yetakchi/features/home/<USER>/widgets/on_work_enums.dart';
import 'package:yetakchi/features/payments/payment_res.dart';
import 'package:yetakchi/features/quiz/presentation/pages/test_main_page.dart';
import 'package:yetakchi/features/quiz/presentation/widgets/home_quiz_widget.dart';
import 'package:yetakchi/features/yetakchi_ai/presentation/page/yetakchi_ai.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

import 'navigation.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  static Widget screen() {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => di<HomeBloc>()),
      ],
      child: HomePage(),
    );
  }

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final _listController = ScrollController();
  var sm = SessionManager();
  late HomeBloc _categoryBloc;
  late final AppLifecycleListener _listener;
  ValueNotifier<int> testCountNotifier = ValueNotifier<int>(0);
  Future<ResponseModel>? _initFuture;
  Future<bool>? _initFutureRegulation;
  bool isOnWork = false;
  bool isOnSetting = false;
  bool _shouldRequestPermission = false;
  SharedPreferences prefs = di();
  AppUpdateInfo? _updateInfo = di();
  final NetworkInfo networkInfo = di();
  ActionSliderController actionSliderController = ActionSliderController();
  final GetStorage gs = di();
  Dio dio = di();
  late String dateHome;
  late String todayDateFormatted;
  var bottomNavPageState;

  Future _handleRefresh(bool refresh) async {
    isOnSetting = await sm.get(on_setting) ?? false;
    _categoryBloc.add(GetHome(refresh));
    reInitializerButton();
  }

  Future _onlineRefresh() async {
    isOnSetting = await sm.get(on_setting) ?? false;
    _categoryBloc.add(GetHome(isOnSetting ? false : true));
    // reInitializerButton();
    reInitRegAvailable();
  }

  Future<bool> checkRegulationAvailability() async {
    IsarService isarService = di();
    var userModel = UserModel();
    var calendarModel = Calendar();
    String userId = prefs.getString('id') ?? '';
    DateTime today = DateTime.now();
    String todayDateFormatted = DateFormat('yyyy-MM-dd').format(today);

    print('======================OUT STARTED');

    if (await networkInfo.isConnected) {
      try {
        final responseUser = await dio.get(usersPath + userId);
        var data = responseUser.data;

        if (responseUser.statusCode == 200) {
          if (data != null) {
            userModel = UserModel.fromJson(data['user']);
            calendarModel = Calendar.fromJson(data['calendar']);

            var anyLocation = userModel.district?.coordinate?.longitude;

            if (anyLocation == null) {
              var status = false;
              await prefs.setBool(show_work_slider, status);
              return status;

              ///This code won't work
              isOnWork = true;
              await prefs.setString(on_work, todayDateFormatted);
              print('User model: ${userModel.toJson()}');
            } else {
              //TODO: get last attended date and write to else case (later)
            }

            await isarService.isar.writeTxn(() async {
              await isarService.isar.userModels.where().deleteAll();
              await isarService.isar.userModels.put(userModel);
              await isarService.isar.calendars.where().deleteAll();
              await isarService.isar.calendars.put(calendarModel);
            });
          }
        }

        final responseRegulation = await dio.get(regulationPath);

        if (responseRegulation.statusCode == 200) {
          var status = true;
          await prefs.setBool(show_work_slider, status);
          print('======================OUT ENDED: $status');
          return status;
        } else {
          var status = false;
          await prefs.setBool(show_work_slider, status);
          return status;
        }
      } on DioException catch (e) {
        var status = false;
        await prefs.setBool(show_work_slider, status);
        print('======================OUT ENDED: $status ${e.toString()}');
        return status;
      } catch (e) {
        var status = false;
        await prefs.setBool(show_work_slider, status);
        print('======================OUT ENDED: $status ${e.toString()}');
        return status;
      }
    } else {
      await prefs.reload();
      return await prefs.getBool(show_work_slider) ?? true;
    }
  }

  reInitRegAvailable() {
    print('============REG_AVAIL');

    var storedDate = prefs.getString(on_work);

    if (storedDate != null) {
      // Compare the stored date with today's date
      isOnWork = storedDate == todayDateFormatted;
    } else {
      isOnWork = false;
    }

    if (!mounted) return;
    setState(() {
      _initFutureRegulation = checkRegulationAvailability();
      _initFutureRegulation?.whenComplete(() => reInitializerButton());
    });
  }

  reInitializerButton() async {
    print('============STATE_BUILD');
    postInfo(prefs: di(), client: di());

    ///It's needed for on_work key
    await prefs.reload();
    if (!mounted) return;
    setState(() {
      _initFuture = widgetStateFuture(di());
    });
  }

  Future<ResponseModel> widgetStateFuture(IsarService isarService) async {
    print('======================IN STARTED');

    isOnSetting = await sm.get(on_setting) ?? false;

    var userId = prefs.getString('id') ?? '';

    final user = await isarService.isar.userModels.where().limit(1).findFirst();
    var lat = user?.district?.coordinate?.latitude ?? 0;
    var lng = user?.district?.coordinate?.longitude ?? 0;

    print('======================IN ENDED');

    if (!isOnSetting) {
      return ResponseModel(
          responseCode: ResultCode.SLIDER, message: 'Show up slider!');
    } else if (isOnSetting) {
      await sm.set(on_setting, false);
      var result;
      try {
        result = await compareGPS(
            lat: lat, lng: lng, allowedDistance: ALLOWED_DISTANCE);
      } catch (e) {
        print(e);
      }

      if (result.runtimeType == String) {
        return ResponseModel(
            responseCode: ResultCode.GPS_ERROR, message: result);
      } else if (!result) {
        await sm.set(on_setting, false);
        print('User is not in zone...');
        return ResponseModel(
            responseCode: ResultCode.NOT_IN_ZONE,
            message: LocaleKeys.not_work_place.tr());
      } else {
        if (await networkInfo.isConnected) {
          try {
            var now = DateTime.now();
            var time = (DateFormat('yyyy-MM-dd HH:mm').format(now));
            todayDateFormatted =
                DateFormat('yyyy-MM-dd').format(DateTime.now());

            final response = await dio
                .post(regulationPath, data: {"user": userId, "date": time});
            var data = response.data as Map;

            if (response.statusCode == 200) {
              print(data);
              prefs.setString(on_work, todayDateFormatted);
              var message =
                  data.containsKey('message') ? data['message'] : null;
              if (message == null) {
                return ResponseModel(
                    responseCode: ResultCode.SUCCESS,
                    message: LocaleKeys.arrive_work_time_receive.tr());
              } else {
                return ResponseModel(
                    responseCode: ResultCode.SUCCESS_CHECKED,
                    message: LocaleKeys.already_checked.tr());
              }
            } else {
              return ResponseModel(
                  responseCode: ResultCode.UNKNOWN_ERROR, message: 'Error!');
            }
          } on DioException catch (e) {
            print(e);
            CustomToast.showToast('Server error: ${e.response?.statusCode}');
            return ResponseModel(
                responseCode: ResultCode.SERVER_ERROR,
                message: LocaleKeys.unknown_server_error_retry.tr());
          } catch (e) {
            print(e);

            CustomToast.showToast(e.toString());
            return ResponseModel(
                responseCode: ResultCode.UNKNOWN_ERROR,
                message: LocaleKeys.unknown_error_retry.tr());
          }
        } else {
          return ResponseModel(
              responseCode: ResultCode.NO_INTERNET,
              message: LocaleKeys.switch_on_internet.tr());
        }
      }
    } else {
      await sm.set(on_setting, false);
      return ResponseModel(
          responseCode: ResultCode.HIDE, message: "Qora tuynuk ochilmoqda...");
    }
  }

  void getTestWidgetAvailability() {
    gs.listenKey(TEST_COUNT, (value) {
      print('new test is $value');
      testCountNotifier.value = value;
    });
  }

  @override
  void initState() {

    Geolocator.getCurrentPosition();

    ///TODO: Why this won't work
    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage? message) {
      ///This won't work
    });

    checkIbratApp(context);
    getTestWidgetAvailability();
    testCountNotifier.value = gs.read(TEST_COUNT) ?? 0;
    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );

    todayDateFormatted = DateFormat('yyyy-MM-dd').format(DateTime.now());

    ///For testing
    IsarService isarService = di();
    final user = isarService.isar.userModels.where().limit(1).findFirstSync();
    var lat = user?.district?.coordinate?.latitude ?? 0;
    var lng = user?.district?.coordinate?.longitude ?? 0;

    print("User lat: $lat\nUser lng: $lng ");

    bottomNavPageState =
        context.findAncestorStateOfType<State<BottomNavigationPage>>();
    // appBarHeaderState =
    //     context.findAncestorStateOfType<State<HeaderWidgetMain>>();
    _categoryBloc = BlocProvider.of<HomeBloc>(context);
    _onlineRefresh();
    startService();
    super.initState();
  }

  startService() async {
    FlutterBackgroundService().invoke("setAsForeground");
    final service = FlutterBackgroundService();
    print("Service is --------------- :${await service.isRunning()}");
    if (!await service.isRunning()) {
      print("Service is not running!");
      await initialiseService();
      service.startService();
    }
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  void _onDetached() => print('detached');

  void _onResumed() {
    print("--------------------Resumed: HOME------------------------");
    if (_shouldRequestPermission) {
      checkIbratApp(context);
      _shouldRequestPermission = false;
    }
  }

  void _onInactive() => print('inactive');

  void _onHidden() => print('hidden');

  void _onPaused() async {
    _shouldRequestPermission = true;
  }

  @override
  void dispose() {
    _listener.dispose();
    _categoryBloc.close();
    testCountNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print("------------${prefs.getString(on_work)}----------------");
    if (_updateInfo?.updateAvailability == UpdateAvailability.updateAvailable &&
        isAndroid()) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showSnack(LocaleKeys.hasUpdate.tr(), context, () {
          launchCustomUrl(APP_LINK);
        });
      });

      InAppUpdate.performImmediateUpdate().catchError((e) {
        showSnack(e.toString(), context, () {});
      });
    }

    return Scaffold(
      floatingActionButton: Padding(
        padding:
            EdgeInsets.only(bottom: context.isTablet ? 90.h : 80.h, right: 5.w),
        child: SizedBox(
          width: 70.w,
          height: 50.h,
          child: FloatingActionButton.extended(
            backgroundColor:
                Theme.of(context).floatingActionButtonTheme.backgroundColor,
            onPressed: () {
              _onlineRefresh();
            },
            label: Icon(
              Icons.refresh,
              size: 30.h,
              color: cWhiteColor,
            ),
          ),
        ),
      ),
      body: Container(
        padding: EdgeInsets.symmetric(vertical: 15.h),
        child: Column(
          children: [
            BlocBuilder<HomeBloc, HomeState>(
              builder: (context, state) {
                if (state is HomeLoadingState || state is HomeInitialState) {
                  return Expanded(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.only(bottom: 100.h),
                        child: CupertinoActivityIndicator(
                          color: Theme.of(context).primaryColor,
                          radius: 30.r,
                        ),
                      ),
                    ),
                  );
                } else if (state is HomeSuccessState) {
                  var mainCategories = state.mainCategoryList;
                  var todayWorks = state.todayWorks;

                  mainCategories
                      .sort((a, b) => b.value?.compareTo(a.value ?? 0) ?? 0);

                  ///Refresh the badges
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    bottomNavPageState?.setState(() {
                      print("Badges are refreshed!");
                    });
                  });

                  print('======================FUTURE BUILD STARTED');

                  return Expanded(
                    child: Column(
                      children: [
                        FutureBuilder<bool>(
                            future: _initFutureRegulation,
                            builder: (context, snapshot) {
                              switch (snapshot.connectionState) {
                                case ConnectionState.waiting:
                                  {
                                    // Otherwise, display a loading indicator.
                                    return Center(
                                        child: CupertinoActivityIndicator(
                                      color: Theme.of(context).primaryColor,
                                      radius: 12.r,
                                    ));
                                  }
                                default:
                                  return Visibility(
                                    visible:
                                        !isOnWork && (snapshot.data ?? true),
                                    child: Container(
                                            width: double.infinity,
                                            padding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 10.w,
                                                    vertical: 20.h),
                                            margin:
                                                EdgeInsets.symmetric(
                                                    horizontal: 20.w),
                                            decoration: BoxDecoration(
                                              color: Theme.of(context)
                                                  .cardTheme
                                                  .color,
                                              borderRadius:
                                                  BorderRadius.circular(30.r),
                                              boxShadow: [
                                                boxShadow60,
                                              ],
                                            ),
                                            child: FutureBuilder<ResponseModel>(
                                                future: _initFuture,
                                                builder: (context, snapshot) {
                                                  switch (snapshot
                                                      .connectionState) {
                                                    case ConnectionState
                                                          .waiting:
                                                      {
                                                        // Otherwise, display a loading indicator.
                                                        return Padding(
                                                          padding:
                                                              EdgeInsets.all(
                                                                  25.h),
                                                          child: Center(
                                                              child:
                                                                  CupertinoActivityIndicator(
                                                            color: Theme.of(
                                                                    context)
                                                                .primaryColor,
                                                            radius: 22.r,
                                                          )),
                                                        );
                                                      }
                                                    default:
                                                      if (snapshot.hasError) {
                                                        print(
                                                            'Error: ${snapshot.connectionState}');
                                                        return Center(
                                                          child: Column(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceAround,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .center,
                                                            children: [
                                                              Text(
                                                                LocaleKeys
                                                                    .please_refresh_page
                                                                    .tr(),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                                style: TextStyle(
                                                                    color:
                                                                        cGrayColor1),
                                                              ),
                                                              SizedBox(
                                                                height: 10.h,
                                                              ),
                                                              CupertinoButton(
                                                                  child: Text(
                                                                    LocaleKeys
                                                                        .refresh
                                                                        .tr(),
                                                                    style: TextStyle(
                                                                        color:
                                                                            cGrayColor1),
                                                                  ),
                                                                  color: cGrayColor1
                                                                      .withAlpha(
                                                                          80),
                                                                  onPressed:
                                                                      () {
                                                                    reInitRegAvailable();
                                                                  }),
                                                            ],
                                                          ),
                                                        );
                                                      } else {
                                                        print(
                                                            'DEFAULT: ${snapshot.connectionState}');

                                                        if (snapshot.data
                                                                ?.responseCode ==
                                                            ResultCode.SLIDER) {

                                                          return Column(
                                                            children: [
                                                              Text(
                                                                  LocaleKeys
                                                                      .check_work_arrive
                                                                      .tr(),
                                                                  style:
                                                                      TextStyle(
                                                                    color: Theme.of(
                                                                            context)
                                                                        .primaryColor,
                                                                    fontSize:
                                                                        18.sp,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                  )),
                                                              SizedBox(
                                                                height: 14.h,
                                                              ),
                                                              Padding(
                                                                padding: EdgeInsets.symmetric(
                                                                    horizontal: context
                                                                            .isPhone
                                                                        ? 60.w
                                                                        : 70.w),
                                                                child:
                                                                    CustomGradientButtonActionUniversal(
                                                                  onAction:
                                                                      (controller) async {
                                                                    actionSliderController =
                                                                        controller;

                                                                    controller
                                                                        .loading();

                                                                    var paymentStatus =
                                                                        await checkUserPayment();

                                                                    if (paymentStatus
                                                                            .runtimeType ==
                                                                        PaymentRes) {
                                                                      if (!(paymentStatus !=
                                                                              null &&
                                                                          paymentStatus.status ==
                                                                              true)) {
                                                                        controller
                                                                            .reset();
                                                                        CustomToast.showToast(payResModelTitle(
                                                                            paymentRes:
                                                                                paymentStatus));
                                                                        return;
                                                                      }
                                                                    }

                                                                    if (paymentStatus
                                                                            .runtimeType ==
                                                                        bool) {
                                                                      CustomToast.showToast(LocaleKeys
                                                                          .check_internet_connection
                                                                          .tr());
                                                                      actionSliderController
                                                                          .reset();
                                                                      return;
                                                                    }

                                                                    await Future.delayed(const Duration(
                                                                        seconds:
                                                                            1));
                                                                    await Navigator.push(
                                                                        context,
                                                                        PageTransition(
                                                                            alignment: Alignment.center,
                                                                            type: PageTransitionType.scale,
                                                                            child: FaceRecognitionView(
                                                                              debug: EMULATOR,
                                                                              child: BottomNavigationPage(),
                                                                              afterRecognized: () async {
                                                                                await sm.set(on_setting, true);
                                                                              },
                                                                            ))).then((value) {
                                                                      controller
                                                                          .reset();
                                                                    });
                                                                  },
                                                                  gradient:
                                                                      cSecondGradient,
                                                                  child: Text(
                                                                    LocaleKeys
                                                                        .arrived_work
                                                                        .tr(),
                                                                    overflow:
                                                                        TextOverflow
                                                                            .ellipsis,
                                                                    style:
                                                                        TextStyle(
                                                                      color:
                                                                          cWhiteColor,
                                                                      fontSize: context
                                                                              .isTablet
                                                                          ? 14.sp
                                                                          : 16.sp,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w600,
                                                                    ),
                                                                  ),
                                                                ),
                                                              )
                                                            ],
                                                          ).animate().fadeIn(
                                                              duration: 500.ms);
                                                        } else if (snapshot.data
                                                                ?.responseCode ==
                                                            ResultCode.HIDE) {
                                                          return Center(
                                                            child: Text(
                                                                "Loading..."),
                                                          );
                                                        } else {
                                                          Timer(
                                                              Duration(
                                                                  seconds: 5),
                                                              () async {
                                                            ///It's needed for on_work key
                                                            await prefs
                                                                .reload();
                                                            reInitRegAvailable();
                                                          });

                                                          if (snapshot.data
                                                                  ?.responseCode ==
                                                              ResultCode
                                                                  .SUCCESS) {
                                                            return Row(
                                                              children: [
                                                                Expanded(
                                                                    flex: 1,
                                                                    child: SvgPicture
                                                                        .asset(
                                                                      Assets
                                                                          .iconsCheckCircle,
                                                                      height:
                                                                          80.h,
                                                                      width:
                                                                          80.w,
                                                                    )),
                                                                SizedBox(
                                                                  width: 10.w,
                                                                ),
                                                                Expanded(
                                                                  flex: 5,
                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .spaceAround,
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Text(
                                                                        snapshot.data?.message ??
                                                                            'Loading...',
                                                                        style:
                                                                            TextStyle(
                                                                          fontSize:
                                                                              15.sp,
                                                                          fontWeight:
                                                                              FontWeight.w600,
                                                                          color:
                                                                              Theme.of(context).primaryColor,
                                                                        ),
                                                                        textAlign:
                                                                            TextAlign.left,
                                                                      ),
                                                                      SizedBox(
                                                                        height:
                                                                            10.h,
                                                                      ),
                                                                      Text(
                                                                        DateFormat('yyyy-MM-dd HH:mm:ss')
                                                                            .format(DateTime.now()),
                                                                        style:
                                                                            TextStyle(
                                                                          fontSize:
                                                                              20.sp,
                                                                          fontWeight:
                                                                              FontWeight.w600,
                                                                          color:
                                                                              Theme.of(context).primaryColor,
                                                                        ),
                                                                        textAlign:
                                                                            TextAlign.left,
                                                                      )
                                                                    ],
                                                                  ),
                                                                ),
                                                              ],
                                                            ).animate().fadeIn(
                                                                duration:
                                                                    500.ms);
                                                          } else if (snapshot
                                                                  .data
                                                                  ?.responseCode ==
                                                              ResultCode
                                                                  .SUCCESS_CHECKED) {
                                                            return Row(
                                                              children: [
                                                                Expanded(
                                                                    flex: 1,
                                                                    child: SvgPicture
                                                                        .asset(
                                                                      Assets
                                                                          .iconsCheckCircle,
                                                                      height:
                                                                          80.h,
                                                                      width:
                                                                          80.w,
                                                                    )),
                                                                SizedBox(
                                                                  width: 10.w,
                                                                ),
                                                                Expanded(
                                                                  flex: 5,
                                                                  child: Text(
                                                                    snapshot.data
                                                                            ?.message ??
                                                                        'Loading...',
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          15.sp,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w600,
                                                                      color: Theme.of(
                                                                              context)
                                                                          .primaryColor,
                                                                    ),
                                                                    textAlign:
                                                                        TextAlign
                                                                            .left,
                                                                  ),
                                                                ),
                                                              ],
                                                            ).animate().fadeIn(
                                                                duration:
                                                                    500.ms);
                                                          } else {
                                                            {
                                                              return Row(
                                                                children: [
                                                                  Expanded(
                                                                      flex: 1,
                                                                      child: SvgPicture
                                                                          .asset(
                                                                        Assets
                                                                            .iconsInfoCircle,
                                                                        height:
                                                                            80.h,
                                                                        width:
                                                                            80.w,
                                                                        color: Theme.of(context)
                                                                            .primaryColor,
                                                                      )),
                                                                  SizedBox(
                                                                    width: 10.w,
                                                                  ),
                                                                  Expanded(
                                                                    flex: 5,
                                                                    child:
                                                                        Column(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .center,
                                                                      crossAxisAlignment:
                                                                          CrossAxisAlignment
                                                                              .start,
                                                                      children: [
                                                                        Text(
                                                                          snapshot.data?.message ??
                                                                              'Loading...',
                                                                          style:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                15.sp,
                                                                            fontWeight:
                                                                                FontWeight.w600,
                                                                            color:
                                                                                Theme.of(context).primaryColor,
                                                                          ),
                                                                          textAlign:
                                                                              TextAlign.left,
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ).animate().fadeIn(
                                                                  duration:
                                                                      500.ms);
                                                            }
                                                          }
                                                        }
                                                      }
                                                  }
                                                }))
                                        .animate()
                                        .fadeIn(duration: 500.ms)
                                        .scale(
                                            duration: 500.ms,
                                            begin: Offset(0.8, 0.8),
                                            end: Offset(1, 1)),
                                  );
                              }
                            }),
                        SizedBox(
                          height: 10.h,
                        ),
                        ValueListenableBuilder<int>(
                            valueListenable: testCountNotifier,
                            builder: (context, data, widget) {
                              if (data > 0) {
                                return Column(
                                  children: [
                                    HomeQuizWidget(
                                      onTap: () {
                                        Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (context) =>
                                                    TestMainPage()));
                                      },
                                    ),
                                    SizedBox(
                                      height: 10.h,
                                    ),
                                  ],
                                );
                              } else {
                                return SizedBox();
                              }
                            }),
                        Visibility(
                          visible: todayWorks.total != todayWorks.success,
                          child: ZoomTapAnimation(
                            onTap: () {
                              Get.to(() => SubCategoriesTodayPage(
                                    subCategories: todayWorks.docs ?? [],
                                  ));
                            },
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.only(left: 20.w, right: 8.w),
                              margin: EdgeInsets.symmetric(horizontal: 20.w),
                              decoration: BoxDecoration(
                                color: Theme.of(context).cardTheme.color,
                                borderRadius: BorderRadius.circular(25.r),
                                boxShadow: [
                                  boxShadow60,
                                ],
                              ),
                              child: Row(
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(LocaleKeys.today_work.tr(),
                                          style: TextStyle(
                                            fontSize: 18.sp,
                                            fontWeight: FontWeight.w600,
                                          )),
                                      SizedBox(
                                        height: 5.h,
                                      ),
                                      Text(
                                          "${todayWorks.total ?? 0} ${LocaleKeys.count_work.tr()}",
                                          style: TextStyle(
                                            color: cGrayColor1,
                                            fontSize: 16.sp,
                                            fontWeight: FontWeight.w400,
                                          )),
                                    ],
                                  ),
                                  Spacer(),
                                  CircularProgress(
                                    total: todayWorks.total?.toDouble() ?? 0,
                                    done: todayWorks.success?.toDouble() ?? 0,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 14.h,
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 20.w),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(LocaleKeys.works_sector.tr(),
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w600,
                                )),
                          ),
                        ),
                        SizedBox(
                          height: 4.h,
                        ),
                        mainCategories.length > 0
                            ? Expanded(
                                child: RefreshIndicator(
                                  onRefresh: _onlineRefresh,
                                  child: FadingEdgeScrollView.fromScrollView(
                                    child: ListView(
                                      physics: BouncingScrollPhysics(
                                          parent:
                                              AlwaysScrollableScrollPhysics()),
                                      controller: PageController(),
                                      children: [
                                        RefreshIndicator(
                                          onRefresh: _onlineRefresh,
                                          child: FadingEdgeScrollView
                                              .fromScrollView(
                                            child: GridView.builder(
                                              physics: ScrollPhysics(),
                                              // to disable GridView's scrolling
                                              shrinkWrap: true,
                                              controller: _listController,
                                              padding: EdgeInsets.only(
                                                  left: 20.w,
                                                  right: 20.w,
                                                  bottom: context.isPhone
                                                      ? 30.h
                                                      : 10.h,
                                                  top: 10.h),
                                              // physics: BouncingScrollPhysics(
                                              //     parent:
                                              //         AlwaysScrollableScrollPhysics()),
                                              // shrinkWrap: false,
                                              gridDelegate:
                                                  SliverGridDelegateWithFixedCrossAxisCount(
                                                crossAxisCount: context.isTablet
                                                    ? 3
                                                    : context.isSmallTablet
                                                        ? 3
                                                        : context.isPhone
                                                            ? 2
                                                            : 4,
                                                crossAxisSpacing: 15.w,
                                                mainAxisSpacing: 15.h,
                                              ),
                                              itemCount: mainCategories.length,
                                              itemBuilder: (context, index) {
                                                return ZoomTapAnimation(
                                                  onTap: () {
                                                    Get.to(SubCategoriesPage(
                                                      categoryId:
                                                          mainCategories[index]
                                                                  .category
                                                                  ?.id ??
                                                              '-',
                                                      categoryName: categoryTitle(
                                                          category:
                                                              mainCategories[
                                                                      index]
                                                                  .category),
                                                      subCategories:
                                                          mainCategories[index]
                                                                  .subCategorys ??
                                                              [],
                                                    ));
                                                  },
                                                  child: Container(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 20.h,
                                                            vertical: 10.h),
                                                    decoration: BoxDecoration(
                                                      color: Theme.of(context)
                                                          .cardTheme
                                                          .color,
                                                      boxShadow: [
                                                        boxShadow20,
                                                      ],
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              20.r),
                                                    ),
                                                    child: Column(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      children: [
                                                        mainCategories[index]
                                                                    .value !=
                                                                0
                                                            ? Text(
                                                                mainCategories[
                                                                        index]
                                                                    .value
                                                                    .toString(),
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: context
                                                                          .isPhone
                                                                      ? 40.sp
                                                                      : 30.sp,
                                                                  color: Theme.of(
                                                                          context)
                                                                      .primaryColor,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                ))
                                                            : Expanded(
                                                                child:
                                                                    SvgPicture
                                                                        .asset(
                                                                  Assets
                                                                      .iconsCheckCircleBig,
                                                                  height: context
                                                                          .isPhone
                                                                      ? 40.h
                                                                      : 30.h,
                                                                  colorFilter: ColorFilter.mode(
                                                                      Theme.of(
                                                                              context)
                                                                          .primaryColor,
                                                                      BlendMode
                                                                          .srcIn),
                                                                ),
                                                              ),
                                                        Expanded(
                                                          child: Text(
                                                              categoryTitle(
                                                                  category: mainCategories[
                                                                          index]
                                                                      .category),
                                                              textAlign:
                                                                  TextAlign
                                                                      .center,
                                                              maxLines: 3,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              style: TextStyle(
                                                                fontSize: context
                                                                        .isPhone
                                                                    ? 16.sp
                                                                    : 10.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                              )),
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 20.w),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                  child: ZoomTapAnimation(
                                                onTap: () {
                                                  Get.to(ElectronLibraryPage.screen());
                                                },
                                                child: Container(
                                                  padding: EdgeInsets.all(4),
                                                  height: 80.h,
                                                  decoration: BoxDecoration(
                                                      color: isDark()
                                                          ? cFirstColor
                                                              .withAlpha(40)
                                                          : cFirstColor
                                                              .withAlpha(8),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              cRadius16.r),
                                                      border: Border.all(
                                                          width: 1.w,
                                                          color: cFirstColor)),
                                                  child: Row(
                                                    children: [
                                                      Image.asset(
                                                        Assets.iconsBookPng,
                                                        width: 60.h,
                                                        height: 60.h,
                                                      ),
                                                      SizedBox(
                                                        width: 4.w,
                                                      ),
                                                      Flexible(
                                                        child: Text(
                                                            LocaleKeys
                                                                .digital_library
                                                                .tr(),
                                                            style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600)),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              )),
                                              SizedBox(
                                                width: 10.w,
                                              ),
                                              Expanded(
                                                  child: ZoomTapAnimation(
                                                onTap: () {
                                                  Get.to(YetakchiAiPage.screen());
                                                },
                                                child: Container(
                                                  padding: EdgeInsets.all(4),
                                                  height: 80.h,
                                                  decoration: BoxDecoration(
                                                      color: isDark()
                                                          ? cGreenColor
                                                              .withAlpha(40)
                                                          : cGreenColor
                                                              .withAlpha(8),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              cRadius16.r),
                                                      border: Border.all(
                                                          width: 1.w,
                                                          color: cGreenColor)),
                                                  child: Row(
                                                    children: [
                                                      Image.asset(
                                                        Assets.imagesYetakchiAi,
                                                        width: 40.h,
                                                        height: 40.h,
                                                      ),
                                                      SizedBox(
                                                        width: 4.w,
                                                      ),
                                                      Flexible(
                                                        child: Text(
                                                          "Yetakchi AI",
                                                          style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              )),
                                            ],
                                          ),
                                        ),
                                        SizedBox(height: 80.h,)
                                        ///TODO need to remove
                                        // RoundedBookReadingWidget()
                                      ],
                                    ),
                                  ),
                                ),
                              )
                            : SingleChildScrollView(
                                physics: BouncingScrollPhysics(),
                                child: Container(
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        ClipRect(
                                          child: Container(
                                            height: 300.h,
                                            child: Column(
                                              children: [
                                                SizedBox(
                                                  height: 20.h,
                                                ),
                                                Expanded(
                                                    child: Image.asset(
                                                  Assets.iconsEmpty,
                                                  height: 140.h,
                                                )),
                                                Padding(
                                                    padding: EdgeInsets.only(
                                                        top: 10.h,
                                                        left: 30.w,
                                                        right: 30.w,
                                                        bottom: 10.h),
                                                    child: Text(
                                                      LocaleKeys.not_have_plan
                                                          .tr(),
                                                      textAlign:
                                                          TextAlign.center,
                                                      style: TextStyle(
                                                          color: cGrayColor1),
                                                    )),
                                                CupertinoButton(
                                                    child: Text(
                                                      LocaleKeys.refresh.tr(),
                                                      style: TextStyle(
                                                          color: cGrayColor1),
                                                    ),
                                                    color: cGrayColor1
                                                        .withAlpha(80),
                                                    onPressed: () {
                                                      _onlineRefresh();
                                                    }),
                                                SizedBox(
                                                  height: 20.h,
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                      ],
                    ),
                  );
                } else if (state is HomeEmptyState) {
                  return Expanded(
                    child: Container(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ClipRect(
                              child: Container(
                                height: 300.h,
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    Expanded(
                                        child: Image.asset(
                                      Assets.iconsEmpty,
                                      height: 140.h,
                                    )),
                                    Padding(
                                        padding: EdgeInsets.only(
                                            top: 10.h,
                                            left: 30.w,
                                            right: 30.w,
                                            bottom: 10.h),
                                        child: Text(
                                          LocaleKeys.not_have_plan.tr(),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(color: cGrayColor1),
                                        )),
                                    CupertinoButton(
                                        child: Text(
                                          LocaleKeys.refresh.tr(),
                                          style: TextStyle(color: cGrayColor1),
                                        ),
                                        color: cGrayColor1.withAlpha(80),
                                        onPressed: () {
                                          _onlineRefresh();
                                        }),
                                    SizedBox(
                                      height: 20.h,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                } else if (state is NoInternetConnectionState) {
                  return Expanded(
                    child: Container(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ClipRect(
                              child: Container(
                                height: 300.h,
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    Expanded(
                                        child: Image.asset(
                                      Assets.imagesNoConnection,
                                      height: 140.h,
                                    )),
                                    Padding(
                                        padding: EdgeInsets.only(
                                            top: 10.h,
                                            left: 30.w,
                                            right: 30.w,
                                            bottom: 10.h),
                                        child: Text(
                                          LocaleKeys.check_internet_connection
                                              .tr(),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(color: cGrayColor1),
                                        )),
                                    CupertinoButton(
                                        child: Text(
                                          LocaleKeys.refresh.tr(),
                                          style: TextStyle(color: cGrayColor1),
                                        ),
                                        color: cGrayColor1.withAlpha(80),
                                        onPressed: () {
                                          _onlineRefresh();
                                        }),
                                    SizedBox(
                                      height: 20.h,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                } else {
                  return Expanded(
                    child: Container(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ClipRect(
                              child: Container(
                                height: 300.h,
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    Expanded(
                                        child: SvgPicture.asset(
                                      Assets.iconsWarning,
                                      height: 140.h,
                                    )),
                                    Padding(
                                        padding: EdgeInsets.only(
                                            top: 10.h,
                                            left: 30.w,
                                            right: 30.w,
                                            bottom: 10.h),
                                        child: Text(
                                          LocaleKeys.error.tr(),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(color: cGrayColor1),
                                        )),
                                    CupertinoButton(
                                        child: Text(
                                          LocaleKeys.refresh.tr(),
                                          style: TextStyle(color: cGrayColor1),
                                        ),
                                        color: cGrayColor1.withAlpha(80),
                                        onPressed: () {
                                          _onlineRefresh();
                                        }),
                                    SizedBox(
                                      height: 20.h,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}

class CircularProgress extends StatelessWidget {
  const CircularProgress({required this.total, required this.done, super.key});

  final double total;
  final double done;

  @override
  Widget build(BuildContext context) {
    print("retry build: ${isDark()}");
    return ConstrainedBox(
      constraints: BoxConstraints(),
      child: AnimatedCircularChart(
        edgeStyle: SegmentEdgeStyle.round,
        size: Size(100.h, 100.h),
        holeRadius: 22.r,
        initialChartData: <CircularStackEntry>[
          new CircularStackEntry(
            <CircularSegmentEntry>[
              new CircularSegmentEntry(
                done,
                isDark() ? cPrimaryTextDark : cFirstColor,
                rankKey: 'completed',
              ),
              new CircularSegmentEntry(
                total - done,
                isDark()
                    ? cPrimaryTextDark.withAlpha(30)
                    : cFirstColor.withAlpha(30),
                rankKey: 'remaining',
              ),
            ],
            rankKey: 'progress',
          ),
        ],
        chartType: CircularChartType.Radial,
        percentageValues: false,
        holeLabel: "${(total - done).toInt()}",
        labelStyle: new TextStyle(
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.w600,
          fontSize: 24.sp,
        ),
      ),
    );
  }
}
