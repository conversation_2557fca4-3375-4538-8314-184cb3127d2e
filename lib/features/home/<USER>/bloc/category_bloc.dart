import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:meta/meta.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/features/home/<USER>/models/category_model.dart';
import 'package:yetakchi/features/home/<USER>/models/today_works.dart';
import 'package:yetakchi/features/home/<USER>/usescases/u_category.dart';

part 'category_event.dart';

part 'category_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final UHome home;

  HomeBloc({
    required this.home,
  }) : super(const HomeInitialState()) {
    on<GetHome>(getHome, transformer: droppable());
  }

  FutureOr<void> getHome(
      GetHome event, Emitter<HomeState> emit) async {
    emit(const HomeLoadingState());
    final result = await home(GetHomeParams(event.refresh));
    result.fold(
        (failure) => {
              ///We don't need this state now
              if (failure is NoConnectionFailure)
                {emit(const NoInternetConnectionState())}
              else if (failure is ServerFailure)
                {emit(const HomeFailureState())}
            },
        (r) => {
              if (r is HomeSuccessState)
                {
                  emit(HomeSuccessState(
                      mainCategoryList: r.mainCategoryList,
                      todayWorks: r.todayWorks,
                      specialTaskCount: r.specialTaskCount))
                }
              else
                {
                  emit(HomeSuccessState(
                      mainCategoryList: [],
                      specialTaskCount: 0,
                      todayWorks: TodayWorks()))
                }
            });
  }
}
