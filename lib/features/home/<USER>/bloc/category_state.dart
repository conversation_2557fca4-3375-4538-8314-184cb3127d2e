part of 'category_bloc.dart';

@immutable
abstract class HomeState {
  const HomeState();
}

class HomeInitialState extends HomeState {
  const HomeInitialState() : super();
}

class HomeLoadingState extends HomeState {
  const HomeLoadingState() : super();
}

class HomeSuccessState extends HomeState {
  final List<CategoryModel> mainCategoryList;
  final TodayWorks todayWorks;
  final int specialTaskCount;

  HomeSuccessState({required this.mainCategoryList, required this.todayWorks, required this.specialTaskCount});

}

class HomeFailureState extends HomeState {
  const HomeFailureState() : super();
}

class NoInternetConnectionState extends HomeState {
  const NoInternetConnectionState() : super();
}

class HomeEmptyState extends HomeState {
  const HomeEmptyState() : super();
}
