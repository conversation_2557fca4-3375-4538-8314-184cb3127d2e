import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/features/home/<USER>/models/category_model.dart';
import 'package:yetakchi/features/home/<USER>/models/support_model.dart';
import 'package:yetakchi/features/home/<USER>/models/today_works.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

abstract class HomeRemoteDatasource {
  Future<List<dynamic>> getHome();
}

///TODO: Add these exceptions to interceptors
class HomeRemoteDatasourceImpl implements HomeRemoteDatasource {
  final SharedPreferences sharedPreferences;
  final Dio dioClient;
  List<CategoryModel> categoriesList = [];
  List<SupportModel> supportList = [];
  int specialTaskCount = 0;
  int testCount = 0;
  TodayWorks todayWorks = TodayWorks();

  HomeRemoteDatasourceImpl(
      {required this.sharedPreferences, required this.dioClient});

  @override
  Future<List<dynamic>> getHome() async {
    var isDemo = sharedPreferences.getBool(is_demo) ?? false;

    categoriesList = await getCategories();
    if (!isDemo) {
      specialTaskCount = await getSpecialTaskCount();
    } else {
      specialTaskCount = 5;
    }
    todayWorks = await getTodayWorks();
    supportList = await getSupports();
    testCount = await getTestCount();

    updateFirebaseToken();

    return [
      categoriesList,
      todayWorks,
      specialTaskCount,
      supportList,
      testCount
    ];
  }

  Future<List<CategoryModel>> getCategories() async {
    categoriesList.clear();

    try {
      try {
        final response = await dioClient.get(categoriesPath);

        var data = response.data;
        print(data);

        if (response.statusCode == 200) {
          for (int i = 0; i < (data ?? []).length; i++) {
            categoriesList.add(CategoryModel.fromJson(data[i]));
          }

          print("Categories ------------------ " +
              categoriesList.length.toString());

          return categoriesList;
        } else {
          return categoriesList;
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            CustomToast.showToast("Xatolik: ${e.response?.data['message']}");
          }
          throw (ServerFailure('bad response'));
        }
        if (e.type == DioExceptionType.connectionError) {
          print('check your connection');
          throw (NoConnectionFailure('no connection'));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          print('unable to connect to the server');
          throw (ServerFailure('time out'));
        }

        if (e.type == DioExceptionType.unknown) {
          print('Something went wrong');
          throw (ServerFailure('unknown'));
        }
        throw (ServerFailure('unknown'));
      } catch (e) {
        print(e);
        CustomToast.showToast("Server formati xato!");
        throw (ServerFailure('format exception'));
      }
    } on InputFormatterFailure catch (e) {
      print('Input formatter error');
      throw (ServerFailure('input formatter failure'));
    }
  }

  Future<TodayWorks> getTodayWorks() async {
    todayWorks = TodayWorks();

    try {
      try {
        final response = await dioClient.get(todayWorksPath);

        var data = response.data;
        print(data);

        if (response.statusCode == 200) {
          if (data != null) {
            todayWorks = TodayWorks.fromJson(data);

            var count = todayWorks.docs?.length ?? 0;

            print("Today Works ------------------ " + count.toString());
          }

          return todayWorks;
        } else {
          return todayWorks;
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            CustomToast.showToast(
                "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}");
          }
          throw (ServerFailure('bad response'));
        }
        if (e.type == DioExceptionType.connectionError) {
          print('check your connection');
          throw (NoConnectionFailure('no connection'));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          print('unable to connect to the server');
          throw (ServerFailure('time out'));
        }

        if (e.type == DioExceptionType.unknown) {
          print('Something went wrong');
          throw (ServerFailure('unknown'));
        }
        throw (ServerFailure('unknown'));
      } catch (e) {
        print(e);
        CustomToast.showToast(LocaleKeys.server_form_error.tr());
        throw (ServerFailure('format exception'));
      }
    } on InputFormatterFailure catch (e) {
      print('Input formatter error');
      throw (ServerFailure('input formatter failure'));
    }
  }

  Future<int> getSpecialTaskCount() async {
    try {
      try {
        final response =
            await dioClient.post(specialTaskPath, data: {"done": "false"});
        var data = response.data;
        print(data);

        if (response.statusCode == 200) {
          var count = data['totalDocs'] ?? 0;
          return count;
        } else {
          return 0;
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            CustomToast.showToast(
                "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}");
          }
          throw (ServerFailure('bad response'));
        }
        if (e.type == DioExceptionType.connectionError) {
          print('check your connection');
          throw (NoConnectionFailure('no connection'));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          print('unable to connect to the server');
          throw (ServerFailure('time out'));
        }

        if (e.type == DioExceptionType.unknown) {
          print('Something went wrong');
          throw (ServerFailure('unknown'));
        }
        throw (ServerFailure('unknown'));
      } catch (e) {
        print(e);
        CustomToast.showToast(LocaleKeys.server_form_error.tr());
        throw (ServerFailure('format exception'));
      }
    } on InputFormatterFailure catch (e) {
      print('Input formatter error');
      throw (ServerFailure('input formatter failure'));
    }
  }

  Future<List<SupportModel>> getSupports() async {
    supportList.clear();

    try {
      try {
        final response = await dioClient.get(supportTypePath);

        var data = response.data;
        print(data);

        if (response.statusCode == 200) {
          for (int i = 0; i < (data ?? []).length; i++) {
            supportList.add(SupportModel.fromJson(data[i]));
          }

          print("Support types ------------------ " +
              supportList.length.toString());

          return supportList;
        } else {
          return supportList;
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            CustomToast.showToast(
                "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}");
          }
          throw (ServerFailure('bad response'));
        }
        if (e.type == DioExceptionType.connectionError) {
          print('check your connection');
          throw (NoConnectionFailure('no connection'));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          print('unable to connect to the server');
          throw (ServerFailure('time out'));
        }

        if (e.type == DioExceptionType.unknown) {
          print('Something went wrong');
          throw (ServerFailure('unknown'));
        }
        throw (ServerFailure('unknown'));
      } catch (e) {
        print(e);
        CustomToast.showToast(LocaleKeys.server_form_error.tr());
        throw (ServerFailure('format exception'));
      }
    } on InputFormatterFailure catch (e) {
      print('Input formatter error');
      throw (ServerFailure('input formatter failure'));
    }
  }

  Future<int> getTestCount() async {
    try {
      try {
        var response = await dioClient
            .post(getNewQuizPath, data: {"page": 1, "limit": 10});

        var data = response.data;
        print(data);

        if (response.statusCode == 200) {
          var count = data['totalDocs'] ?? 0;
          return count;
        } else {
          return 0;
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            CustomToast.showToast(
                "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}");
          }
          throw (ServerFailure('bad response'));
        }
        if (e.type == DioExceptionType.connectionError) {
          print('check your connection');
          throw (NoConnectionFailure('no connection'));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          print('unable to connect to the server');
          throw (ServerFailure('time out'));
        }

        if (e.type == DioExceptionType.unknown) {
          print('Something went wrong');
          throw (ServerFailure('unknown'));
        }
        throw (ServerFailure('unknown'));
      } catch (e) {
        print(e);
        CustomToast.showToast(LocaleKeys.server_form_error.tr());
        throw (ServerFailure('format exception'));
      }
    } on InputFormatterFailure catch (e) {
      print('Input formatter error');
      throw (ServerFailure('input formatter failure'));
    }
  }
}
