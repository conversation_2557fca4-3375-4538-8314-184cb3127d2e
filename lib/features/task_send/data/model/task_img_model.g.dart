// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_img_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetTaskImgModelCollection on Isar {
  IsarCollection<TaskImgModel> get taskImgModels => this.collection();
}

const TaskImgModelSchema = CollectionSchema(
  name: r'TaskImgModel',
  id: -2425984201523507097,
  properties: {
    r'desc': PropertySchema(
      id: 0,
      name: r'desc',
      type: IsarType.string,
    ),
    r'image': PropertySchema(
      id: 1,
      name: r'image',
      type: IsarType.string,
    ),
    r'lat': PropertySchema(
      id: 2,
      name: r'lat',
      type: IsarType.string,
    ),
    r'lng': PropertySchema(
      id: 3,
      name: r'lng',
      type: IsarType.string,
    ),
    r'recieveDate': PropertySchema(
      id: 4,
      name: r'recieveDate',
      type: IsarType.string,
    ),
    r'sendDate': PropertySchema(
      id: 5,
      name: r'sendDate',
      type: IsarType.string,
    ),
    r'taskId': PropertySchema(
      id: 6,
      name: r'taskId',
      type: IsarType.string,
    ),
    r'text': PropertySchema(
      id: 7,
      name: r'text',
      type: IsarType.string,
    ),
    r'titleQQ': PropertySchema(
      id: 8,
      name: r'titleQQ',
      type: IsarType.string,
    ),
    r'titleRU': PropertySchema(
      id: 9,
      name: r'titleRU',
      type: IsarType.string,
    ),
    r'titleUZ': PropertySchema(
      id: 10,
      name: r'titleUZ',
      type: IsarType.string,
    ),
    r'user': PropertySchema(
      id: 11,
      name: r'user',
      type: IsarType.object,
      target: r'User',
    ),
    r'video': PropertySchema(
      id: 12,
      name: r'video',
      type: IsarType.string,
    ),
    r'withPhoto': PropertySchema(
      id: 13,
      name: r'withPhoto',
      type: IsarType.bool,
    ),
    r'withText': PropertySchema(
      id: 14,
      name: r'withText',
      type: IsarType.bool,
    ),
    r'withVideo': PropertySchema(
      id: 15,
      name: r'withVideo',
      type: IsarType.bool,
    )
  },
  estimateSize: _taskImgModelEstimateSize,
  serialize: _taskImgModelSerialize,
  deserialize: _taskImgModelDeserialize,
  deserializeProp: _taskImgModelDeserializeProp,
  idName: r'localId',
  indexes: {},
  links: {},
  embeddedSchemas: {r'User': UserSchema},
  getId: _taskImgModelGetId,
  getLinks: _taskImgModelGetLinks,
  attach: _taskImgModelAttach,
  version: '3.1.0+1',
);

int _taskImgModelEstimateSize(
  TaskImgModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.desc;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.image;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.lat;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.lng;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.recieveDate;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.sendDate;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.taskId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.text;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleQQ;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleRU;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.titleUZ;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.user;
    if (value != null) {
      bytesCount +=
          3 + UserSchema.estimateSize(value, allOffsets[User]!, allOffsets);
    }
  }
  {
    final value = object.video;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _taskImgModelSerialize(
  TaskImgModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.desc);
  writer.writeString(offsets[1], object.image);
  writer.writeString(offsets[2], object.lat);
  writer.writeString(offsets[3], object.lng);
  writer.writeString(offsets[4], object.recieveDate);
  writer.writeString(offsets[5], object.sendDate);
  writer.writeString(offsets[6], object.taskId);
  writer.writeString(offsets[7], object.text);
  writer.writeString(offsets[8], object.titleQQ);
  writer.writeString(offsets[9], object.titleRU);
  writer.writeString(offsets[10], object.titleUZ);
  writer.writeObject<User>(
    offsets[11],
    allOffsets,
    UserSchema.serialize,
    object.user,
  );
  writer.writeString(offsets[12], object.video);
  writer.writeBool(offsets[13], object.withPhoto);
  writer.writeBool(offsets[14], object.withText);
  writer.writeBool(offsets[15], object.withVideo);
}

TaskImgModel _taskImgModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = TaskImgModel(
    desc: reader.readStringOrNull(offsets[0]),
    image: reader.readStringOrNull(offsets[1]),
    lat: reader.readStringOrNull(offsets[2]),
    lng: reader.readStringOrNull(offsets[3]),
    recieveDate: reader.readStringOrNull(offsets[4]),
    sendDate: reader.readStringOrNull(offsets[5]),
    taskId: reader.readStringOrNull(offsets[6]),
    text: reader.readStringOrNull(offsets[7]),
    titleQQ: reader.readStringOrNull(offsets[8]),
    titleRU: reader.readStringOrNull(offsets[9]),
    titleUZ: reader.readStringOrNull(offsets[10]),
    user: reader.readObjectOrNull<User>(
      offsets[11],
      UserSchema.deserialize,
      allOffsets,
    ),
    video: reader.readStringOrNull(offsets[12]),
    withPhoto: reader.readBoolOrNull(offsets[13]),
    withText: reader.readBoolOrNull(offsets[14]),
    withVideo: reader.readBoolOrNull(offsets[15]),
  );
  object.localId = id;
  return object;
}

P _taskImgModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readObjectOrNull<User>(
        offset,
        UserSchema.deserialize,
        allOffsets,
      )) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    case 13:
      return (reader.readBoolOrNull(offset)) as P;
    case 14:
      return (reader.readBoolOrNull(offset)) as P;
    case 15:
      return (reader.readBoolOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _taskImgModelGetId(TaskImgModel object) {
  return object.localId;
}

List<IsarLinkBase<dynamic>> _taskImgModelGetLinks(TaskImgModel object) {
  return [];
}

void _taskImgModelAttach(
    IsarCollection<dynamic> col, Id id, TaskImgModel object) {
  object.localId = id;
}

extension TaskImgModelQueryWhereSort
    on QueryBuilder<TaskImgModel, TaskImgModel, QWhere> {
  QueryBuilder<TaskImgModel, TaskImgModel, QAfterWhere> anyLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension TaskImgModelQueryWhere
    on QueryBuilder<TaskImgModel, TaskImgModel, QWhereClause> {
  QueryBuilder<TaskImgModel, TaskImgModel, QAfterWhereClause> localIdEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localId,
        upper: localId,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterWhereClause> localIdNotEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterWhereClause>
      localIdGreaterThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localId, includeLower: include),
      );
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterWhereClause> localIdLessThan(
      Id localId,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localId, includeUpper: include),
      );
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterWhereClause> localIdBetween(
    Id lowerLocalId,
    Id upperLocalId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalId,
        includeLower: includeLower,
        upper: upperLocalId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension TaskImgModelQueryFilter
    on QueryBuilder<TaskImgModel, TaskImgModel, QFilterCondition> {
  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> descIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'desc',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      descIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'desc',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> descEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      descGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> descLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> descBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'desc',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      descStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> descEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> descContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> descMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'desc',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      descIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'desc',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      descIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'desc',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      imageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'image',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      imageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'image',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> imageEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'image',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      imageGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'image',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> imageLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'image',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> imageBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'image',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      imageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'image',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> imageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'image',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> imageContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'image',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> imageMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'image',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      imageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'image',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      imageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'image',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> latIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lat',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      latIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lat',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> latEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      latGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> latLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> latBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lat',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> latStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'lat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> latEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'lat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> latContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'lat',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> latMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'lat',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> latIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lat',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      latIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'lat',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> lngIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lng',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      lngIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lng',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> lngEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lng',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      lngGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lng',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> lngLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lng',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> lngBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lng',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> lngStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'lng',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> lngEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'lng',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> lngContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'lng',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> lngMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'lng',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> lngIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lng',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      lngIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'lng',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      localIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      localIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      localIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      localIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'recieveDate',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'recieveDate',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recieveDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recieveDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recieveDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recieveDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'recieveDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'recieveDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'recieveDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'recieveDate',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recieveDate',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      recieveDateIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'recieveDate',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sendDate',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sendDate',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sendDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sendDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sendDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sendDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sendDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sendDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sendDate',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sendDate',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sendDate',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      sendDateIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sendDate',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      taskIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'taskId',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      taskIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'taskId',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> taskIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'taskId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      taskIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'taskId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      taskIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'taskId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> taskIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'taskId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      taskIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'taskId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      taskIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'taskId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      taskIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'taskId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> taskIdMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'taskId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      taskIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'taskId',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      taskIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'taskId',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> textIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'text',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      textIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'text',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> textEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'text',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      textGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'text',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> textLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'text',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> textBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'text',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      textStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'text',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> textEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'text',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> textContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'text',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> textMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'text',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      textIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'text',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      textIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'text',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleQQ',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleQQ',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleQQ',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleQQ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleQQ',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleQQ',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleQQIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleQQ',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleRU',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleRU',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRULessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleRU',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleRU',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleRU',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleRU',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleRUIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleRU',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'titleUZ',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'titleUZ',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'titleUZ',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'titleUZ',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'titleUZ',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'titleUZ',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      titleUZIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'titleUZ',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> userIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'user',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      userIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'user',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      videoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'video',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      videoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'video',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> videoEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'video',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      videoGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'video',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> videoLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'video',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> videoBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'video',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      videoStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'video',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> videoEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'video',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> videoContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'video',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> videoMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'video',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      videoIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'video',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      videoIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'video',
        value: '',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      withPhotoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withPhoto',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      withPhotoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withPhoto',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      withPhotoEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withPhoto',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      withTextIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withText',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      withTextIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withText',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      withTextEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withText',
        value: value,
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      withVideoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'withVideo',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      withVideoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'withVideo',
      ));
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition>
      withVideoEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'withVideo',
        value: value,
      ));
    });
  }
}

extension TaskImgModelQueryObject
    on QueryBuilder<TaskImgModel, TaskImgModel, QFilterCondition> {
  QueryBuilder<TaskImgModel, TaskImgModel, QAfterFilterCondition> user(
      FilterQuery<User> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'user');
    });
  }
}

extension TaskImgModelQueryLinks
    on QueryBuilder<TaskImgModel, TaskImgModel, QFilterCondition> {}

extension TaskImgModelQuerySortBy
    on QueryBuilder<TaskImgModel, TaskImgModel, QSortBy> {
  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByDescDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByImage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'image', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByImageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'image', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByLat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lat', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByLatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lat', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByLng() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lng', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByLngDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lng', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByRecieveDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recieveDate', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy>
      sortByRecieveDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recieveDate', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortBySendDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sendDate', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortBySendDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sendDate', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByTaskId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'taskId', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByTaskIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'taskId', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByText() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'text', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByTextDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'text', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByTitleQQ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByTitleQQDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByTitleRU() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByTitleRUDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByTitleUZ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByTitleUZDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'video', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByVideoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'video', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByWithPhoto() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByWithPhotoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByWithText() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByWithTextDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByWithVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> sortByWithVideoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.desc);
    });
  }
}

extension TaskImgModelQuerySortThenBy
    on QueryBuilder<TaskImgModel, TaskImgModel, QSortThenBy> {
  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByDescDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByImage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'image', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByImageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'image', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByLat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lat', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByLatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lat', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByLng() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lng', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByLngDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lng', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByLocalIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByRecieveDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recieveDate', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy>
      thenByRecieveDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recieveDate', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenBySendDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sendDate', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenBySendDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sendDate', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByTaskId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'taskId', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByTaskIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'taskId', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByText() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'text', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByTextDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'text', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByTitleQQ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByTitleQQDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleQQ', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByTitleRU() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByTitleRUDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleRU', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByTitleUZ() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByTitleUZDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'titleUZ', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'video', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByVideoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'video', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByWithPhoto() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByWithPhotoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withPhoto', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByWithText() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByWithTextDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withText', Sort.desc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByWithVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.asc);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QAfterSortBy> thenByWithVideoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'withVideo', Sort.desc);
    });
  }
}

extension TaskImgModelQueryWhereDistinct
    on QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> {
  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByDesc(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'desc', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByImage(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'image', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByLat(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lat', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByLng(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lng', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByRecieveDate(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recieveDate', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctBySendDate(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sendDate', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByTaskId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'taskId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByText(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'text', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByTitleQQ(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleQQ', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByTitleRU(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleRU', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByTitleUZ(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'titleUZ', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByVideo(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'video', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByWithPhoto() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withPhoto');
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByWithText() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withText');
    });
  }

  QueryBuilder<TaskImgModel, TaskImgModel, QDistinct> distinctByWithVideo() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'withVideo');
    });
  }
}

extension TaskImgModelQueryProperty
    on QueryBuilder<TaskImgModel, TaskImgModel, QQueryProperty> {
  QueryBuilder<TaskImgModel, int, QQueryOperations> localIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localId');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> descProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'desc');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> imageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'image');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> latProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lat');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> lngProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lng');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> recieveDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recieveDate');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> sendDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sendDate');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> taskIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'taskId');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> textProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'text');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> titleQQProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleQQ');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> titleRUProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleRU');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> titleUZProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'titleUZ');
    });
  }

  QueryBuilder<TaskImgModel, User?, QQueryOperations> userProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'user');
    });
  }

  QueryBuilder<TaskImgModel, String?, QQueryOperations> videoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'video');
    });
  }

  QueryBuilder<TaskImgModel, bool?, QQueryOperations> withPhotoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withPhoto');
    });
  }

  QueryBuilder<TaskImgModel, bool?, QQueryOperations> withTextProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withText');
    });
  }

  QueryBuilder<TaskImgModel, bool?, QQueryOperations> withVideoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'withVideo');
    });
  }
}
