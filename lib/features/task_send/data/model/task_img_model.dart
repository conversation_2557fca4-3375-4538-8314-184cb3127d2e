import 'package:isar/isar.dart';
import 'package:yetakchi/features/tasks/data/model/user.dart';

part 'task_img_model.g.dart';

@collection
@Name("TaskImgModel")
class TaskImgModel {
  Id localId = Isar.autoIncrement;
  String? taskId;
  String? text;
  String? lat;
  String? lng;
  String? sendDate;
  String? image;
  String? video;
  bool? withPhoto;
  bool? withVideo;
  bool? withText;
  String? recieveDate;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;
  String? desc;
  User? user;

  TaskImgModel(
      {this.taskId,
      this.text,
      this.lat,
      this.lng,
      this.sendDate,
      this.image,
      this.video,
      this.withVideo,
      this.withPhoto,
      this.withText,
      this.recieveDate,
      this.titleUZ,
      this.titleRU,
      this.titleQQ,
      this.desc,
      this.user});

  TaskImgModel.fromJson(Map<String, dynamic> json) {
    taskId = json['taskId'];
    text = json['desc'];
    lat = json['lat'];
    lng = json['lng'];
    sendDate = json['sana'];
    image = json['image'];
    video = json['video'];
  }
}
