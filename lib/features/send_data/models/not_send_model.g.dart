// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'not_send_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetNotSendModelCollection on Isar {
  IsarCollection<NotSendModel> get notSendModels => this.collection();
}

const NotSendModelSchema = CollectionSchema(
  name: r'NotSendModel',
  id: -3590836413356714362,
  properties: {
    r'categoryId': PropertySchema(
      id: 0,
      name: r'categoryId',
      type: IsarType.string,
    ),
    r'categoryTitle': PropertySchema(
      id: 1,
      name: r'categoryTitle',
      type: IsarType.string,
    ),
    r'desc': PropertySchema(
      id: 2,
      name: r'desc',
      type: IsarType.string,
    ),
    r'individual': PropertySchema(
      id: 3,
      name: r'individual',
      type: IsarType.string,
    ),
    r'mediaList': PropertySchema(
      id: 4,
      name: r'mediaList',
      type: IsarType.objectList,
      target: r'ImgModel',
    ),
    r'publicCount': PropertySchema(
      id: 5,
      name: r'publicCount',
      type: IsarType.long,
    ),
    r'sound': PropertySchema(
      id: 6,
      name: r'sound',
      type: IsarType.string,
    ),
    r'status': PropertySchema(
      id: 7,
      name: r'status',
      type: IsarType.long,
    ),
    r'subCategoryId': PropertySchema(
      id: 8,
      name: r'subCategoryId',
      type: IsarType.string,
    ),
    r'subCategoryTitle': PropertySchema(
      id: 9,
      name: r'subCategoryTitle',
      type: IsarType.string,
    ),
    r'supportType': PropertySchema(
      id: 10,
      name: r'supportType',
      type: IsarType.string,
    ),
    r'supportedPerson': PropertySchema(
      id: 11,
      name: r'supportedPerson',
      type: IsarType.string,
    ),
    r'webEdit': PropertySchema(
      id: 12,
      name: r'webEdit',
      type: IsarType.bool,
    )
  },
  estimateSize: _notSendModelEstimateSize,
  serialize: _notSendModelSerialize,
  deserialize: _notSendModelDeserialize,
  deserializeProp: _notSendModelDeserializeProp,
  idName: r'localId',
  indexes: {},
  links: {},
  embeddedSchemas: {r'ImgModel': ImgModelSchema},
  getId: _notSendModelGetId,
  getLinks: _notSendModelGetLinks,
  attach: _notSendModelAttach,
  version: '3.1.0+1',
);

int _notSendModelEstimateSize(
  NotSendModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.categoryId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.categoryTitle;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.desc;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.individual;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final list = object.mediaList;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        final offsets = allOffsets[ImgModel]!;
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += ImgModelSchema.estimateSize(value, offsets, allOffsets);
        }
      }
    }
  }
  {
    final value = object.sound;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.subCategoryId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.subCategoryTitle;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.supportType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.supportedPerson;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _notSendModelSerialize(
  NotSendModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.categoryId);
  writer.writeString(offsets[1], object.categoryTitle);
  writer.writeString(offsets[2], object.desc);
  writer.writeString(offsets[3], object.individual);
  writer.writeObjectList<ImgModel>(
    offsets[4],
    allOffsets,
    ImgModelSchema.serialize,
    object.mediaList,
  );
  writer.writeLong(offsets[5], object.publicCount);
  writer.writeString(offsets[6], object.sound);
  writer.writeLong(offsets[7], object.status);
  writer.writeString(offsets[8], object.subCategoryId);
  writer.writeString(offsets[9], object.subCategoryTitle);
  writer.writeString(offsets[10], object.supportType);
  writer.writeString(offsets[11], object.supportedPerson);
  writer.writeBool(offsets[12], object.webEdit);
}

NotSendModel _notSendModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = NotSendModel(
    categoryId: reader.readStringOrNull(offsets[0]),
    categoryTitle: reader.readStringOrNull(offsets[1]),
    desc: reader.readStringOrNull(offsets[2]),
    individual: reader.readStringOrNull(offsets[3]),
    mediaList: reader.readObjectList<ImgModel>(
      offsets[4],
      ImgModelSchema.deserialize,
      allOffsets,
      ImgModel(),
    ),
    publicCount: reader.readLongOrNull(offsets[5]),
    sound: reader.readStringOrNull(offsets[6]),
    status: reader.readLongOrNull(offsets[7]),
    subCategoryId: reader.readStringOrNull(offsets[8]),
    subCategoryTitle: reader.readStringOrNull(offsets[9]),
    supportType: reader.readStringOrNull(offsets[10]),
    supportedPerson: reader.readStringOrNull(offsets[11]),
    webEdit: reader.readBoolOrNull(offsets[12]),
  );
  object.localId = id;
  return object;
}

P _notSendModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readObjectList<ImgModel>(
        offset,
        ImgModelSchema.deserialize,
        allOffsets,
        ImgModel(),
      )) as P;
    case 5:
      return (reader.readLongOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readLongOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readBoolOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _notSendModelGetId(NotSendModel object) {
  return object.localId;
}

List<IsarLinkBase<dynamic>> _notSendModelGetLinks(NotSendModel object) {
  return [];
}

void _notSendModelAttach(
    IsarCollection<dynamic> col, Id id, NotSendModel object) {
  object.localId = id;
}

extension NotSendModelQueryWhereSort
    on QueryBuilder<NotSendModel, NotSendModel, QWhere> {
  QueryBuilder<NotSendModel, NotSendModel, QAfterWhere> anyLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension NotSendModelQueryWhere
    on QueryBuilder<NotSendModel, NotSendModel, QWhereClause> {
  QueryBuilder<NotSendModel, NotSendModel, QAfterWhereClause> localIdEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localId,
        upper: localId,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterWhereClause> localIdNotEqualTo(
      Id localId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterWhereClause>
      localIdGreaterThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localId, includeLower: include),
      );
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterWhereClause> localIdLessThan(
      Id localId,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localId, includeUpper: include),
      );
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterWhereClause> localIdBetween(
    Id lowerLocalId,
    Id upperLocalId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalId,
        includeLower: includeLower,
        upper: upperLocalId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension NotSendModelQueryFilter
    on QueryBuilder<NotSendModel, NotSendModel, QFilterCondition> {
  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'categoryId',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'categoryId',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'categoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'categoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'categoryId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'categoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'categoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'categoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'categoryId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryId',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'categoryId',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'categoryTitle',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'categoryTitle',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'categoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'categoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'categoryTitle',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'categoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'categoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'categoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'categoryTitle',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryTitle',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      categoryTitleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'categoryTitle',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> descIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'desc',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      descIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'desc',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> descEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      descGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> descLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> descBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'desc',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      descStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> descEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> descContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'desc',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> descMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'desc',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      descIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'desc',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      descIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'desc',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'individual',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'individual',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'individual',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'individual',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'individual',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'individual',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'individual',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'individual',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'individual',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'individual',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'individual',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      individualIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'individual',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      localIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      localIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      localIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      localIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      mediaListIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'mediaList',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      mediaListIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'mediaList',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      mediaListLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'mediaList',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      mediaListIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'mediaList',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      mediaListIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'mediaList',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      mediaListLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'mediaList',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      mediaListLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'mediaList',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      mediaListLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'mediaList',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      publicCountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'publicCount',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      publicCountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'publicCount',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      publicCountEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'publicCount',
        value: value,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      publicCountGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'publicCount',
        value: value,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      publicCountLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'publicCount',
        value: value,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      publicCountBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'publicCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      soundIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sound',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      soundIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sound',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> soundEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sound',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      soundGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sound',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> soundLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sound',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> soundBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sound',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      soundStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sound',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> soundEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sound',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> soundContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sound',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> soundMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sound',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      soundIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sound',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      soundIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sound',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      statusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      statusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> statusEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      statusGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      statusLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition> statusBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'subCategoryId',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'subCategoryId',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'subCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'subCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'subCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'subCategoryId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'subCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'subCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'subCategoryId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'subCategoryId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'subCategoryId',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'subCategoryId',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'subCategoryTitle',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'subCategoryTitle',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'subCategoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'subCategoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'subCategoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'subCategoryTitle',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'subCategoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'subCategoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'subCategoryTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'subCategoryTitle',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'subCategoryTitle',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      subCategoryTitleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'subCategoryTitle',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'supportType',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'supportType',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'supportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'supportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'supportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'supportType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'supportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'supportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'supportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'supportType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'supportType',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'supportType',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'supportedPerson',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'supportedPerson',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'supportedPerson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'supportedPerson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'supportedPerson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'supportedPerson',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'supportedPerson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'supportedPerson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'supportedPerson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'supportedPerson',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'supportedPerson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      supportedPersonIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'supportedPerson',
        value: '',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      webEditIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'webEdit',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      webEditIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'webEdit',
      ));
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      webEditEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'webEdit',
        value: value,
      ));
    });
  }
}

extension NotSendModelQueryObject
    on QueryBuilder<NotSendModel, NotSendModel, QFilterCondition> {
  QueryBuilder<NotSendModel, NotSendModel, QAfterFilterCondition>
      mediaListElement(FilterQuery<ImgModel> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'mediaList');
    });
  }
}

extension NotSendModelQueryLinks
    on QueryBuilder<NotSendModel, NotSendModel, QFilterCondition> {}

extension NotSendModelQuerySortBy
    on QueryBuilder<NotSendModel, NotSendModel, QSortBy> {
  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByCategoryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryId', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortByCategoryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryId', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByCategoryTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryTitle', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortByCategoryTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryTitle', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByDescDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByIndividual() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'individual', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortByIndividualDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'individual', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByPublicCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'publicCount', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortByPublicCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'publicCount', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortBySound() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sound', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortBySoundDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sound', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortBySubCategoryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subCategoryId', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortBySubCategoryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subCategoryId', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortBySubCategoryTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subCategoryTitle', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortBySubCategoryTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subCategoryTitle', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortBySupportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportType', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortBySupportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportType', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortBySupportedPerson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportedPerson', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      sortBySupportedPersonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportedPerson', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByWebEdit() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'webEdit', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> sortByWebEditDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'webEdit', Sort.desc);
    });
  }
}

extension NotSendModelQuerySortThenBy
    on QueryBuilder<NotSendModel, NotSendModel, QSortThenBy> {
  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByCategoryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryId', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenByCategoryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryId', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByCategoryTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryTitle', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenByCategoryTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'categoryTitle', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByDescDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'desc', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByIndividual() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'individual', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenByIndividualDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'individual', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByLocalIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByPublicCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'publicCount', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenByPublicCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'publicCount', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenBySound() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sound', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenBySoundDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sound', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenBySubCategoryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subCategoryId', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenBySubCategoryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subCategoryId', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenBySubCategoryTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subCategoryTitle', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenBySubCategoryTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'subCategoryTitle', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenBySupportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportType', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenBySupportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportType', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenBySupportedPerson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportedPerson', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy>
      thenBySupportedPersonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supportedPerson', Sort.desc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByWebEdit() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'webEdit', Sort.asc);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QAfterSortBy> thenByWebEditDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'webEdit', Sort.desc);
    });
  }
}

extension NotSendModelQueryWhereDistinct
    on QueryBuilder<NotSendModel, NotSendModel, QDistinct> {
  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctByCategoryId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'categoryId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctByCategoryTitle(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'categoryTitle',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctByDesc(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'desc', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctByIndividual(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'individual', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctByPublicCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'publicCount');
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctBySound(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sound', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'status');
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctBySubCategoryId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'subCategoryId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct>
      distinctBySubCategoryTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'subCategoryTitle',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctBySupportType(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'supportType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctBySupportedPerson(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'supportedPerson',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<NotSendModel, NotSendModel, QDistinct> distinctByWebEdit() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'webEdit');
    });
  }
}

extension NotSendModelQueryProperty
    on QueryBuilder<NotSendModel, NotSendModel, QQueryProperty> {
  QueryBuilder<NotSendModel, int, QQueryOperations> localIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localId');
    });
  }

  QueryBuilder<NotSendModel, String?, QQueryOperations> categoryIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'categoryId');
    });
  }

  QueryBuilder<NotSendModel, String?, QQueryOperations>
      categoryTitleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'categoryTitle');
    });
  }

  QueryBuilder<NotSendModel, String?, QQueryOperations> descProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'desc');
    });
  }

  QueryBuilder<NotSendModel, String?, QQueryOperations> individualProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'individual');
    });
  }

  QueryBuilder<NotSendModel, List<ImgModel>?, QQueryOperations>
      mediaListProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'mediaList');
    });
  }

  QueryBuilder<NotSendModel, int?, QQueryOperations> publicCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'publicCount');
    });
  }

  QueryBuilder<NotSendModel, String?, QQueryOperations> soundProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sound');
    });
  }

  QueryBuilder<NotSendModel, int?, QQueryOperations> statusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'status');
    });
  }

  QueryBuilder<NotSendModel, String?, QQueryOperations>
      subCategoryIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'subCategoryId');
    });
  }

  QueryBuilder<NotSendModel, String?, QQueryOperations>
      subCategoryTitleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'subCategoryTitle');
    });
  }

  QueryBuilder<NotSendModel, String?, QQueryOperations> supportTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'supportType');
    });
  }

  QueryBuilder<NotSendModel, String?, QQueryOperations>
      supportedPersonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'supportedPerson');
    });
  }

  QueryBuilder<NotSendModel, bool?, QQueryOperations> webEditProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'webEdit');
    });
  }
}
