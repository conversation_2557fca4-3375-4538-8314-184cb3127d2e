import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/embedded_models.dart';

part 'not_send_model.g.dart';

@collection
@Name("NotSendModel")
class NotSendModel {
  NotSendModel({
    this.categoryId,
    this.subCategoryId,
    this.categoryTitle, // Added categoryTitle
    this.subCategoryTitle, // Added subCategoryTitle
    this.webEdit,
    this.sound,
    this.desc,
    this.supportedPerson,
    this.supportType,
    this.status,
    this.mediaList,
    this.publicCount,
    this.individual,
  });

  NotSendModel.fromJson(dynamic json) {
    categoryId = json['categoryId'];
    subCategoryId = json['subCategoryId'];
    categoryTitle = json['categoryTitle']; // Added categoryTitle
    subCategoryTitle = json['subCategoryTitle']; // Added subCategoryTitle
    webEdit = json['webEdit'];
    sound = json['sound'];
    desc = json['desc'];
    supportedPerson = json['supportedPerson'];
    supportType = json['supportType'];
    status = json['status'];
    if (json['mediaList'] != null) {
      mediaList = [];
      json['mediaList'].forEach((v) {
        mediaList?.add(ImgModel.fromJson(v));
      });
    }
    publicCount = json['publicCount'];
    individual = json['individual'];
  }

  Id localId = Isar.autoIncrement;
  String? categoryId;
  String? subCategoryId;
  String? categoryTitle; // Added categoryTitle
  String? subCategoryTitle; // Added subCategoryTitle
  bool? webEdit;
  String? sound;
  String? desc;
  String? supportedPerson;
  String? supportType;
  int? status;
  List<ImgModel>? mediaList;
  int? publicCount;
  String? individual;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['categoryId'] = categoryId;
    map['subCategoryId'] = subCategoryId;
    map['categoryTitle'] = categoryTitle; // Added categoryTitle
    map['subCategoryTitle'] = subCategoryTitle; // Added subCategoryTitle
    map['webEdit'] = webEdit;
    map['sound'] = sound;
    map['desc'] = desc;
    map['supportedPerson'] = supportedPerson;
    map['supportType'] = supportType;
    map['status'] = status;
    if (mediaList != null) {
      map['mediaList'] = mediaList?.map((v) => v.toJson()).toList();
    }
    map['publicCount'] = publicCount;
    map['individual'] = individual;
    return map;
  }
}