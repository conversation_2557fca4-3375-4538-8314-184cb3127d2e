// ignore_for_file: prefer_const_constructors

import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:social_media_recorder/audio_encoder_type.dart';
import 'package:social_media_recorder/screen/social_media_recorder.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/alert_dialog.dart';
import 'package:yetakchi/features/send_data/widgets/path_root.dart';
import 'package:yetakchi/features/send_data/widgets/player_widget.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class VoiceWidget extends StatefulWidget {
  final Function(String) onRecorded;
  final String root;

  const VoiceWidget({Key? key, required this.onRecorded, required this.root})
      : super(key: key);

  @override
  State<VoiceWidget> createState() => _VoiceWidgetState();
}

class _VoiceWidgetState extends State<VoiceWidget> {
  String localePath = "";
  Directory downloadSoundDir = di();

  @override
  void initState() {
    if (Platform.isAndroid)
      _androidVersion().then((value) {
        if (value < 29) {
          _clearCache();
        }
      });
    else {
      _clearCache();
    }

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(alignment: Alignment.centerLeft, children: [
      Container(
        height: 55.h,
        margin:
            EdgeInsets.only(left: 35.w, right: 5.w, top: 10.h, bottom: 10.h),
        decoration: BoxDecoration(
            color: Theme.of(context).cardTheme.color,
            borderRadius: BorderRadius.circular(15.r)),
        child: Stack(children: [
          Align(
            alignment: Alignment.center,
            child: Wrap(children: [
              InkWell(
                onTap: () async {
                  if (localePath != "") {
                    var result = await showAlert(context) ?? false;
                    if (result) {
                      setState(() {
                        widget.onRecorded(localePath);
                        localePath = "";
                      });
                    }
                  }
                },
                child: Container(
                    margin: EdgeInsets.only(right: 30.w),
                    decoration: BoxDecoration(
                        color: localePath == ""
                            ? cFirstColor.withOpacity(0.1)
                            : Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(5.r)),
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: 10.w, right: 10.w, top: 6.h, bottom: 6.h),
                      child: localePath == ""
                          ? Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(LocaleKeys.not_written.tr(),
                                    style: TextStyle(
                                        color: Theme.of(context).primaryColor)),
                                Padding(
                                  padding: EdgeInsets.only(left: 2.w),
                                  child: Icon(
                                    Icons.mic_off_outlined,
                                    color: Theme.of(context).primaryColor,
                                    size: 22.h,
                                  ),
                                )
                              ],
                            )
                          : Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(LocaleKeys.deleting.tr(),
                                    style: TextStyle(color: Colors.red)),
                                Padding(
                                  padding: EdgeInsets.only(left: 2.w),
                                  child: Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                    size: 22.h,
                                  ),
                                )
                              ],
                            ),
                    )),
              )
            ]),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Wrap(children: [
              Container(
                  decoration: BoxDecoration(
                      color: cFirstColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(15.r)),
                  height: 40.h,
                  width: 40.h,
                  child: Padding(
                    padding: EdgeInsets.all(5.h),
                    child: Icon(
                      Icons.mic,
                      color: Theme.of(context).primaryColor,
                      size: 22.h,
                    ),
                  ))
            ]),
          ),
          Align(
            alignment: Alignment.bottomRight,
            child: SocialMediaRecorder(
              storeSoundRecoringPath: this.downloadSoundDir.path,

              /// Attributes are compile time
              backGroundColor: Colors.black.withOpacity(0.0),
              counterBackGroundColor: Theme.of(context).cardTheme.color,
              recordIconBackGroundColor: cFirstColor,
              recordIconWhenLockBackGroundColor: cFirstColor,
              slideToCancelText: LocaleKeys.cancelling.tr(),
              cancelText: LocaleKeys.cancel.tr(),
              cancelTextStyle: Theme.of(context).textTheme.bodyMedium,
              slideToCancelTextStyle: Theme.of(context).textTheme.bodyMedium,
              counterTextStyle: Theme.of(context).textTheme.bodyMedium,
              cancelTextBackGroundColor: Theme.of(context).cardTheme.color,
              recordIconWhenLockedRecord: Icon(
                Icons.save,
                color: Colors.white,
                size: 22.h,
              ),
              recordIcon:
                  Icon(Icons.mic_rounded, color: Colors.black.withOpacity(0.0)),
              sendRequestFunction: (soundFile, name) async {
                setState(() {
                  localePath = soundFile.path;
                  widget.onRecorded(localePath);
                });
              },
              encode: AudioEncoderType.AAC,
            ),
          ),
        ]),
      ),
      Container(
          decoration: BoxDecoration(
              color: Theme.of(context).cardTheme.color,
              borderRadius: BorderRadius.circular(15.r)),
          padding: EdgeInsets.all(5.h),
          child:
              PlayerWidget(localePath: localePath, key: ValueKey(localePath))),
    ]);
  }

  Future<dynamic> _androidVersion() async {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    final androidInfo = await deviceInfoPlugin.androidInfo;
    return androidInfo.version.sdkInt;
  }

  _clearCache() async {
    final path = downloadSoundDir.path;
    final isExists = await Directory(path).exists();
    getFilePath().then((value) {
      if (isExists) {
        if (value.isNotEmpty) {
          final dir = Directory(path);
          dir.deleteSync(recursive: true);
          print("Voices are cleared!");
        }
      }
    });
  }
}

extension FileExtension on String {
  String getFileName() {
    final List<String> parts = this.split('/');
    if (parts.isNotEmpty) {
      return parts.last;
    } else {
      return '';
    }
  }
}
