import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kplayer/kplayer.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

class PlayerWidget extends StatefulWidget {
  const PlayerWidget({Key? key, required this.localePath}) : super(key: key);
  final String localePath;

  @override
  State<PlayerWidget> createState() => _PlayerWidgetState();
}

class _PlayerWidgetState extends State<PlayerWidget> {
  late PlayerController player;
  bool loading = false;

  @override
  initState() {
    player = Player.file(widget.localePath, autoPlay: false);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    player.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox(
          width: 40.h,
          height: 40.h,
          child: StreamBuilder(
            stream: player.streams.position,
            builder: (context, snapshot) {
              return CircularProgressIndicator(
                strokeWidth: 1.w,
                color: cFirstColor,
                value: loading
                    ? null
                    : player.position.inSeconds /
                        max(player.duration.inSeconds, 0.01),
              );
            },
          ),
        ),
        SizedBox(
          width: 40.h,
          height: 40.h,
          child: FloatingActionButton(
            disabledElevation: 0,
            elevation: 0,
            focusElevation: 0,
            hoverElevation: 0,
            highlightElevation: 0,
            backgroundColor: cFirstColor.withOpacity(0.1),
            onPressed: () {
              setState(() {
                player.toggle();
              });
            },
            child: DefaultTextStyle(
              style: const TextStyle(color: Colors.black87),
              child: StreamBuilder(
                stream: player.streams.position,
                builder: (context, snapshot) {
                  return player.playing
                      ?  Icon(
                          Icons.pause,
                          color: Theme.of(context).primaryColor,
                          size: 30.h,
                        )
                      :  Icon(
                          Icons.play_arrow,
                          color: Theme.of(context).primaryColor,
                          size: 30.h,
                        );
                },
              ),
            ),
          ),
        ),
      ],
    );
  }
}
