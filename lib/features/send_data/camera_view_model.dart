/// https://github.com/flutter/flutter/issues/96140
///
/// Always use camera with this way

// Future<void> initCamera() async {
//   PermissionStatus status = await Permission.camera.request();
//   if (status.isGranted) {
//     List<CameraDescription> availableCams = await availableCameras();
//     _cameras.clear();
//     _cameras.addAll(availableCams);
//     cameraController = CameraController(
//         _cameras.first, ResolutionPreset.veryHigh,
//         enableAudio: false);
//     if (!cameraController!.value.isInitialized) {
//       await cameraController!.initialize();
//       cameraController!.setFlashMode(FlashMode.off);
//     }
//   } else {
//     if (status.isPermanentlyDenied) {
//       AppSettings.openAppSettings();
//     }
//   }
// }
//
// void disposeCamera() {
//   if (cameraController != null && cameraController!.value.isInitialized) {
//     cameraController!.dispose();
//     cameraController = null;
//   }
// }
//
// void resumeCamera() {
//   if (cameraController != null) {
//     if (cameraController!.value.isPreviewPaused) {
//       cameraController!.resumePreview();
//     } else {
//       initCamera();
//     }
//   }
// }
//
// void pauseCamera() {
//   if (cameraController != null && cameraController!.value.isInitialized) {
//     cameraController!.pausePreview();
//   }
// }
