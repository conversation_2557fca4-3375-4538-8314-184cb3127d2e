class PaymentRes {
  PaymentRes({
      this.status, 
      this.titleQQ, 
      this.titleUZ, 
      this.titleRU, 
      this.date,});

  PaymentRes.fromJson(dynamic json) {
    status = json['status'];
    titleQQ = json['titleQQ'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    date = json['date'];
  }
  bool? status;
  String? titleQQ;
  String? titleUZ;
  String? titleRU;
  int? date;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['status'] = status;
    map['titleQQ'] = titleQQ;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['date'] = date;
    return map;
  }

}