import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart' hide Trans;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:isar/isar.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/auth/data/model/user_model.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

///TODO: Unable to retrieve flag snapshot for com.google.android.libraries.

class MapPage extends StatefulWidget {
  const MapPage({super.key});

  @override
  State<MapPage> createState() => _MapPageState();
}

class _MapPageState extends State<MapPage> {
  final defaultLat = 40.39015226173924;
  final defaultLng = 71.77977461367846;
  var ZOOM_LEVEL = 16.0;
  var ZOOM_LEVEL_CLOSER = 18.0;

  final IsarService isarService = di();
  Completer<GoogleMapController> _controller = Completer();
  Set<Polygon> polygonSet = new Set();
  Set<Circle>? circles;
  double? workLat;
  double? workLng;
  Position? _currentPosition;
  late Future<dynamic> _initFuture;
  List<Marker> markers = [];

  // late StreamSubscription<Position> _positionStreamSubscription;
  MapType mapType = MapType.normal;
  var mapStyle;

  Set<Polygon> myPolygon() {
    List<LatLng> polygonCoords = [];
    List<List<double?>> polyList = [];
    UserModel? user = isarService.isar.userModels.where().findFirstSync();
    user?.district?.coordinates?.forEach((element) {
      List<double?> location = [];
      location.add(element.latitude);
      location.add(element.longitude);
      polyList.add(location);
    });

    workLat = user?.district?.coordinate?.latitude ?? 0;
    workLng = user?.district?.coordinate?.longitude ?? 0;

    if (workLng != 0 && workLng != 0) {
      circles = Set.from([
        Circle(
            circleId: CircleId("circleId"),
            center: LatLng(user?.district?.coordinate?.latitude ?? 0,
                user?.district?.coordinate?.longitude ?? 0),
            radius: ALLOWED_DISTANCE.toDouble(),
            strokeColor: Colors.green,
            strokeWidth: 3.w.toInt())
      ]);

      markers.add(Marker(
          markerId: MarkerId('markerId'),
          position: LatLng(workLat ?? defaultLng, workLng ?? defaultLng),
          icon: BitmapDescriptor.defaultMarker,
          infoWindow: InfoWindow(
            title: LocaleKeys.work_place.tr(),
          )));
    }
    if (polyList.isNotEmpty) {
      polyList.forEach((element) {
        polygonCoords.add(LatLng(element.first ?? 0.0, element.last ?? 0.0));
      });
      polygonSet.add(Polygon(
          polygonId: PolygonId('polygonId'),
          points: polygonCoords,
          strokeColor: Colors.red,
          strokeWidth: 4.w.toInt(),
          fillColor: Colors.transparent));
    }

    return polygonSet;
  }

  @override
  void initState() {
    getCurrentLocation();
    reInitializerButton();
    rootBundle.loadString(Assets.mapStyleMapStyle).then((string) {
      mapStyle = string;
    });
    super.initState();
  }

  // getStreamLocation() {
  //   _positionStreamSubscription =
  //       Geolocator.getPositionStream().listen((event) async {
  //     _currentPosition = event;
  //     print(_currentPosition);
  //
  //   });
  // }

  @override
  void dispose() {
    // _positionStreamSubscription.cancel();
    _controller = Completer();
    super.dispose();
  }

  getCurrentLocation() {
    Geolocator.getCurrentPosition().then((value) async {
      print("${value.latitude},${value.longitude}");
      CameraPosition cameraPosition = new CameraPosition(
        target: LatLng(value.latitude, value.longitude),
        zoom: ZOOM_LEVEL_CLOSER,
      );
      GoogleMapController controller = await _controller.future;
      controller.animateCamera(CameraUpdate.newCameraPosition(cameraPosition));
    });
  }

  Future<void> askPermission() async {
    bool location = await Permission.location.shouldShowRequestRationale;
    PermissionStatus status = await Permission.location.request();
    if (status != PermissionStatus.granted) {
      if (status.isPermanentlyDenied && !location) {
        showCustomDialog(context);
      }
    } else {
      setState(() {
        _initFuture = Future.value();
      });
      //getStreamLocation();
    }
  }

  reInitializerButton() {
    setState(() {
      _initFuture = askPermission();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: false,
      appBar: AppBar(
        backgroundColor: Theme.of(context).primaryColorDark,
        title: Text(LocaleKeys.work_map.tr(),
            style: TextStyle(fontSize: context.isTablet ? 12.sp : 16.sp,color: cWhiteColor),),
        toolbarHeight: 70.h,
        centerTitle: true,
        leadingWidth: 40.w,
        iconTheme: IconThemeData(size: 22.h, color: cWhiteColor),
      ),
      body: FutureBuilder(
          future: _initFuture,
          builder: (context, snapshot) {
            switch (snapshot.connectionState) {
              case ConnectionState.waiting:
                {
                  // Otherwise, display a loading indicator.
                  return Center(
                      child: CupertinoActivityIndicator(
                    color: Theme.of(context).primaryColor,
                    radius: 30.r,
                  ));
                }
              default:
                if (snapshot.hasError) {
                  print('Error: ${snapshot.connectionState}');
                  return Container(
                    child: Center(child: Text('Error!')),
                  );
                } else {
                  print('DEFAULT: ${snapshot.connectionState}');
                  return GoogleMap(
                    zoomControlsEnabled: false,
                    myLocationButtonEnabled: false,
                    mapToolbarEnabled: true,
                    myLocationEnabled: true,
                    polygons: myPolygon(),
                    mapType: mapType,
                    initialCameraPosition: CameraPosition(
                        target: LatLng(_currentPosition?.latitude ?? defaultLat,
                            _currentPosition?.longitude ?? defaultLng),
                        zoom: ZOOM_LEVEL),
                    markers: Set<Marker>.from(markers),
                    circles:
                        circles ?? {Circle(circleId: CircleId("circleId"))},
                    onMapCreated: (GoogleMapController controller) {
                      _controller.complete(controller);
                      controller.setMapStyle(isDark() ? mapStyle : null);
                      controller.showMarkerInfoWindow(MarkerId("markerId"));
                    },
                  );
                }
            }
          }),
      floatingActionButton: Padding(
        padding: EdgeInsets.all(15.h),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            PopupMenuButton<String>(
              color: Theme.of(context).cardTheme.color,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.r)),
              itemBuilder: (_) {
                return [
                  PopupMenuItem<String>(
                    child: Text(
                      "Hybrid",
                      style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16.sp),
                    ),
                    onTap: () {
                      setState(() {
                        mapType = MapType.hybrid;
                      });
                    },
                  ),
                  PopupMenuItem<String>(
                    child: Text(
                      "Normal",
                      style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16.sp),
                    ),
                    onTap: () {
                      setState(() {
                        mapType = MapType.normal;
                      });
                    },
                  ),
                ];
              },
              child: SizedBox(
                height: 55.h,
                width: 55.h,
                child: FloatingActionButton(
                    onPressed: null,
                    child: Icon(Icons.map, color: cWhiteColor, size: 22.h),
                    backgroundColor: isDark() ? cCardDarkColor : cFirstColor),
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            SizedBox(
              height: 55.h,
              width: 55.h,
              child: FloatingActionButton(
                heroTag: "workPlace",
                onPressed: () async {
                  if (workLat != 0 && workLng != 0) {
                    CameraPosition cameraPosition = new CameraPosition(
                      target:
                          LatLng(workLat ?? defaultLat, workLng ?? defaultLng),
                      zoom: ZOOM_LEVEL,
                    );
                    GoogleMapController controller = await _controller.future;
                    controller.animateCamera(
                        CameraUpdate.newCameraPosition(cameraPosition));
                  } else {
                    CustomToast.showToast(
                        LocaleKeys.not_work_place_in_base.tr());
                  }
                },
                child:
                    Icon(Icons.maps_home_work, color: cWhiteColor, size: 22.h),
                backgroundColor: isDark() ? cCardDarkColor : cFirstColor,
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            SizedBox(
              height: 55.h,
              width: 55.h,
              child: FloatingActionButton(
                heroTag: "currentLocation",
                onPressed: () {
                  getCurrentLocation();
                },
                child: Icon(Icons.my_location, color: cWhiteColor, size: 22.h),
                backgroundColor: isDark() ? cCardDarkColor : cFirstColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
