import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/electron_library/book_list/data/datasource/book_local_datasource.dart';
import 'package:yetakchi/features/electron_library/book_list/data/datasource/book_remote_datasource.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/chapter_model.dart';
import 'package:yetakchi/features/electron_library/book_test/model/book_test_result.dart';
import 'package:yetakchi/features/electron_library/book_test/model/book_test_send.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'book_test_result_state.dart';

class BookTestResultCubit extends Cubit<BookTestResultState> {
  final Dio dio;
  final NetworkInfo networkInfo;
  final BookRemoteDataSourceImp bookRemoteDataSourceImp = di();
  final BookLocalDatasourceImpl bookLocalDatasourceImpl = di();

  BookTestResultCubit({required this.dio, required this.networkInfo})
      : super(BookTestResultState.initial());

  Future<void> checkResult(
      {required BookTestSend testSend,
      required int bookPercentage,
      required String bookId}) async {
    emit(BookTestResultState(status: BookTestResultStatus.loading));
    if (await networkInfo.isConnected) {
      try {
        var response =
            await dio.post(bookSendAnswerPath, data: testSend.toJson());

        if (response.statusCode == 201 || response.statusCode == 200) {
          BookTestResult bookTestResult =
              BookTestResult.fromJson(response.data);
          bool isPassed = (bookTestResult.trueAnswersPercent?.toDouble() ?? 0) >
                  bookPercentage
              ? true
              : false;
          if (isPassed) {
            try {
              List<ChapterModel> chapters = await bookRemoteDataSourceImp.getChapters(bookId);
              await  bookLocalDatasourceImpl.updateChapters(chapters, bookId);
              emit(BookTestResultState(
                  status: BookTestResultStatus.success,
                  testResult: bookTestResult));
            } catch (e) {
              print(e.toString());
              emit(BookTestResultState(
                  status: BookTestResultStatus.failure,
                  message: "Server Error"));
            }
          } else {
            emit(BookTestResultState(
                status: BookTestResultStatus.success,
                testResult: bookTestResult));
          }
        } else {
          emit(BookTestResultState(
              status: BookTestResultStatus.failure, message: "Server Error"));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            try {
              emit(BookTestResultState(
                  status: BookTestResultStatus.failure,
                  message:
                      "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}"));
            } catch (e) {
              emit(BookTestResultState(
                  status: BookTestResultStatus.failure,
                  message: "Parsing error of RAW data"));
            }
          }
        }
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(BookTestResultState(
              status: BookTestResultStatus.failure,
              message: "Connection timeout"));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          emit(BookTestResultState(
              status: BookTestResultStatus.failure,
              message: "Receive timeout"));
        }

        if (e.type == DioExceptionType.unknown) {
          emit(BookTestResultState(
              status: BookTestResultStatus.failure,
              message: "Something went wrong"));
        }

        if (e.response?.statusCode == 400) {
          try {
            emit(BookTestResultState(
                status: BookTestResultStatus.failure,
                message: "${e.response?.data['message']}"));
          } catch (e) {
            emit(BookTestResultState(
                status: BookTestResultStatus.failure,
                message: "Something went wrong"));
          }
        }
      }
    } else {
      emit(BookTestResultState(
          status: BookTestResultStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
