part of 'book_test_result_cubit.dart';

enum BookTestResultStatus { initial, loading, success, failure }

class BookTestResultState {
  final BookTestResultStatus status;
  final BookTestResult? testResult;
  final String? message;

  BookTestResultState({required this.status, this.testResult, this.message});

  static BookTestResultState initial() =>
      BookTestResultState(status: BookTestResultStatus.initial);

  BookTestResultState copyWith(
      BookTestResultStatus? status, BookTestResult? testResult, String? message) {
    return BookTestResultState(
        status: status ?? this.status,
        testResult: testResult ?? this.testResult,
        message: message ?? this.message);
  }
}
