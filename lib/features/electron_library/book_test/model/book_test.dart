class BookTest {
  BookTest({
      this.id, 
      this.title, 
      this.book, 
      this.chapterNumber, 
      this.status, 
      this.questions,});

  BookTest.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
    book = json['book'];
    chapterNumber = json['chapterNumber'];
    status = json['status'];
    if (json['questions'] != null) {
      questions = [];
      json['questions'].forEach((v) {
        questions?.add(BookQuestions.fromJson(v));
      });
    }
  }
  String? id;
  String? title;
  String? book;
  int? chapterNumber;
  int? status;
  List<BookQuestions>? questions;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['title'] = title;
    map['book'] = book;
    map['chapterNumber'] = chapterNumber;
    map['status'] = status;
    if (questions != null) {
      map['questions'] = questions?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class BookQuestions {
  BookQuestions({
      this.title, 
      this.answerOptions, 
      this.id,});

  BookQuestions.fromJson(dynamic json) {
    title = json['title'];
    if (json['answerOptions'] != null) {
      answerOptions = [];
      json['answerOptions'].forEach((v) {
        answerOptions?.add(AnswerOptions.fromJson(v));
      });
    }
    id = json['_id'];
  }
  String? title;
  List<AnswerOptions>? answerOptions;
  String? id;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['title'] = title;
    if (answerOptions != null) {
      map['answerOptions'] = answerOptions?.map((v) => v.toJson()).toList();
    }
    map['_id'] = id;
    return map;
  }

}

class AnswerOptions {
  AnswerOptions({
      this.title, 
      this.isCorrect, 
      this.id,});

  AnswerOptions.fromJson(dynamic json) {
    title = json['title'];
    isCorrect = json['isCorrect'];
    id = json['_id'];
  }
  String? title;
  bool? isCorrect;
  String? id;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['title'] = title;
    map['isCorrect'] = isCorrect;
    map['_id'] = id;
    return map;
  }

}