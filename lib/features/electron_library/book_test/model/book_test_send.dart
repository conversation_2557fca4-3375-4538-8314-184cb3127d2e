class BookTestSend {
  BookTestSend({
      this.bookQuiz, 
      this.questions,});

  BookTestSend.fromJson(dynamic json) {
    bookQuiz = json['bookQuiz'];
    if (json['questions'] != null) {
      questions = [];
      json['questions'].forEach((v) {
        questions?.add(BookTestSendQuestions.fromJson(v));
      });
    }
  }
  String? bookQuiz;
  List<BookTestSendQuestions>? questions;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['bookQuiz'] = bookQuiz;
    if (questions != null) {
      map['questions'] = questions?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class BookTestSendQuestions {
  BookTestSendQuestions({
      this.question,
      this.userAnswer,});

  BookTestSendQuestions.fromJson(dynamic json) {
    question = json['question'];
    userAnswer = json['userAnswer'];
  }
  String? question;
  String? userAnswer;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['question'] = question;
    map['userAnswer'] = userAnswer;
    return map;
  }

}