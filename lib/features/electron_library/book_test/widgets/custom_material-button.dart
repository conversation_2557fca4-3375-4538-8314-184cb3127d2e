import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

class CustomMaterialButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String title;
  final Color color;

  const CustomMaterialButton(
      {super.key, required this.onPressed, required this.title, required this.color});

  @override
  Widget build(BuildContext context) {
    return MaterialButton(
      onPressed: onPressed,
      minWidth: 220.w,
      color: color,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(cRadius36)),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.h),
        alignment: Alignment.center,
        width: 180.w,
        child: Text(
          title,
          maxLines: 4,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
          style: TextStyle(
              color: cWhiteColor, fontSize: 16.sp, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
