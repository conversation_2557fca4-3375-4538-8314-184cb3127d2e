part of 'book_test_cubit.dart';

enum BookTestStatus { initial, loading, success, failure,emptyList }

final class BookTestState {
  final BookTestStatus? status;
  final BookTest? testModel;
  final String? message;

  BookTestState({required this.status, this.testModel, this.message});

  static BookTestState initial() =>
      BookTestState(status: BookTestStatus.initial);

  BookTestState copyWith(
      BookTestStatus? status, BookTest? testCardNewModel, String? message) {
    return BookTestState(
        status: status ?? this.status,
        testModel: testCardNewModel ?? this.testModel,
        message: message ?? this.message);
  }
}
