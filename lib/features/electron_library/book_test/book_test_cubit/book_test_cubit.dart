import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/features/electron_library/book_test/model/book_test.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'book_test_state.dart';

class BookTestCubit extends Cubit<BookTestState> {
  final Dio dio;
  final NetworkInfo networkInfo;

  BookTestCubit({required this.dio, required this.networkInfo})
      : super(BookTestState.initial());

  loadTest(String id) async {
    if (await networkInfo.isConnected) {
      emit(BookTestState(status: BookTestStatus.loading));
      try {
        var response = await dio.get("${booksQuizPath}$id");
        if (response.statusCode == 200) {
          BookTest testModel = BookTest.fromJson(response.data);
          if (testModel.questions?.isEmpty == true) {
            emit(BookTestState(
                status: BookTestStatus.emptyList, testModel: testModel));
          } else {
            emit(BookTestState(
                status: BookTestStatus.success, testModel: testModel));
          }
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            try {
              emit(BookTestState(
                  status: BookTestStatus.failure,
                  message:
                      "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}"));
            } catch (e) {
              emit(BookTestState(
                  status: BookTestStatus.failure,
                  message: "Parsing error of RAW data"));
            }
          }
        }
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(BookTestState(
              status: BookTestStatus.failure, message: "Connection timeout"));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          emit(BookTestState(
              status: BookTestStatus.failure, message: "Receive timeout"));
        }

        if (e.type == DioExceptionType.unknown) {
          emit(BookTestState(
              status: BookTestStatus.failure, message: "Something went wrong"));
        }

        if (e.response?.statusCode == 400) {
          try {
            emit(BookTestState(
                status: BookTestStatus.failure,
                message: "${e.response?.data['message']}"));
          } catch (e) {
            emit(BookTestState(
                status: BookTestStatus.failure,
                message: "Something went wrong"));
          }
        }
      } catch (e) {
        emit(BookTestState(
            status: BookTestStatus.failure,
            message:
                "${LocaleKeys.error_download_data.tr()}.. ${LocaleKeys.server_form_error.tr()}"));
      }
    } else {
      emit(BookTestState(
          status: BookTestStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
