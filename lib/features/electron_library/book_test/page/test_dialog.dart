import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/dialog_frame.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_test/page/book_test_page.dart';
import 'package:yetakchi/features/electron_library/book_test/widgets/custom_material-button.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class TestDialog extends StatefulWidget {
  final String chapterId;
  final String chapterTitle;
  final int bookPercentage;
  final bool isLastChapter;
  final BookModel backendBookModel;

  const TestDialog(
      {super.key,
      required this.chapterId,
      required this.chapterTitle,
      required this.bookPercentage,
      required this.isLastChapter,
      required this.backendBookModel});

  @override
  State<TestDialog> createState() => _TestDialogState();
}

class _TestDialogState extends State<TestDialog> {
  @override
  Widget build(BuildContext context) {
    return AllDialogSkeleton(
        child: Container(
          width: MediaQuery.of(context).size.width,
          padding: EdgeInsets.all(10.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 20.h,
              ),
              SvgPicture.asset(
                Assets.iconsAward,
                width: 100.w,
                height: 100.w,
              ),
              SizedBox(
                height: 20.h,
              ),
              CustomMaterialButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) => BookTestPage.screen(
                                chapterId: widget.chapterId,
                                chapterTitle: widget.chapterTitle,
                                bookPercentage: widget.bookPercentage,
                                isLastChapter: widget.isLastChapter,
                                backendBookModel: widget.backendBookModel,
                              )));
                },
                color: cFirstColor,
                title: LocaleKeys.start_test.tr(),
              ),
            ],
          ),
        ),
        title: LocaleKeys.need_testing_current.tr(),
        icon: Assets.iconsInfoCircle);
  }
}
