import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_test/page/book_test_page.dart';
import 'package:yetakchi/features/electron_library/book_test/widgets/custom_material-button.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class BookTestIntro extends StatefulWidget {
  final String chapterId;
  final String chapterTitle;
  final VoidCallback onBackChapterStart;
  final VoidCallback onBackChapters;
  final int bookPercentage;
  final bool isLastChapter;
  final BookModel backendBookModel;

  const BookTestIntro({
    super.key,
    required this.chapterId,
    required this.chapterTitle,
    required this.onBackChapterStart,
    required this.onBackChapters,
    required this.bookPercentage,
    required this.isLastChapter,
    required this.backendBookModel,
  });

  @override
  State<BookTestIntro> createState() => _BookTestIntroState();
}

class _BookTestIntroState extends State<BookTestIntro> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      body: SafeArea(
        child: Container(
          decoration: BoxDecoration(
              image: DecorationImage(
            opacity: 0.1,
            image: AssetImage(Assets.imagesLockPattern),
            fit: BoxFit.cover,
          )),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: EdgeInsets.only(
                        right: context.isTablet ? 30.w : 8.w, top: 40.h),
                    child: IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        showDialog(
                            context: context,
                            builder: (_) {
                              return BackdropFilter(
                                  filter: ImageFilter.blur(
                                      sigmaX: 5.0, sigmaY: 5.0),
                                  child: Dialog(
                                    backgroundColor:
                                        Theme.of(context).cardTheme.color,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(15.r),
                                    ),
                                    child: Padding(
                                      padding: EdgeInsets.all(20.h),
                                      child: Text("${LocaleKeys.test_support.tr()}:\n\n$SUPPORT_TEL1\n$SUPPORT_TEL2\n$TELEGRAM"),
                                    ),
                                  ));
                            });
                      },
                      icon: Icon(
                        Icons.info,
                        size: context.isTablet ? 40.h : 35.h,
                        color:
                            Theme.of(context).primaryColor.withOpacity(0.7) ??
                                cBlackColor,
                      ),
                    ),
                  )),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Spacer(
                    flex: 2,
                  ),
                  Card(
                    margin: EdgeInsets.symmetric(horizontal: 20.w),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(cRadius8.r)),
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      padding: EdgeInsets.all(10.w),
                      child: Column(
                        children: [
                          Text(
                            textAlign: TextAlign.center,
                            LocaleKeys.need_testing_current.tr(),
                            style: TextStyle(fontSize: 18.sp),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Spacer(),
                  SvgPicture.asset(
                    Assets.iconsBustedLock,
                    height: 200.h,
                  ),
                  Spacer(),
                  CustomMaterialButton(
                    onPressed: () {
                      Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                              builder: (context) => BookTestPage.screen(
                                    chapterId: widget.chapterId,
                                    chapterTitle: widget.chapterTitle,
                                    bookPercentage: widget.bookPercentage,
                                    isLastChapter: widget.isLastChapter,
                                    backendBookModel: widget.backendBookModel,
                                  )));
                    },
                    color: cGreenColor,
                    title: LocaleKeys.start_test.tr(),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  CustomMaterialButton(
                    onPressed: widget.onBackChapterStart,
                    color: cFirstColor,
                    title: LocaleKeys.coming_back_branch.tr(),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  CustomMaterialButton(
                    onPressed: widget.onBackChapters,
                    color: cFirstColor,
                    title: LocaleKeys.all_chapters.tr(),
                  ),
                  Spacer(
                    flex: 2,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
