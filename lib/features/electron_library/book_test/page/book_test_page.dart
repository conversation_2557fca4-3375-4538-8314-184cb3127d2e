import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/alert_dialog.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/core/widgets/header_widget_sub.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/chapter_model.dart';
import 'package:yetakchi/features/electron_library/book_reader/epub_awesome.dart';
import 'package:yetakchi/features/electron_library/book_reader/show_epub.dart';
import 'package:yetakchi/features/electron_library/book_test/book_test_cubit/book_test_cubit.dart';
import 'package:yetakchi/features/electron_library/book_test/model/book_test.dart';
import 'package:yetakchi/features/electron_library/book_test/model/book_test_send.dart';
import 'package:yetakchi/features/electron_library/book_test/page/book_test_result_page.dart';
import 'package:yetakchi/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class BookTestPage extends StatefulWidget {
  final String chapterId;
  final String chapterTitle;
  final int bookPercentage;
  final bool isLastChapter;
  final BookModel backendBookModel;

  const BookTestPage(
      {super.key,
      required this.chapterId,
      required this.chapterTitle,
      required this.bookPercentage,
      required this.isLastChapter,
      required this.backendBookModel});

  static Widget screen(
      {required String chapterId,
      required String chapterTitle,
      required int bookPercentage,
      required bool isLastChapter,
      required BookModel backendBookModel}) {
    return BlocProvider(
      create: (context) => di<BookTestCubit>(),
      child: BookTestPage(
        chapterId: chapterId,
        chapterTitle: chapterTitle,
        bookPercentage: bookPercentage,
        isLastChapter: isLastChapter,
        backendBookModel: backendBookModel,
      ),
    );
  }

  @override
  State<BookTestPage> createState() => _BookTestPageState();
}

class _BookTestPageState extends State<BookTestPage> {
  bool isVisible = true;
  int selectedIndex = -1;
  int questionNumber = 0;
  List<BookQuestions>? questions = [];
  List<BookTestSendQuestions>? sendQuestions = [];
  List<AnswerOptions>? answerOptions = [];
  List<ChapterModel>? chaptersList;
  final IsarService isarService = di();
  var currentChapterIndex;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<BookTestCubit>(context).loadTest(widget.chapterId);
    currentChapterIndex = bookProgress
            .getBookProgress(widget.backendBookModel.sId ?? '')
            .currentChapterIndex ??
        0;

  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        var result =
            await showAlertText(context, LocaleKeys.sure_exit_test.tr()) ??
                false;
        if (result) {
          EpubAwesome.openBook(widget.backendBookModel, context,
              starterChapter: currentChapterIndex);
        }
        return false;
      },
      child: Scaffold(
          appBar: AppHeaderWidgetSub(
            title: LocaleKeys.tests.tr(),
            onBackTap: () async {
              var result = await showAlertText(
                      context, LocaleKeys.sure_exit_test.tr()) ??
                  false;
              if (result) {
                EpubAwesome.openBook(widget.backendBookModel, context,
                    starterChapter: currentChapterIndex);
              }
            },
            actionImage: Assets.iconsPlaceholder,
            onActionTap: () {},
          ),
          body: SafeArea(
            child: BlocConsumer<BookTestCubit, BookTestState>(
              listener: (context, state) {
                if (state.status == BookTestStatus.success) {
                  questions = state.testModel?.questions;
                  answerOptions = questions?[questionNumber].answerOptions;
                  answerOptions?.shuffle();
                }
              },
              builder: (context, state) {
                if (state.status == BookTestStatus.loading ||
                    state.status == BookTestStatus.initial) {
                  return Center(
                    child: CupertinoActivityIndicator(
                      color: Theme.of(context).primaryColor,
                      radius: 30.r,
                    ),
                  );
                } else if (state.status == BookTestStatus.success) {
                  return Container(
                    width: MediaQuery.of(context).size.width,
                    margin: EdgeInsets.symmetric(
                      horizontal: 20.w,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: SingleChildScrollView(
                            physics: BouncingScrollPhysics(),
                            child: Column(
                              children: [
                                Text(
                                  state.testModel?.title ?? "Empty",
                                  style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600),
                                ),
                                SizedBox(
                                  height: 10.h,
                                ),
                                Text(
                                  "${questionNumber + 1}/${state.testModel?.questions?.length}",
                                  style: TextStyle(
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 20.sp),
                                ),
                                SizedBox(
                                  height: 10.h,
                                ),
                                Container(
                                  width: MediaQuery.of(context).size.width,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 20.w, vertical: 10.h),
                                  decoration: BoxDecoration(
                                      borderRadius:
                                          BorderRadius.circular(cRadius22.r),
                                      color: cFirstColor.withAlpha(20),
                                      border: Border.all(
                                          width: 1.w,
                                          color: isDark()
                                              ? cWhiteColor
                                              : cFirstColor.withAlpha(90))),
                                  child: Text(
                                    textAlign: TextAlign.center,
                                    questions?[questionNumber].title ?? "Empty",
                                    style: TextStyle(fontSize: 20.sp),
                                  ),
                                ),
                                SizedBox(
                                  height: 10.h,
                                ),
                                ListView.builder(
                                    physics: NeverScrollableScrollPhysics(
                                        parent: NeverScrollableScrollPhysics()),
                                    shrinkWrap: true,
                                    itemCount: answerOptions?.length,
                                    itemBuilder: (context, index) {
                                      return ZoomTapAnimation(
                                        onTap: () {
                                          setState(() {
                                            selectedIndex = index;
                                          });
                                        },
                                        child: Container(
                                          margin: EdgeInsets.symmetric(
                                              vertical: 4.h),
                                          width:
                                              MediaQuery.of(context).size.width,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 20.w, vertical: 20.h),
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      cRadius22.r),
                                              border: Border.all(
                                                  width: 1,
                                                  color: selectedIndex == index
                                                      ? cGreenColor
                                                      : isDark()
                                                          ? cCardDarkColor
                                                          : cFirstColor
                                                              .withAlpha(90)),
                                              color: selectedIndex == index
                                                  ? cGreenColor
                                                  : isDark()
                                                      ? cCardDarkColor
                                                      : cWhiteColor),
                                          child: Text(
                                            answerOptions?[index].title ?? "",
                                            style: TextStyle(
                                                color: selectedIndex == index
                                                    ? cWhiteColor
                                                    : isDark()
                                                        ? cPrimaryTextDark
                                                        : cFirstTextColor,
                                                fontSize: 18.sp),
                                          ),
                                        ),
                                      );
                                    }),
                                SizedBox(
                                  height: 10.h,
                                ),
                              ],
                            ),
                          ),
                        ),
                        Column(
                          children: [
                            MaterialButton(
                                minWidth: MediaQuery.of(context).size.width,
                                height: 55.h,
                                color: cFirstColor,
                                shape: RoundedRectangleBorder(
                                    borderRadius:
                                        BorderRadius.circular(cRadius36)),
                                child: Text(
                                  questionNumber + 1 == (questions?.length)
                                      ? LocaleKeys.finishing.tr()
                                      : LocaleKeys.next_question.tr(),
                                  style: TextStyle(
                                      color: cWhiteColor, fontSize: 16.sp),
                                ),
                                onPressed: () {
                                  if (selectedIndex != -1) {
                                    if (questionNumber + 1 ==
                                        questions?.length) {
                                      sendQuestions?.add(BookTestSendQuestions(
                                          question:
                                              questions?[questionNumber].id,
                                          userAnswer:
                                              answerOptions?[selectedIndex]
                                                  .id));
                                      selectedIndex = -1;

                                      BookTestSend bookTestSend = BookTestSend(
                                          bookQuiz: widget.chapterId,
                                          questions: sendQuestions);
                                      print(bookTestSend.questions);
                                      Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  BookTestResultPage.screen(
                                                    bookTestSend: bookTestSend,
                                                    bookPercentage:
                                                        widget.bookPercentage,
                                                    isLastChapter:
                                                        widget.isLastChapter,
                                                    backendBookModel:
                                                        widget.backendBookModel,
                                                  )));
                                    } else {
                                      sendQuestions?.add(BookTestSendQuestions(
                                          question:
                                              questions?[questionNumber].id,
                                          userAnswer:
                                              answerOptions?[selectedIndex]
                                                  .id));
                                      selectedIndex = -1;
                                      setState(() {
                                        questionNumber += 1;
                                        answerOptions = questions?[questionNumber].answerOptions;
                                        answerOptions?.shuffle();
                                      });
                                    }
                                  } else {
                                    CustomToast.showToast(
                                        LocaleKeys.select_answer.tr());
                                  }
                                }),
                            SizedBox(
                              height: 24.h,
                            ),
                          ],
                        )
                      ],
                    ),
                  );
                } else if (state.status == BookTestStatus.failure) {
                  return Container(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ClipRect(
                            child: Container(
                              height: 300.h,
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  Expanded(
                                      child: SvgPicture.asset(
                                    Assets.iconsWarning,
                                    height: 140.h,
                                  )),
                                  Padding(
                                      padding: EdgeInsets.only(
                                          top: 10.h,
                                          left: 30.w,
                                          right: 30.w,
                                          bottom: 10.h),
                                      child: Text(
                                        state.message.toString(),
                                        textAlign: TextAlign.center,
                                        style: TextStyle(color: cGrayColor1),
                                      )),
                                  CupertinoButton(
                                      child: Text(
                                        LocaleKeys.refresh.tr(),
                                        style: TextStyle(
                                            color: cGrayColor1,
                                            fontSize: 20.sp),
                                      ),
                                      color: cGrayColor1.withAlpha(80),
                                      onPressed: () {
                                        BlocProvider.of<BookTestCubit>(context)
                                            .loadTest(widget.chapterId);
                                      }),
                                  SizedBox(
                                    height: 20.h,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                } else if (state.status == BookTestStatus.emptyList) {
                  return EmptyListWidget(
                    onTap: () {
                      setState(() {
                        isVisible = false;
                        BlocProvider.of<BookTestCubit>(context)
                            .loadTest(widget.chapterId);
                      });
                    },
                    title: LocaleKeys.empty_test.tr(),
                  );
                } else {
                  return SizedBox();
                }
              },
            ),
          )),
    );
  }
}
