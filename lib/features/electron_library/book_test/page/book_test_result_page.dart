import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/header_widget.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/chapter_model.dart';
import 'package:yetakchi/features/electron_library/book_reader/epub_awesome.dart';
import 'package:yetakchi/features/electron_library/book_reader/show_epub.dart';
import 'package:yetakchi/features/electron_library/book_test/book_test_result_cubit/book_test_result_cubit.dart';
import 'package:yetakchi/features/electron_library/book_test/model/book_test_send.dart';
import 'package:yetakchi/features/quiz/presentation/widgets/test_circular_progress.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class BookTestResultPage extends StatefulWidget {
  final BookTestSend bookTestSend;
  final int bookPercentage;
  final bool isLastChapter;
  final BookModel backendBookModel;

  const BookTestResultPage(
      {super.key,
      required this.bookTestSend,
      required this.bookPercentage,
      required this.isLastChapter,
      required this.backendBookModel});

  static screen(
      {required BookTestSend bookTestSend,
      required int bookPercentage,
      required bool isLastChapter,
      required BookModel backendBookModel}) {
    return BlocProvider(
      create: (context) => di<BookTestResultCubit>(),
      child: BookTestResultPage(
        bookTestSend: bookTestSend,
        bookPercentage: bookPercentage,
        isLastChapter: isLastChapter,
        backendBookModel: backendBookModel,
      ),
    );
  }

  @override
  State<BookTestResultPage> createState() => _BookTestResultPageState();
}

class _BookTestResultPageState extends State<BookTestResultPage> {
  List<ChapterModel>? chaptersList;
  final IsarService isarService = di();
  var currentChapterIndex;

  // getChapterFromLocal() {
  //   chaptersList = isarService.isar.chapterModels
  //       .where()
  //       .filter()
  //       .bookIdEqualTo(widget.backendBookModel.sId)
  //       .findAllSync();
  // }

  @override
  void initState() {
    super.initState();
    BlocProvider.of<BookTestResultCubit>(context).checkResult(
        testSend: widget.bookTestSend,
        bookPercentage: widget.bookPercentage,
        bookId: widget.backendBookModel.sId ?? "");
    currentChapterIndex = bookProgress
            .getBookProgress(widget.backendBookModel.sId ?? '')
            .currentChapterIndex ??
        0;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        await EpubAwesome.openBook(widget.backendBookModel, context,
            starterChapter: currentChapterIndex, willPopScope: true);
        return true;
      },
      child: Scaffold(
        appBar: AppHeaderWidget(
          title: LocaleKeys.book_test_result.tr(),
          onBackTap: () {
            EpubAwesome.openBook(widget.backendBookModel, context,
                starterChapter: currentChapterIndex);
          },
          isBackVisible: true,
        ),
        body: BlocBuilder<BookTestResultCubit, BookTestResultState>(
          builder: (context, state) {
            if (state.status == BookTestResultStatus.initial ||
                state.status == BookTestResultStatus.loading) {
              return Center(
                child: CupertinoActivityIndicator(
                  color: Theme.of(context).primaryColor,
                  radius: 30.r,
                ),
              );
            } else if (state.status == BookTestResultStatus.success) {
              bool isPassed =
                  (state.testResult?.trueAnswersPercent?.toDouble() ?? 0) >
                          widget.bookPercentage
                      ? true
                      : false;
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
                width: MediaQuery.of(context).size.width,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Spacer(
                      flex: 2,
                    ),
                    TestCircularProgress(
                      total: 100,
                      done:
                          state.testResult?.trueAnswersPercent?.toDouble() ?? 0,
                      size: 230,
                      holeRadius: 70,
                      bookQuiz: true,
                      isPassed: isPassed,
                      fontSize: 40,
                    ),
                    Spacer(),
                    Card(
                      margin: EdgeInsets.symmetric(horizontal: 20.w),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius8.r)),
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        padding: EdgeInsets.all(10.w),
                        child: Column(
                          children: [
                            Text(
                              (isPassed == true && widget.isLastChapter == true)
                                  ? LocaleKeys.you_completed_book.tr()
                                  : isPassed
                                      ? LocaleKeys.answer_satisfiction.tr()
                                      : LocaleKeys.answer_unsatisfied.tr(),
                              textAlign: TextAlign.center,
                              style: TextStyle(fontSize: 18.sp),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Spacer(),
                    Visibility(
                      visible:
                          (isPassed == true && widget.isLastChapter == true)
                              ? false
                              : true,
                      child: MaterialButton(
                        onPressed: () {
                          // getChapterFromLocal();

                          var currentChapterIndex = bookProgress
                                  .getBookProgress(
                                      widget.backendBookModel.sId ?? '')
                                  .currentChapterIndex ??
                              0;
                          // widget.backendBookModel.chaptersList = chaptersList;
                          if (isPassed) {
                            EpubAwesome.openBook(
                                widget.backendBookModel, context,
                                starterChapter: currentChapterIndex + 1);
                          } else {
                            EpubAwesome.openBook(
                                widget.backendBookModel, context,
                                starterChapter: currentChapterIndex);
                          }
                        },
                        minWidth: MediaQuery.of(context).size.width,
                        height: 55.h,
                        color: isPassed ? cGreenColor : cRedColor,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(cRadius36.r)),
                        child: Text(
                          isPassed
                              ? LocaleKeys.go_through.tr()
                              : LocaleKeys.backing.tr(),
                          style: TextStyle(color: cWhiteColor, fontSize: 16.sp),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        // getChapterFromLocal();
                        //widget.backendBookModel.chaptersList = chaptersList;
                        EpubAwesome.openBook(widget.backendBookModel, context,
                            shouldOpenDrawer: true);
                      },
                      minWidth: MediaQuery.of(context).size.width,
                      height: 55.h,
                      color: cFirstColor,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius36.r)),
                      child: Text(
                        LocaleKeys.chapters.tr(),
                        style: TextStyle(color: cWhiteColor, fontSize: 16.sp),
                      ),
                    ),
                    Spacer(
                      flex: 2,
                    ),
                  ],
                ),
              );
            } else if (state.status == BookTestResultStatus.failure) {
              return Container(
                color: Theme.of(context).cardTheme.color,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ClipRect(
                        child: Container(
                          height: 300.h,
                          child: Column(
                            children: [
                              SizedBox(
                                height: 20.h,
                              ),
                              Expanded(
                                  child: SvgPicture.asset(
                                Assets.iconsWarning,
                                height: 140.h,
                              )),
                              Padding(
                                  padding: EdgeInsets.only(
                                      top: 10.h,
                                      left: 30.w,
                                      right: 30.w,
                                      bottom: 10.h),
                                  child: Text(
                                    state.message.toString(),
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color: cGrayColor1),
                                  )),
                              CupertinoButton(
                                  child: Text(
                                    LocaleKeys.refresh.tr(),
                                    style: TextStyle(
                                        color: cGrayColor1, fontSize: 20.sp),
                                  ),
                                  color: cGrayColor1.withAlpha(80),
                                  onPressed: () {
                                    BlocProvider.of<BookTestResultCubit>(
                                            context)
                                        .checkResult(
                                            testSend: widget.bookTestSend,
                                            bookPercentage:
                                                widget.bookPercentage,
                                            bookId:
                                                widget.backendBookModel.sId ??
                                                    "");
                                  }),
                              SizedBox(
                                height: 20.h,
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            } else {
              return SizedBox();
            }
          },
        ),
      ),
    );
  }
}
