import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/usescases/usecase.dart';

import '../../data/models/book_model.dart';
import '../repositories/book_repository.dart';

class URow extends UseCase<dynamic, DownloadBookParams> {
  final BookRepository bookRepository;

  URow({required this.bookRepository});

  @override
  Future<Either<Failure, dynamic>> call(DownloadBookParams params) {
    return bookRepository.downloadBookAndChapters(
        params.refresh!, params.bookModel);
  }
}

class DownloadBookParams extends Equatable {
  final bool? refresh;
  final BookModel? bookModel;

  DownloadBookParams(this.refresh, this.bookModel);

  @override
  List<Object?> get props => [refresh];
}


