import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/usescases/usecase.dart';
import 'package:yetakchi/features/electron_library/book_list/domain/repositories/book_repository.dart';

class UBook extends UseCase<dynamic, GetBookParams> {
  final BookRepository bookRepository;

  UBook({required this.bookRepository});

  @override
  Future<Either<Failure, dynamic>> call(GetBookParams params) {
    return bookRepository.getBooks(params.refresh!);
  }
}

class GetBookParams extends Equatable {
  final bool? refresh;

  GetBookParams(this.refresh);

  @override
  List<Object?> get props => [refresh];
}

