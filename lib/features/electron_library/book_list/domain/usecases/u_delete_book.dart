import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/usescases/usecase.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/domain/repositories/book_repository.dart';

class UDeleteBook extends UseCase<dynamic, DeleteBookFromLocalParams> {
  final BookRepository bookRepository;

  UDeleteBook({required this.bookRepository});

  @override
  Future<Either<Failure, dynamic>> call(DeleteBookFromLocalParams params) {
    return bookRepository.bookDeleteFromLocal(params.bookModel);
  }
}

class DeleteBookFromLocalParams extends Equatable {
  final BookModel? bookModel;

  DeleteBookFromLocalParams(this.bookModel);

  @override
  List<Object?> get props => [];
}
