part of 'book_bloc.dart';

@immutable
abstract class BookState {
 const BookState();
}

class BookInitialState extends BookState {
  const BookInitialState() : super();
}

class BookLoadingState extends BookState {
  const BookLoadingState() : super();
}

class BookSuccessState extends BookState {
  final List<BookModel> bookList;

  BookSuccessState({required this.bookList});
}

class BookFailureState extends BookState {
  const BookFailureState() : super();
}

class NoInternetConnectionState extends BookState {
  const NoInternetConnectionState() : super();
}

class BookEmptyState extends BookState {
  const BookEmptyState() : super();
}
