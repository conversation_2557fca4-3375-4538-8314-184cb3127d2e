import 'dart:async';

import 'package:bloc/bloc.dart';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:flutter/cupertino.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/domain/usecases/u_book.dart';



part 'book_event.dart';

part 'book_state.dart';

class BookBloc extends Bloc<BookEvent, BookState> {
  final UBook book;

  BookBloc({required this.book}) : super(BookInitialState()) {
    on<GetBooks>(getBooks, transformer: droppable());
  }

  FutureOr<void> getBooks(GetBooks event, Emitter<BookState> emit) async {
    emit(BookLoadingState());

    final result = await book(GetBookParams(event.refresh));

    result.fold(
      (failure) => {
        if (failure is NoConnectionFailure)
          {emit(NoInternetConnectionState())}
        else if (failure is ServerFailure)
          {emit(BookFailureState())}
      },
      (r) => {
        if (r is BookSuccessState)
          {emit(BookSuccessState(bookList: r.bookList))}
        else
          {emit(BookSuccessState(bookList: []))}
      },
    );
  }
}
