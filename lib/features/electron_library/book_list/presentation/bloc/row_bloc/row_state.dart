part of 'row_bloc.dart';

@immutable
abstract class RowState {
  const RowState();
}

class RowInitialState extends RowState {
  const RowInitialState() : super();
}

class RowLoadingState extends RowState {
  const RowLoadingState() : super();
}

class RowBookSuccessState extends RowState {
  final List<ChapterModel> chaptersList;

  RowBookSuccessState({required this.chaptersList});
}

class RowBookFailureState extends RowState {
  const RowBookFailureState() : super();
}

class NoInternetConnectionState extends RowState {
  const NoInternetConnectionState() : super();
}

class <PERSON>BookEmptyState extends RowState {
  const RowBookEmptyState() : super();
}

class RowDeleteBookSuccessState extends RowState {
  RowDeleteBookSuccessState();
}