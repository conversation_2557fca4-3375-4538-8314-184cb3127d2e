import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:meta/meta.dart';
import 'package:yetakchi/core/errors/failures.dart';

import '../../../data/models/book_model.dart';
import '../../../data/models/chapter_model.dart';
import '../../../domain/usecases/u_delete_book.dart';
import '../../../domain/usecases/u_row.dart';

part 'row_event.dart';

part 'row_state.dart';

class RowBloc extends Bloc<RowEvent, RowState> {
  final URow row;
  final UDeleteBook deleteRow;

  RowBloc({required this.deleteRow, required this.row})
      : super(RowInitialState()) {
    on<DownloadBook>(getDownloadBookAndChapters, transformer: droppable());
    on<DeleteBookFromLocal>(deleteBookFromLocal, transformer: droppable());
  }

  FutureOr<void> getDownloadBookAndChapters(
      DownloadBook event, Emitter<RowState> emit) async {
    emit(RowLoadingState());

    final result =
        await row(DownloadBookParams(event.refresh, event.bookModel));

    result.fold(
      (failure) => {
        if (failure is NoConnectionFailure)
          {emit(NoInternetConnectionState())}
        else if (failure is ServerFailure)
          {emit(RowBookFailureState())}
      },
      (r) => {
        if (r is RowBookSuccessState)
          {emit(RowBookSuccessState(chaptersList: r.chaptersList))}
        else
          {emit(RowBookSuccessState(chaptersList: []))}
      },
    );
  }

  FutureOr<void> deleteBookFromLocal(
      DeleteBookFromLocal event, Emitter<RowState> emit) async {
    emit(RowLoadingState());

    final r = await deleteRow(DeleteBookFromLocalParams(event.bookModel));

    r.fold(
      (failure) => {
        if (failure is LocalFailure) {emit(RowBookFailureState())}
      },
      (r) => {
        if (r is RowDeleteBookSuccessState) {emit(RowDeleteBookSuccessState())}
      },
    );
  }
}
