
///For later usage
// import 'dart:ui';
//
// import 'package:epub_view/epub_view.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:yetakchi/core/function/functions.dart';
// import 'package:yetakchi/core/utils/app_constants.dart';
//
//
// import 'package:yetakchi/generated/assets.dart';
//
// PreferredSize AppBookHeaderWidget(
//     {
//     required VoidCallback onBackTap,
//     required String actionImage,
//     required VoidCallback onActionTap,
//     bool? isActionImageVisibility,
//     FontWeight? fontWeight,
//     required EpubController epubReaderController}) {
//   return PreferredSize(
//     preferredSize: Size.fromHeight(HEADER_SIZE.h),
//     child: BookHeaderWidget(
//       onBackTap: onBackTap,
//       actionImage: actionImage,
//       onActionTap: onActionTap,
//       isActionImageVisibility: isActionImageVisibility,
//       fontWeight: fontWeight,
//       epubReaderController: epubReaderController,
//     ), // Set this height
//   );
// }
//
// class BookHeaderWidget extends StatelessWidget {
//   final String actionImage;
//   final VoidCallback onBackTap;
//   final VoidCallback onActionTap;
//   final bool? isActionImageVisibility;
//   final FontWeight? fontWeight;
//   final EpubController epubReaderController;
//
//   const BookHeaderWidget(
//       {super.key,
//       required this.actionImage,
//       required this.onBackTap,
//       required this.onActionTap,
//       this.isActionImageVisibility,
//       this.fontWeight,
//       required this.epubReaderController});
//
//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: ClipRRect(
//         borderRadius: BorderRadius.only(
//             bottomRight: Radius.circular(20.r),
//             bottomLeft: Radius.circular(20.r)),
//         child: Container(
//           margin: EdgeInsets.symmetric(vertical: 25.h),
//           padding: EdgeInsets.symmetric(horizontal: 15.w),
//           width: MediaQuery.of(context).size.width,
//           child: Row(
//             mainAxisSize: MainAxisSize.max,
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               ClipOval(
//                 child: Material(
//                   color: isDark() ? cCardDarkColor : cFirstColor.withAlpha(20),
//                   child: IconButton(
//                     iconSize: 30.r,
//                     onPressed: onBackTap,
//                     icon: SvgPicture.asset(
//                       Assets.iconsArrowLeft,
//                       width: 20.h,
//                       height: 20.h,
//                       colorFilter: ColorFilter.mode(
//                           isDark() ? cPrimaryTextDark : cFirstColor,
//                           BlendMode.srcIn),
//                     ),
//                   ),
//                 ),
//               ),
//               SizedBox(width: 8.w,),
//               Expanded(
//                 child: EpubViewActualChapter(
//                   controller: epubReaderController,
//                   builder: (chapterValue) => Text(
//                     chapterValue?.chapter?.Title?.replaceAll('\n', '').trim() ??
//                         '',
//                     textAlign: TextAlign.center,
//                     maxLines: 1,
//                     style: TextStyle(
//                       fontSize: 18.sp,
//                       fontWeight: fontWeight ?? FontWeight.w500,
//                     ),
//                   ),
//                 ),
//               ),
//               isActionImageVisibility == true
//                   ? SvgPicture.asset(
//                       actionImage,
//                       width: 45.h,
//                       height: 45.h,
//                     )
//                   : SizedBox(
//                       width: 45.h,
//                       height: 45.h,
//                     ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
