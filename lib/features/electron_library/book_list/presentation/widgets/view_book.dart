
///For later usage
// import 'package:epub_view/epub_view.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:yetakchi/core/utils/app_constants.dart';
// import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
// import 'package:yetakchi/features/electron_library/book_list/presentation/widgets/book_header.dart';
// import 'package:yetakchi/generated/assets.dart';
//
// class ReviewBookPage extends StatefulWidget {
//   BookModel? bookModel;
//
//   ReviewBookPage(this.bookModel, {super.key});
//
//   @override
//   State<ReviewBookPage> createState() => _ReviewBookPageState();
// }
//
// class _ReviewBookPageState extends State<ReviewBookPage>
//     with WidgetsBindingObserver {
//   late EpubController _epubReaderController;
//
//   @override
//   void initState() {
//     WidgetsBinding.instance.addObserver(this);
//
//     _epubReaderController = EpubController(
//       document: EpubDocument.openAsset('assets/documents/sway.epub'),
//       // epubCfi:
//       //     'epubcfi(/6/26[id4]!/4/2/2[id4]/22)', // book.epub Chapter 3 paragraph 10
//     );
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       // appBar: AppBar(),
//       appBar: AppBookHeaderWidget(
//         fontWeight: FontWeight.w600,
//         onBackTap: () {
//           Navigator.pop(context);
//         },
//         actionImage: Assets.iconsPlaceholder,
//         onActionTap: () {},
//         epubReaderController: _epubReaderController,
//       ),
//       endDrawerEnableOpenDragGesture: true,
//       drawerEnableOpenDragGesture: true,
//       endDrawer: Drawer(
//         child: EpubViewTableOfContents(controller: _epubReaderController),
//       ),
//       body: Column(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Expanded(
//             child: Container(
//               margin: EdgeInsets.symmetric(horizontal: 14.w, vertical: 2.h),
//               decoration: BoxDecoration(
//                 color: Theme.of(context).cardTheme.color,
//                 borderRadius: BorderRadius.circular(20.r),
//                 boxShadow: [
//                   BoxShadow(
//                     color: cFirstColor.withOpacity(0.1),
//                     spreadRadius: 3.r,
//                     blurRadius: 15.r,
//                     offset: Offset(0, 10.w), // changes position of shadow
//                   ),
//                 ],
//               ),
//               child: Padding(
//                 padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.h),
//                 child: EpubView(
//                   builders: EpubViewBuilders<DefaultBuilderOptions>(
//                     options: const DefaultBuilderOptions(),
//                     chapterDividerBuilder: (_) => const Divider(),
//                   ),
//                   controller: _epubReaderController,
//                 ),
//               ),
//             ),
//           ),
//           Padding(
//             padding: EdgeInsets.all(8.w),
//             child: Text(
//               '15/${widget.bookModel?.pageTotal}',
//               style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   void _showCurrentEpubCfi(context) {
//     final cfi = _epubReaderController.generateEpubCfi();
//
//     if (cfi != null) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text(cfi),
//           action: SnackBarAction(
//             label: 'GO',
//             onPressed: () {
//               _epubReaderController.gotoEpubCfi(cfi);
//             },
//           ),
//         ),
//       );
//     }
//   }
//
//   @override
//   void didChangePlatformBrightness() {
//     _setSystemUIOverlayStyle();
//   }
//
//   Brightness get platformBrightness =>
//       MediaQueryData.fromView(WidgetsBinding.instance.window)
//           .platformBrightness;
//
//   void _setSystemUIOverlayStyle() {
//     if (platformBrightness == Brightness.light) {
//       SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
//         statusBarBrightness: Brightness.light,
//         statusBarIconBrightness: Brightness.dark,
//         systemNavigationBarColor: Colors.grey[50],
//         systemNavigationBarIconBrightness: Brightness.dark,
//       ));
//     } else {
//       SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
//         statusBarBrightness: Brightness.dark,
//         statusBarIconBrightness: Brightness.light,
//         systemNavigationBarColor: Colors.grey[850],
//         systemNavigationBarIconBrightness: Brightness.light,
//       ));
//     }
//   }
//
//   @override
//   void dispose() {
//     WidgetsBinding.instance.removeObserver(this);
//     _epubReaderController.dispose();
//     super.dispose();
//   }
// }
