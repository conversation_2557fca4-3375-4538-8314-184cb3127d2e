import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:fading_edge_scrollview/fading_edge_scrollview.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/header_widget.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/presentation/bloc/book_bloc.dart';
import 'package:yetakchi/features/electron_library/book_list/presentation/pages/book_item.dart';
import 'package:yetakchi/features/home/<USER>/sub_categories.dart';
import 'package:yetakchi/features/home/<USER>/src/transformable_list_view.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class ElectronLibraryPage extends StatefulWidget {
  const ElectronLibraryPage({super.key});



  static Widget screen() {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => di<BookBloc>()),
      ],
      child: ElectronLibraryPage(),
    );
  }

  @override
  State<ElectronLibraryPage> createState() => _ElectronLibraryPageState();
}

class _ElectronLibraryPageState extends State<ElectronLibraryPage> {
  final _listController = ScrollController();
  bool isOnSetting = false;

  Future _onlineRefresh() async {
    context.read<BookBloc>().add(GetBooks(true));
  }

  @override
  void initState() {
    _onlineRefresh();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeaderWidget(
          title: LocaleKeys
              .book_title
              .tr(),
          onBackTap: () {
            Get.back();
          },
          isBackVisible: true),
      body: Column(
          children: [
            SizedBox(
              height: 10.h,
            ),
            BlocBuilder<BookBloc, BookState>(
              builder: (context, state) {
                if (state is BookLoadingState || state is BookInitialState) {
                  return Expanded(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.only(bottom: 100.h),
                        child: CupertinoActivityIndicator(
                          color: Theme.of(context).primaryColor,
                          radius: 30.r,
                        ),
                      ),
                    ),
                  );
                } else if (state is BookSuccessState) {
                  List<BookModel> bookList = state.bookList;
                  return Expanded(
                    child: bookList.length > 0
                        ? RefreshIndicator(
                      onRefresh: _onlineRefresh,
                          child: FadingEdgeScrollView.fromScrollView(
                              gradientFractionOnEnd: 0,
                              child: TransformableListView.builder(
                                physics: BouncingScrollPhysics(
                                    parent: AlwaysScrollableScrollPhysics()),
                                controller: _listController,
                                getTransformMatrix: getTransformMatrix,
                                padding: EdgeInsets.only(bottom: 140.h, top: 10.h),
                                itemBuilder: (context, index) {
                                  return BookItem.screen(bookList[index]);
                                },
                                itemCount: bookList.length,
                              ),
                            ),
                        )
                        : Container(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  ClipRect(
                                    child: Container(
                                      height: 300.h,
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: 20.h,
                                          ),
                                          Expanded(
                                              child: Image.asset(
                                            Assets.iconsEmpty,
                                            height: 140.h,
                                          )),
                                          Padding(
                                              padding: EdgeInsets.only(
                                                  top: 10.h,
                                                  left: 30.w,
                                                  right: 30.w,
                                                  bottom: 10.h),
                                              child: Text(
                                                LocaleKeys.not_have_book.tr(),
                                                textAlign: TextAlign.center,
                                                style:
                                                    TextStyle(color: cGrayColor1),
                                              )),
                                          CupertinoButton(
                                              child: Text(
                                                LocaleKeys.refresh.tr(),
                                                style:
                                                    TextStyle(color: cGrayColor1),
                                              ),
                                              color: cGrayColor1.withAlpha(80),
                                              onPressed: () {
                                                _onlineRefresh();
                                              }),
                                          SizedBox(
                                            height: 20.h,
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                  );
                } else if (state is BookEmptyState) {
                  return Expanded(
                    child: Container(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ClipRect(
                              child: Container(
                                height: 300.h,
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    Expanded(
                                        child: Image.asset(
                                      Assets.iconsEmpty,
                                      height: 140.h,
                                    )),
                                    Padding(
                                        padding: EdgeInsets.only(
                                            top: 10.h,
                                            left: 30.w,
                                            right: 30.w,
                                            bottom: 10.h),
                                        child: Text(
                                          LocaleKeys.not_have_book.tr(),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(color: cGrayColor1),
                                        )),
                                    CupertinoButton(
                                        child: Text(
                                          LocaleKeys.refresh.tr(),
                                          style: TextStyle(color: cGrayColor1),
                                        ),
                                        color: cGrayColor1.withAlpha(80),
                                        onPressed: () {
                                          _onlineRefresh();
                                        }),
                                    SizedBox(
                                      height: 20.h,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                } else if (state is NoInternetConnectionState) {
                  return Expanded(
                    child: Container(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ClipRect(
                              child: Container(
                                height: 300.h,
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    Expanded(
                                        child: Image.asset(
                                      Assets.imagesNoConnection,
                                      height: 140.h,
                                    )),
                                    Padding(
                                        padding: EdgeInsets.only(
                                            top: 10.h,
                                            left: 30.w,
                                            right: 30.w,
                                            bottom: 10.h),
                                        child: Text(
                                          LocaleKeys.check_internet_connection
                                              .tr(),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(color: cGrayColor1),
                                        )),
                                    CupertinoButton(
                                        child: Text(
                                          LocaleKeys.refresh.tr(),
                                          style: TextStyle(color: cGrayColor1),
                                        ),
                                        color: cGrayColor1.withAlpha(80),
                                        onPressed: () {
                                          _onlineRefresh();
                                        }),
                                    SizedBox(
                                      height: 20.h,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                } else {
                  return Expanded(
                    child: Container(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ClipRect(
                              child: Container(
                                height: 300.h,
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    Expanded(
                                        child: SvgPicture.asset(
                                      Assets.iconsWarning,
                                      height: 140.h,
                                    )),
                                    Padding(
                                        padding: EdgeInsets.only(
                                            top: 10.h,
                                            left: 30.w,
                                            right: 30.w,
                                            bottom: 10.h),
                                        child: Text(
                                          LocaleKeys.error.tr(),
                                          textAlign: TextAlign.center,
                                          style: TextStyle(color: cGrayColor1),
                                        )),
                                    CupertinoButton(
                                        child: Text(
                                          LocaleKeys.refresh.tr(),
                                          style: TextStyle(color: cGrayColor1),
                                        ),
                                        color: cGrayColor1.withAlpha(80),
                                        onPressed: () {
                                          _onlineRefresh();
                                        }),
                                    SizedBox(
                                      height: 20.h,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }
              },
            ),
          ],
        ),
    );
  }

}
