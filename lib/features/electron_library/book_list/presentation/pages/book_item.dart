import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/alert_dialog.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/presentation/bloc/row_bloc/row_bloc.dart';
import 'package:yetakchi/features/electron_library/book_reader/epub_awesome.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';
import 'package:yetakchi/generated/assets.dart';

//ignore: must_be_immutable
class BookItem extends StatefulWidget {
  BookModel? bookModel;

  BookItem(this.bookModel, {super.key});

  static Widget screen(BookModel? bookModel) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => di<RowBloc>()),
      ],
      child: BookItem(bookModel),
    );
  }

  @override
  State<BookItem> createState() => _BookItemState();
}

class _BookItemState extends State<BookItem> {
  final NetworkInfo networkInfo = di();

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      onTap: () {
        if (widget.bookModel?.localPath != null &&
            widget.bookModel?.chaptersList != null &&
            widget.bookModel?.chaptersList?.length != 0) {
          EpubAwesome.openBook(widget.bookModel ?? BookModel(), context);
        } else {
          CustomToast.showToast(LocaleKeys.please_download.tr());
        }
      },
      child: BlocBuilder<RowBloc, RowState>(
        builder: (context, state) {
          if (state is RowInitialState) {
            return card(isLoading: false);
          } else if (state is RowLoadingState) {
            return card(isLoading: true);
          } else if (state is RowBookSuccessState) {
            widget.bookModel?.chaptersList = state.chaptersList;
            return card(isLoading: false);
          } else if (state is RowDeleteBookSuccessState) {
            widget.bookModel?.chaptersList = null;
            widget.bookModel?.localPath = null;
            return card(isLoading: false);
          } else {
            return card(isLoading: false);
          }
        },
      ),
    );
  }

  Widget card({required bool isLoading}) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 4.h,
      ),
      decoration: BoxDecoration(
          color: Theme.of(context).cardTheme.color,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: cFirstColor.withOpacity(0.1),
              spreadRadius: 3.r,
              blurRadius: 15.r,
              offset: Offset(0, 10.w), // changes position of shadow
            ),
          ]),
      alignment: Alignment.center,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: SizedBox(
                height: MediaQuery.of(context).size.height / 5.5,
                width: 90.w,
                child: CachedNetworkImage(
                  fit: BoxFit.cover,
                  imageUrl: widget.bookModel?.photo ?? '',
                  placeholder: (context, url) =>
                      CupertinoActivityIndicator(radius: 10.r),
                  errorWidget: (context, url, error) => SvgPicture.asset(
                    Assets.iconsBookPlaceholder,
                    width: 20.h,
                    height: 20.h,
                  ),
                ),
              ),
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: SizedBox(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                bookTitle(bookModel: widget.bookModel),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: isDark() ? cWhiteColor : cLightBlue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(width: 16.w),
                            Text(
                              widget.bookModel?.finishedPercent != null
                                  ? '${widget.bookModel?.finishedPercent}%'
                                  : '',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: isDark() ? cWhiteColor : cLightBlue,
                                fontWeight: FontWeight.w600,
                              ),
                            )
                          ],
                        ),
                        SizedBox(height: 6.h),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Container(
                            decoration: BoxDecoration(
                                color: isDark()
                                    ? cGrayColor1.withOpacity(0.2)
                                    : cLightBlue.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(20.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: cLightBlue.withOpacity(0.1),
                                    spreadRadius: 3.r,
                                    blurRadius: 15.r,
                                    offset: Offset(
                                        0, 10.w), // changes position of shadow
                                  ),
                                ]),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 4.h, horizontal: 8.w),
                              child: Text(
                                LocaleKeys.number_of_sections.tr() +
                                    ' ${widget.bookModel?.totalChaptersCount ?? 0} ',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: isDark() ? cGrayColor1 : cLightBlue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 24.h),
                      ],
                    ),
                    Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              LocaleKeys.author.tr(),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 13.sp,
                                fontWeight: FontWeight.w600,
                                color: isDark() ? cGrayColor1 : cFirstTextColor,
                              ),
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                            Flexible(
                              child: Text(
                                authorTitle(bookModel: widget.bookModel),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: isDark()
                                      ? cGrayColor1.withOpacity(0.8)
                                      : cFirstTextColor,
                                  fontSize: 13.sp,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 6.h),
                        (widget.bookModel?.localPath != null &&
                                widget.bookModel?.chaptersList != null &&
                                widget.bookModel?.chaptersList?.length != 0)
                            ? Align(
                                alignment: Alignment.centerRight,
                                child: InkWell(
                                  onTap: () async {
                                    var result = await showAlertText(
                                        context,
                                        LocaleKeys.are_you_sure_you_delete_book
                                            .tr());

                                    if (result != null && result) {
                                      BlocProvider.of<RowBloc>(context).add(
                                          DeleteBookFromLocal(
                                              widget.bookModel!));
                                    }
                                  },
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        vertical: 4.h, horizontal: 8.w),
                                    decoration: BoxDecoration(
                                        color: Colors.red.withOpacity(0.8),
                                        borderRadius:
                                            BorderRadius.circular(20.r),
                                        boxShadow: [
                                          BoxShadow(
                                            color: cLightBlue.withOpacity(0.1),
                                            spreadRadius: 3.r,
                                            blurRadius: 15.r,
                                            offset: Offset(0,
                                                10.w), // changes position of shadow
                                          ),
                                        ]),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        SvgPicture.asset(
                                          Assets.iconsCleaner,
                                          width: 20.w,
                                          height: 20.w,
                                          fit: BoxFit.fill,
                                          color: Colors.white,
                                        ),
                                        Text(
                                          LocaleKeys.clear.tr(),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            fontSize: 14.sp,
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              )
                            : (isLoading
                                ? Container(
                                    width: double.infinity,
                                    child: CupertinoButton(
                                      child: Center(
                                          child: CupertinoActivityIndicator(
                                        radius: 8.r,
                                      )),
                                      color: cFirstColor.withAlpha(80),
                                      onPressed: () async {
                                        CustomToast.showToast(
                                            LocaleKeys.wait.tr());
                                      },
                                    ),
                                  )
                                : Container(
                                    width: double.infinity,
                                    child: CupertinoButton(
                                      child: Text(
                                        LocaleKeys.download.tr(),
                                        style: TextStyle(
                                            color: isDark()
                                                ? cWhiteColor
                                                : cFirstColor,
                                            fontSize: 14.sp),
                                      ),
                                      color: cFirstColor.withAlpha(80),
                                      onPressed: () async {
                                        if (await networkInfo.isConnected) {
                                          BlocProvider.of<RowBloc>(context).add(
                                              DownloadBook(
                                                  true, widget.bookModel!));
                                        } else {
                                          CustomToast.showToast(LocaleKeys
                                              .check_internet_connection
                                              .tr());
                                        }
                                      },
                                    ),
                                  )),
                      ],
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
