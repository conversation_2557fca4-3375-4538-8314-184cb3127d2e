import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/features/electron_library/book_list/data/datasource/book_local_datasource.dart';
import 'package:yetakchi/features/electron_library/book_list/data/datasource/book_remote_datasource.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/chapter_model.dart';
import 'package:yetakchi/features/electron_library/book_list/domain/repositories/book_repository.dart';
import 'package:yetakchi/features/electron_library/book_list/presentation/bloc/book_bloc.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

import '../../presentation/bloc/row_bloc/row_bloc.dart';

class BookRepositoryImpl extends BookRepository {
  final BookRemoteDataSourceImp bookRemoteDataSourceImp;
  final BookLocalDatasourceImpl bookLocalDatasourceImpl;
  final NetworkInfo networkInfo;

  BookRepositoryImpl(
      {required this.bookRemoteDataSourceImp,
      required this.bookLocalDatasourceImpl,
      required this.networkInfo});

  @override
  Future<Either<Failure, dynamic>> getBooks(bool refresh) async {
    if (await networkInfo.isConnected && refresh) {
      try {
        List<BookModel> remoteBooks = await bookRemoteDataSourceImp.getBooks();
        List<BookModel> localBooks = await bookLocalDatasourceImpl.fetchBooks();

        List<BookModel> books = await addLocalPath(remoteBooks, localBooks);

        return Right(BookSuccessState(bookList: books));
      } on NoConnectionFailure catch (e) {
        CustomToast.showToast(LocaleKeys.check_internet_connection.tr());
        return Left(NoConnectionFailure(LocaleKeys.error_download_data.tr()));
      } on ServerFailure catch (e) {
        CustomToast.showToast(LocaleKeys.server_error.tr());
        return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      } catch (e) {
        print(e);
        CustomToast.showToast(LocaleKeys.unknown_server_error.tr());
        return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      }
    } else {
      try {
        var books = await addLocalBookWithChapters();
        return Right(BookSuccessState(bookList: books));
      } on LocalFailure {
        return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      }
    }
  }

  Future<List<BookModel>> addLocalPath(
      List<BookModel> remoteBooks, List<BookModel> localBooks) async {
    if (remoteBooks.isNotEmpty && localBooks.isNotEmpty) {
      for (BookModel localBook in localBooks) {
        for (BookModel remoteBook in remoteBooks) {
          if (remoteBook.sId.toString().contains(localBook.sId.toString())) {
            List<ChapterModel> chaptersList =
                await bookLocalDatasourceImpl.fetchChapters(remoteBook.sId);

            //Set data remote book
            remoteBook.chaptersList = chaptersList;
            remoteBook.localPath = localBook.localPath;
            remoteBook.localId = localBook.localId;

            //Update Local book
            await bookLocalDatasourceImpl.updateBook(remoteBook);
          }
        }
      }
    }
    return remoteBooks;
  }

  Future<List<BookModel>> addLocalBookWithChapters() async {
    var localBooks = await bookLocalDatasourceImpl.fetchBooks();

    if (localBooks.isNotEmpty) {
      for (BookModel localBook in localBooks) {
        if (localBook.sId != null) {
          List<ChapterModel> chaptersList =
              await bookLocalDatasourceImpl.fetchChapters(localBook.sId);
          localBook.chaptersList = chaptersList;
        }
      }
    }
    return localBooks;
  }

  @override
  Future<Either<Failure, dynamic>> downloadBookAndChapters(
      bool refresh, BookModel? bookModel) async {
    if (await networkInfo.isConnected && refresh) {
      try {
        BookModel? model =
            await bookRemoteDataSourceImp.downloadBook(bookModel);

        ///ADD Local
        if (model != null) {
          bookLocalDatasourceImpl.setBook(model);
          print('BookModel title-> ${model.titleUZ}');
        } else {
          print('Model is null');
        }

        List<ChapterModel> chaptersList =
            await bookRemoteDataSourceImp.getChapters(bookModel?.sId);

        bookLocalDatasourceImpl.setChapters(chaptersList, bookModel?.sId);

        return Right(RowBookSuccessState(chaptersList: chaptersList));
      } on NoConnectionFailure catch (e) {
        CustomToast.showToast(LocaleKeys.check_internet_connection.tr());
        return Left(NoConnectionFailure(LocaleKeys.error_download_data.tr()));
      } on ServerFailure catch (e) {
        CustomToast.showToast(LocaleKeys.server_error.tr());
        return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      } catch (e) {
        print(e);
        CustomToast.showToast(LocaleKeys.unknown_server_error.tr());
        return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      }
    } else {
      return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
    }
  }

  @override
  Future<Either<Failure, dynamic>> bookDeleteFromLocal(
      BookModel? bookModel) async {
    if (bookModel != null) {
      bookLocalDatasourceImpl.bookDeleteFromLocal(bookModel);

      return Right(RowDeleteBookSuccessState());
    } else {
      return Left(LocalFailure('Ochirishda xatolik yuz berdi'));
    }
  }
}
