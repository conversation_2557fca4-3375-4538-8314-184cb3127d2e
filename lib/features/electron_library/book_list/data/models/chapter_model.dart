import 'package:isar/isar.dart';

part 'chapter_model.g.dart';

@collection
@Name('ChapterModel')
class ChapterModel {
  Id localId = Isar.autoIncrement;
  String? id;
  String? bookId;
  String? title;
  int? chapterNumber;
  bool? completed;

  ChapterModel(
      {this.id,
      this.bookId,
      this.title,
      this.chapterNumber,
      this.completed});

  ChapterModel.fromJson(Map<String, dynamic> json) {
    id = json['_id'];
    title = json['title'];
    bookId = json['book'];
    chapterNumber = json['chapterNumber'];
    completed = json['completed'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['_id'] = this.id;
    data['title'] = this.title;
    data['chapterNumber'] = this.chapterNumber;
    data['completed'] = this.completed;
    data['book'] = this.bookId;
    return data;
  }
}
