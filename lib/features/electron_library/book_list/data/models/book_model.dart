
import 'package:isar/isar.dart';

import 'chapter_model.dart';

part 'book_model.g.dart';

@collection
@Name("BookModel")
class BookModel {
  Id localId = Isar.autoIncrement;
  String? sId;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;
  String? authorUZ;
  String? authorRU;
  String? authorQQ;
  int? pageTotal;
  String? photo;
  String? book;
  String? createdAt;
  String? updatedAt;
  String? localPath;
  @Ignore()
  List<ChapterModel>? chaptersList;
  List<String>? provinces;
  int? status;
  int? percent;
  bool? active;
  int? chapterTotal;
  int? completed;
  int? finishedPercent;
  int? totalChaptersCount;


  BookModel(
      {this.sId,
        this.titleUZ,
        this.titleRU,
        this.titleQQ,
        this.authorUZ,
        this.authorRU,
        this.authorQQ,
        this.pageTotal,
        this.photo,
        this.book,
        this.createdAt,
        this.updatedAt,
        this.provinces,
        this.status,
        this.percent,
        this.active,
        this.chapterTotal,
        this.completed,
        this.finishedPercent,
        this.totalChaptersCount
      });

  BookModel.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
    authorUZ = json['authorUZ'];
    authorRU = json['authorRU'];
    authorQQ = json['authorQQ'];
    pageTotal = json['pageTotal'];
    photo = json['photo'];
    book = json['book'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    provinces = json['provinces'].cast<String>();
    status = json['status'];
    percent = json['percent'];
    active = json['active'];
    chapterTotal = json['chapterTotal'];
    completed = json['completed'];
    finishedPercent = json['finishedPercent'];
    totalChaptersCount = json['totalChaptersCount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['_id'] = this.sId;
    data['titleUZ'] = this.titleUZ;
    data['titleRU'] = this.titleRU;
    data['titleQQ'] = this.titleQQ;
    data['authorUZ'] = this.authorUZ;
    data['authorRU'] = this.authorRU;
    data['authorQQ'] = this.authorQQ;
    data['pageTotal'] = this.pageTotal;
    data['photo'] = this.photo;
    data['book'] = this.book;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['provinces'] = this.provinces;
    data['status'] = this.status;
    data['percent'] = this.percent;
    data['active'] = this.active;
    data['chapterTotal'] = this.chapterTotal;
    data['completed'] = this.completed;
    data['finishedPercent'] = this.finishedPercent;
    data['totalChaptersCount'] = this.totalChaptersCount;
    return data;
  }


}
