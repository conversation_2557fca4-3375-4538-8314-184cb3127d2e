import 'dart:io';

import 'package:get_storage/get_storage.dart';
import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';

import '../models/chapter_model.dart';

abstract class BookLocalDatasource {
  Future<List<BookModel>> fetchBooks();

  Future<List<ChapterModel>> fetchChapters(String? id);

  Future<bool> setBooks(List<BookModel> list);

  Future<bool> setBook(BookModel bookModel);

  Future<bool> updateBook(BookModel bookModel);

  Future<bool> setChapters(List<ChapterModel> list, String? bookId);

  Future<bool> updateChapters(List<ChapterModel> list, String? bookId);

  Future<bool> bookDeleteFromLocal(BookModel bookModel);
}

class BookLocalDatasourceImpl extends BookLocalDatasource {
  final IsarService isarService = di();
  final GetStorage gs = di();

  @override
  Future<List<BookModel>> fetchBooks() async {
    try {
      List<BookModel> list =
          await isarService.isar.bookModels.where().findAll();
      return list;
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  Future<bool> setBooks(List<BookModel> list) async {
    try {
      await isarService.isar.writeTxn(() async {
        isarService.isar.bookModels.where().deleteAll();
      });

      await isarService.isar.writeTxn(() async {
        isarService.isar.bookModels.putAll(list);
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> setBook(BookModel bookModel) async {
    try {
      final existingResult = await isarService.isar.bookModels
          .where()
          .filter()
          .sIdEqualTo(bookModel.sId)
          .count();

      print('COUNT-> ${existingResult}');
      if (existingResult == 0) {
        await isarService.isar.writeTxn(() async {
          isarService.isar.bookModels.put(bookModel);
        });
        print('Successfully');
      } else {
        final result = await isarService.isar.bookModels.get(bookModel.localId);
        if (result?.localPath == null ||
            result?.localPath == null && result!.localPath!.isEmpty) {
          result?.localPath = bookModel.localPath;
          await isarService.isar
              .writeTxn(() => isarService.isar.bookModels.put(result!));

          print('Update LocalPath id ${bookModel.sId}');
        } else {
          print('Already added id ${bookModel.sId}');
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> updateBook(BookModel bookModel) async {
    try {
      final existingResult = await isarService.isar.bookModels
          .where()
          .filter()
          .sIdEqualTo(bookModel.sId)
          .count();

      if (existingResult != 0) {
        await isarService.isar
            .writeTxn(() => isarService.isar.bookModels.put(bookModel));
      } else {
        print('Book not found');
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> setChapters(
      List<ChapterModel> remoteList, String? bookId) async {
    try {
      if (remoteList.isNotEmpty && bookId != null && bookId.isNotEmpty) {
        //Update local id, Kelajak uchun
        // List<ChapterModel> chaptersLocalList = await fetchChapters(bookId);
        // print('ChapterList ==> ${chaptersLocalList.length}');
        // if (chaptersLocalList != null && chaptersLocalList.isNotEmpty) {
        //   for (ChapterModel chapterModel in chaptersLocalList) {
        //     for (ChapterModel remoteModel in remoteList) {
        //       if (chapterModel.bookId
        //           .toString()
        //           .contains(remoteModel.bookId.toString())) {
        //         remoteModel.id = chapterModel.id;
        //       }
        //     }
        //   }
        // }

        await isarService.isar.writeTxn(() async {
          isarService.isar.chapterModels
              .where()
              .filter()
              .bookIdEqualTo(bookId)
              .deleteAll();
        });

        await isarService.isar.writeTxn(() async {
          isarService.isar.chapterModels.putAll(remoteList);
        });
        print('ChapterList Remote size: ${remoteList.length}, Added local');
        return true;
      } else {
        print('Server, ChapterList is null');
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<ChapterModel>> fetchChapters(String? id) async {
    try {
      List<ChapterModel> list = await isarService.isar.chapterModels
          .where()
          .filter()
          .bookIdEqualTo(id)
          .findAll();

      return list;
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  Future<bool> updateChapters(List<ChapterModel> list, String? bookId) async {
    try {
      final existingResult = await isarService.isar.chapterModels
          .where()
          .filter()
          .bookIdEqualTo(bookId)
          .count();

      if (existingResult != 0) {
        await isarService.isar.writeTxn(() async {
          isarService.isar.chapterModels
              .where()
              .filter()
              .bookIdEqualTo(bookId)
              .deleteAll();
        });

        await isarService.isar.writeTxn(() async {
          isarService.isar.chapterModels.putAll(list);
        });
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> bookDeleteFromLocal(BookModel bookModel) async {
    try {
      if (bookModel.sId != null && bookModel.localPath != null) {
        deleteFile(bookModel.localPath.toString());

        await isarService.isar.writeTxn(() async {
          isarService.isar.chapterModels
              .where()
              .filter()
              .bookIdEqualTo(bookModel.sId)
              .deleteAll();
          print('Deleted Chapters from Isar');

          isarService.isar.bookModels
              .where()
              .filter()
              .sIdEqualTo(bookModel.sId)
              .deleteAll();

          print('Deleted Book from Isar: ${bookModel.localPath}');
        });
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  void deleteFile(String path) async {
    try {
      File file = File(path);
      var isExists = await file.exists();
      if (isExists) {
        await file.delete(recursive: true);
        print('Fayl muvaffaqiyatli o\'chirildi: $path');
      } else {
        print('Fayl korsatilgan manzilda mavjud emas! : $path');
      }
    } catch (e) {
      print('Xatolik yuz berdi: $e');
    }
  }
}
