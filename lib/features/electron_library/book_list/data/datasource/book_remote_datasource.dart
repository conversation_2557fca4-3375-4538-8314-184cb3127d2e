import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/chapter_model.dart';

abstract class BookRemoteDataSource {
  Future<List<BookModel>> getBooks();

  Future<BookModel?> downloadBook(BookModel? item);

  Future<List<ChapterModel>> getChapters(String? id);
}

class BookRemoteDataSourceImp implements BookRemoteDataSource {
  final Dio dioClient;
  List<BookModel> booksList = [];
  List<ChapterModel> chaptersList = [];
  BookModel? model = BookModel();

  BookRemoteDataSourceImp({required this.dioClient});

  @override
  Future<List<BookModel>> getBooks() async {
    booksList = await fetchBooks();
    return booksList;
  }

  Future<List<BookModel>> fetchBooks() async {
    booksList.clear();

    try {
      try {
        final response = await dioClient.get(booksPath);

        var data = response.data;
        print(data);
        print("Response->> $response");
        if (response.statusCode == 200) {
          for (int i = 0; i < (data ?? []).length; i++) {
            booksList.add(BookModel.fromJson(data[i]));
          }

          print('Books ---------' + booksList.length.toString());

          return booksList;
        } else {
          return booksList;
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            CustomToast.showToast("Xatolik: ${e.response?.data['message']}");
          }
          throw (ServerFailure('bad response'));
        }
        if (e.type == DioExceptionType.connectionError) {
          print('check your connection');
          throw (NoConnectionFailure('no connection'));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          print('unable to connect to the server');
          throw (ServerFailure('time out'));
        }

        if (e.type == DioExceptionType.unknown) {
          print('Something went wrong');
          throw (ServerFailure('unknown'));
        }
        throw (ServerFailure('unknown'));
      } catch (e) {
        print(e);
        CustomToast.showToast("Server formati xato!");
        throw (ServerFailure('Format exception'));
      }
    } on InputFormatterFailure catch (e) {
      print('Input formatter error');
      throw (ServerFailure('Input formatter failure'));
    }
  }

  @override
  Future<BookModel?> downloadBook(BookModel? item) async {
    model = item;
    print('Title-> ${model?.titleUZ}');
    if (Platform.isAndroid || Platform.isIOS) {
      String? firstPart;
      final deviceInfoPlugin = DeviceInfoPlugin();
      final deviceInfo = await deviceInfoPlugin.deviceInfo;
      final allInfo = deviceInfo.data;
      if (allInfo['version']["release"].toString().contains(".")) {
        int indexOfFirstDot = allInfo['version']["release"].indexOf(".");
        firstPart = allInfo['version']["release"].substring(0, indexOfFirstDot);
      } else {
        firstPart = allInfo['version']["release"];
      }
      int intValue = int.parse(firstPart!);
      if (intValue >= 13) {
        model = await startDownload(model);
        return model;
      } else {
        if (await Permission.storage.isGranted) {
          await Permission.storage.request();
          model = await startDownload(model);
          return model;
        } else {
          model = await startDownload(model);
          return model;
        }
      }
    }
    return model;
  }

  Future<BookModel?> startDownload(BookModel? item) async {
    try {
      try {
        Directory? appDocDir = Platform.isAndroid
            ? await getExternalStorageDirectory()
            : await getApplicationDocumentsDirectory();

        String path = '${appDocDir!.path}/${item?.sId}.epub';
        File file = File(path);

        if (!File(path).existsSync()) {
          await file.create();

          // 'https://test.e-tahlil.uz/files/book/images/test-book2/files-2047969981427020.epub+zip',
          if (item != null && item.book != null) {
            await dioClient.download(
              item.book!,
              path,
              deleteOnError: true,
              onReceiveProgress: (received, total) {
                if (total != -1) {
                  print(
                      'Downloaded: ${((received / total) * 100).toStringAsFixed(0)}%');
                }
              },
            ).whenComplete(() {
              print('File downloaded successfully');
              print('BookModel p-> $path');
              if (item != null) {
                item.localPath = path;
                print('BookModel-> ${item.authorUZ}');
                return item;
              }
            });
          }
        } else {
          print('BOOK ELSE-> ${item?.authorQQ}');
          item?.localPath = path;
          print('BOOK Path-> ${item?.localPath}');
          return item;
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            CustomToast.showToast("Xatolik: ${e.response?.data['message']}");
          }
          throw (ServerFailure('bad response'));
        }
        if (e.type == DioExceptionType.connectionError) {
          print('check your connection');
          throw (NoConnectionFailure('no connection'));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          print('unable to connect to the server');
          throw (ServerFailure('time out'));
        }

        if (e.type == DioExceptionType.unknown) {
          print('Something went wrong');
          throw (ServerFailure('unknown'));
        }
        throw (ServerFailure('unknown'));
      } catch (e) {
        print(e);
        CustomToast.showToast("Server formati xato!");
        throw (ServerFailure('Format exception $e'));
      }
    } on InputFormatterFailure catch (e) {
      print('Input formatter error $e');
      throw (ServerFailure('Input formatter failure'));
    }
    return model;
  }

  @override
  Future<List<ChapterModel>> getChapters(String? id) async {
    chaptersList.clear();
    try {
      try {
        final response = await dioClient.get(chaptersPath + "/${id}");

        var data = response.data;
        print(data);
        print("Response->> $response");
        if (response.statusCode == 200) {
          for (int i = 0; i < (data ?? []).length; i++) {
            chaptersList.add(ChapterModel.fromJson(data[i]));
          }

          print('Chapters ---------' + chaptersList.length.toString());

          return chaptersList;
        } else {
          return chaptersList;
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            CustomToast.showToast("Xatolik: ${e.response?.data['message']}");
          }
          throw (ServerFailure('bad response'));
        }
        if (e.type == DioExceptionType.connectionError) {
          print('check your connection');
          throw (NoConnectionFailure('no connection'));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          print('unable to connect to the server');
          throw (ServerFailure('time out'));
        }

        if (e.type == DioExceptionType.unknown) {
          print('Something went wrong');
          throw (ServerFailure('unknown'));
        }
        throw (ServerFailure('unknown'));
      } catch (e) {
        print(e);
        CustomToast.showToast("Server formati xato!");
        throw (ServerFailure('Format exception'));
      }
    } on InputFormatterFailure catch (e) {
      print('Input formatter error');
      throw (ServerFailure('Input formatter failure'));
    }
  }
}
