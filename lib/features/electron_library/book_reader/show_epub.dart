import 'package:epubx/epubx.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/utils.dart';
import 'package:get_storage/get_storage.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/chapter_model.dart';
import 'package:yetakchi/features/electron_library/book_reader/Helpers/chapters.dart';
import 'package:yetakchi/features/electron_library/book_reader/Helpers/progress_singleton.dart';
import 'package:yetakchi/features/electron_library/book_test/page/test_dialog.dart';
import 'Component/circle_button.dart';
import 'Helpers/pagination.dart';
import 'Model/chapter_model.dart';
import 'package:html/parser.dart';

BookProgressSingleton bookProgress = BookProgressSingleton();

String selectedFont = 'Segoe';
List<String> fontNames = [
  "Segoe",
  "Alegreya",
  "Amazon Ember",
  "Atkinson Hyperlegible",
  "Bitter Pro",
  "Bookerly",
  "Droid Sans",
  "EB Garamond",
  "Gentium Book Plus",
  "Halant",
  "IBM Plex Sans",
  "LinLibertine",
  "Literata",
  "Lora",
  "Ubuntu"
];
Color backColor = Colors.white;
Color fontColor = Colors.black;
int staticThemeId = 3;

// ignore: must_be_immutable
class ShowEpub extends StatefulWidget {
  EpubBook epubBook;
  final BookModel backendBookModel;
  bool shouldOpenDrawer;
  int starterChapter;

  ShowEpub({
    super.key,
    required this.epubBook,
    this.starterChapter = 0,
    required this.backendBookModel,
    this.shouldOpenDrawer = false,
  });

  @override
  State<StatefulWidget> createState() => Home();
}

class Home extends State<ShowEpub> {
  String htmlContent = '';
  String? innerHtmlContent;
  String textContent = '';
  bool showBrightnessWidget = false;
  bool hasTest = false;
  int firstUnfinishedChapterIndex = 0;
  final controller = ScrollController();
  Future<void> loadChapterFuture = Future.value(true);
  List<LocalChapterModel> chaptersList = [];
  double _fontSizeProgress = 17.0;
  double _fontSize = 17.0;

  late EpubBook epubBook;
  late String bookId;
  String bookTitle = '';
  String chapterTitle = '';
  double brightnessLevel = 0.5;

  // late Map<String, String> allFonts;

  // Initialize with the first font in the list
  late String selectedTextStyle;

  bool showHeader = true;
  bool isLastPage = false;
  int lastSwipe = 0;
  int prevSwipe = 0;
  bool showPrevious = false;
  bool showNext = false;
  var dropDownFontItems;

  GetStorage gs = di();

  PagingTextHandler controllerPaging = PagingTextHandler(paginate: () {});

  @override
  void initState() {
    loadThemeSettings();

    bookId = widget.backendBookModel.sId ?? '';
    epubBook = widget.epubBook;
    // allFonts = GoogleFonts.asMap().cast<String, String>();
    // fontNames = allFonts.keys.toList();
    // selectedTextStyle = GoogleFonts.getFont(selectedFont).fontFamily!;
    selectedTextStyle =
        fontNames.where((element) => element == selectedFont).first;

    getTitleFromXhtml();
    reLoadChapter(init: true);

    super.initState();
  }

  loadThemeSettings() {
    selectedFont = gs.read(LIB_FONT) ?? selectedFont;
    var themeId = gs.read(LIB_THEME) ?? staticThemeId;
    updateTheme(themeId, isInit: true);
    _fontSize = gs.read(LIB_FONT_SIZE) ?? _fontSize;
    _fontSizeProgress = _fontSize;
  }

  getTitleFromXhtml() {
    ///Listener for slider
    // controller.addListener(() {
    //   if (controller.position.userScrollDirection == ScrollDirection.forward &&
    //       showHeader == false) {
    //     showHeader = true;
    //     update();
    //   } else if (controller.position.userScrollDirection ==
    //           ScrollDirection.reverse &&
    //       showHeader) {
    //     showHeader = false;
    //     update();
    //   }
    // });

    if (epubBook.Title != null) {
      bookTitle = epubBook.Title!;
      updateUI();
    }
  }

  reLoadChapter({bool init = false, int index = -1}) async {
    int currentIndex =
        bookProgress.getBookProgress(bookId).currentChapterIndex ?? 0;

    setState(() {
      loadChapterFuture = loadChapter(
          index: init
              ? -1
              : index == -1
                  ? currentIndex
                  : index);
    });
  }

  loadChapter({int index = -1}) async {
    chaptersList = [];

    await Future.wait(epubBook.Chapters!.map((EpubChapter chapter) async {
      String? chapterTitle = chapter.Title;
      List<LocalChapterModel> subChapters = [];
      for (var element in chapter.SubChapters!) {
        subChapters.add(LocalChapterModel(
            chapter: element.Title!,
            isSubChapter: true,
            chapterModel: ChapterModel()));
      }
      chaptersList += subChapters;

      await Future.wait(
          widget.backendBookModel.chaptersList!.map((element) async {
        if (chapter.Title?.toLowerCase() == element.title?.toLowerCase()) {
          chaptersList.add(LocalChapterModel(
              chapter: chapterTitle ?? '...',
              isSubChapter: false,
              chapterModel: element));
        }
      }));
    }));

    if (chaptersList.last.chapterModel.completed == false) {
      ///TODO: Translate
      chaptersList.add(LocalChapterModel(
          chapter: 'YAKUNIY TEST',
          isSubChapter: false,
          chapterModel: ChapterModel(id: 'FINAL', title: 'TAMOM')));
    }

    ///Identify if we should show test to show next chapter
    identifyShouldLock();

    firstUnfinishedChapterIndex = chaptersList
        .indexWhere((element) => element.chapterModel.completed == false);

    ///Choose initial chapter

    if (widget.starterChapter >= 0 &&
        widget.starterChapter < chaptersList.length) {
      setupNavButtons();
      await updateContentAccordingChapter(
          index == -1 ? widget.starterChapter : index);
    } else {
      setupNavButtons();
      await updateContentAccordingChapter(0);
      CustomToast.showToast(
          "Invalid chapter number. Range [0-${chaptersList.length}]");
    }
  }

  identifyShouldLock() {
    int index = bookProgress.getBookProgress(bookId).currentChapterIndex ?? 0;
    if (index < chaptersList.length - 1) {
      hasTest = !(chaptersList[index].chapterModel.completed ?? true);
    }
  }

  updateContentAccordingChapter(int chapterIndex) async {
    ///Set current chapter index
    await bookProgress.setCurrentChapterIndex(bookId, chapterIndex);

    ///Identify if we should show test to show next chapter
    identifyShouldLock();

    String content = epubBook.Chapters![chapterIndex].HtmlContent!;

    await Future.wait(epubBook.Chapters!.map((EpubChapter chapter) async {
      content = epubBook.Chapters![chapterIndex].HtmlContent!;

      List<EpubChapter>? subChapters = chapter.SubChapters;
      if (subChapters != null && subChapters.isNotEmpty) {
        for (int i = 0; i < subChapters.length; i++) {
          content = content + subChapters[i].HtmlContent!;
        }
      } else {
        subChapters?.forEach((element) {
          if (element.Title == epubBook.Chapters![chapterIndex].Title) {
            content = element.HtmlContent!;
          }
        });
      }
    }));

    htmlContent = content;

    // Regular expression to match the <title> tag and its content
    RegExp titleTagExp = RegExp(r'<title>.*?<\/title>', caseSensitive: false, dotAll: true);

    // Remove the <title> tag
    htmlContent = htmlContent.replaceAll(titleTagExp, '');
    textContent = parse(htmlContent).documentElement!.text;

    ///Experiment
    // textContent = textToHtml(textContent);
    if (isHTML(textContent)) {
      innerHtmlContent = textContent;
    }
    // textContent = textContent.replaceAll('Unknown', '').trim();

    controllerPaging.paginate();

    setupNavButtons();
  }

  bool isHTML(String str) {
    final RegExp htmlRegExp =
        RegExp('<[^>]*>', multiLine: true, caseSensitive: false);
    return htmlRegExp.hasMatch(str);
  }

  setupNavButtons() {
    int index = bookProgress.getBookProgress(bookId).currentChapterIndex ?? 0;

    setState(() {
      if (index == 0) {
        showPrevious = false;
      } else {
        showPrevious = true;
      }
      if (index == chaptersList.length - 1) {
        showNext = false;
      } else {
        showNext = true;
      }
    });
  }

  Future<bool> backPress() async {
    // Navigator.of(context).pop();
    return true;
  }

  void setBrightness(double brightness) async {
    await ScreenBrightness().setScreenBrightness(brightness);
    await Future.delayed(const Duration(seconds: 5));
    showBrightnessWidget = false;
    updateUI();
  }

  updateFontSettings() {
    return showModalBottomSheet(
        context: context,
        elevation: 10,
        clipBehavior: Clip.antiAlias,
        backgroundColor: backColor,
        enableDrag: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r))),
        builder: (context) {
          return SingleChildScrollView(
              child: StatefulBuilder(
                  builder: (BuildContext context, setState) => SizedBox(
                        height: 170.h,
                        child: Column(
                          children: [
                            Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: 10.h, vertical: 8.w),
                              height: 45.h,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  GestureDetector(
                                    onTap: () {
                                      updateTheme(1);
                                    },
                                    child: CircleButton(
                                        backColor: cVioletishColor,
                                        fontColor: Colors.black,
                                        id: 1),
                                  ),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      updateTheme(2);
                                    },
                                    child: CircleButton(
                                        backColor: cBluishColor,
                                        fontColor: Colors.black,
                                        id: 2),
                                  ),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      updateTheme(3);
                                    },
                                    child: CircleButton(
                                        id: 3,
                                        backColor: Colors.white,
                                        fontColor: Colors.black),
                                  ),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      updateTheme(4);
                                    },
                                    child: CircleButton(
                                        id: 4,
                                        backColor: Colors.black,
                                        fontColor: Colors.white),
                                  ),
                                  SizedBox(
                                    width: 10.w,
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      updateTheme(5);
                                    },
                                    child: CircleButton(
                                        id: 5,
                                        backColor: cPinkishColor,
                                        fontColor: Colors.black),
                                  ),
                                ],
                              ),
                            ),
                            Divider(
                              thickness: 1.h,
                              height: 0,
                              indent: 0,
                              color: Colors.grey,
                            ),
                            Expanded(
                              child: Container(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.h),
                                  child: Column(
                                    children: [
                                      StatefulBuilder(
                                        builder: (BuildContext context,
                                                StateSetter setState) =>
                                            Theme(
                                          data: Theme.of(context)
                                              .copyWith(canvasColor: backColor),
                                          child: DropdownButtonHideUnderline(
                                            child: DropdownButton<String>(
                                                value: selectedFont,
                                                isExpanded: true,
                                                menuMaxHeight: 400.h,
                                                onChanged: (newValue) {
                                                  selectedFont =
                                                      newValue ?? 'Segoe';

                                                  selectedTextStyle = fontNames
                                                      .where((element) =>
                                                          element ==
                                                          selectedFont)
                                                      .first;

                                                  gs.write(
                                                      LIB_FONT, selectedFont);

                                                  ///For updating inside
                                                  setState(() {});
                                                  controllerPaging.paginate();
                                                  updateUI();
                                                },
                                                items: fontNames.map<
                                                    DropdownMenuItem<
                                                        String>>((String font) {
                                                  return DropdownMenuItem<
                                                      String>(
                                                    value: font,
                                                    child: Text(
                                                      font,
                                                      style: TextStyle(
                                                          color: selectedFont ==
                                                                  font
                                                              ? cFirstColor
                                                              : fontColor,
                                                          fontSize:
                                                              context.isTablet
                                                                  ? 10.sp
                                                                  : 15.sp,
                                                          fontWeight:
                                                              selectedFont ==
                                                                      font
                                                                  ? FontWeight
                                                                      .bold
                                                                  : FontWeight
                                                                      .normal,
                                                          fontFamily: font),
                                                    ),
                                                  );
                                                }).toList()),
                                          ),
                                        ),
                                      ),
                                      Row(
                                        children: [
                                          Text(
                                            "Aa",
                                            style: TextStyle(
                                                fontSize: 15.sp,
                                                color: fontColor,
                                                fontWeight: FontWeight.bold),
                                          ),
                                          Expanded(
                                            child: Slider(
                                              activeColor: staticThemeId == 4
                                                  ? Colors.grey.withOpacity(0.8)
                                                  : Colors.blue,
                                              value: _fontSizeProgress,
                                              min: 15.0,
                                              max: 30.0,
                                              onChangeEnd: (double value) {
                                                _fontSize = value;

                                                gs.write(
                                                    LIB_FONT_SIZE, _fontSize);

                                                ///For updating outside
                                                updateUI();
                                                controllerPaging.paginate();
                                              },
                                              onChanged: (double value) {
                                                ///For updating widget's inside
                                                setState(() {
                                                  _fontSizeProgress = value;
                                                });
                                              },
                                            ),
                                          ),
                                          Text(
                                            "Aa",
                                            style: TextStyle(
                                                color: fontColor,
                                                fontSize: 20.sp,
                                                fontWeight: FontWeight.bold),
                                          )
                                        ],
                                      )
                                    ],
                                  )),
                            ),
                          ],
                        ),
                      )));
        });
  }

  updateTheme(int id, {bool isInit = false}) {
    staticThemeId = id;
    if (id == 1) {
      backColor = cVioletishColor;
      fontColor = Colors.black;
    } else if (id == 2) {
      backColor = cBluishColor;
      fontColor = Colors.black;
    } else if (id == 3) {
      backColor = Colors.white;
      fontColor = Colors.black;
    } else if (id == 4) {
      backColor = Colors.black;
      fontColor = Colors.white;
    } else {
      backColor = cPinkishColor;
      fontColor = Colors.black;
    }

    gs.write(LIB_THEME, id);

    if (!isInit) {
      Navigator.of(context).pop();
      controllerPaging.paginate();
      updateUI();
    }
  }

  ///Update widget tree
  updateUI() {
    setState(() {});
  }

  nextChapter() async {
    ///Set page to initial
    await bookProgress.setCurrentPageIndex(bookId, 0);

    var index = bookProgress.getBookProgress(bookId).currentChapterIndex ?? 0;

    if (index != chaptersList.length - 1) {
      reLoadChapter(index: index + 1);
    }
  }

  prevChapter() async {
    ///Set page to initial
    await bookProgress.setCurrentPageIndex(bookId, 0);

    var index = bookProgress.getBookProgress(bookId).currentChapterIndex ?? 0;

    if (index != 0) {
      reLoadChapter(index: index - 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: backPress,
        child: Scaffold(
          backgroundColor: backColor,
          body: SafeArea(
            child: Stack(
              children: [
                Column(
                  children: [
                    Expanded(
                        child: Stack(
                      children: [
                        FutureBuilder<void>(
                            future: loadChapterFuture,
                            builder: (context, snapshot) {
                              switch (snapshot.connectionState) {
                                case ConnectionState.waiting:
                                  {
                                    // Otherwise, display a loading indicator.
                                    return Center(
                                        child: CupertinoActivityIndicator(
                                      color: Theme.of(context).primaryColor,
                                      radius: 30.r,
                                    ));
                                  }
                                default:
                                  {
                                    if (widget.shouldOpenDrawer) {
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                        openTableOfContents();
                                      });

                                      widget.shouldOpenDrawer = false;
                                    }

                                    var currentChapterIndex = bookProgress
                                            .getBookProgress(bookId)
                                            .currentChapterIndex ??
                                        0;

                                    return PagingWidget(
                                      textContent,
                                      innerHtmlContent,
                                      starterPageIndex: bookProgress
                                              .getBookProgress(bookId)
                                              .currentPageIndex ??
                                          0,
                                      style: TextStyle(
                                          backgroundColor: backColor,
                                          fontSize: _fontSize.sp,
                                          fontFamily: selectedTextStyle,
                                          color: fontColor),
                                      handlerCallback: (ctrl) {
                                        controllerPaging = ctrl;
                                      },
                                      onTextTap: () {
                                        if (showHeader) {
                                          showHeader = false;
                                        } else {
                                          showHeader = true;
                                        }
                                        updateUI();
                                      },
                                      onPageFlip: (currentPage, totalPages) {
                                        if (currentPage == totalPages - 1) {
                                          bookProgress.setCurrentPageIndex(
                                              bookId, 0);
                                        } else {
                                          bookProgress.setCurrentPageIndex(
                                              bookId, currentPage);
                                        }

                                        if (isLastPage) {
                                          showHeader = true;
                                        }
                                        isLastPage = false;
                                        updateUI();

                                        if (currentPage == 0) {
                                          prevSwipe++;
                                          if (prevSwipe > 1) {
                                            prevChapter();
                                          }
                                        } else {
                                          prevSwipe = 0;
                                        }
                                      },
                                      onLastPage: (index, totalPages) async {
                                        if (totalPages > 1) {
                                          lastSwipe++;
                                        } else {
                                          lastSwipe = 2;
                                        }

                                        var currentChapter = (bookProgress
                                                    .getBookProgress(bookId)
                                                    .currentChapterIndex ??
                                                0) +
                                            1;

                                        ///Last page & last chapter
                                        if (!(chaptersList.length ==
                                            currentChapter)) {
                                          if (!hasTest && lastSwipe > 1) {
                                            nextChapter();
                                            lastSwipe = 0;
                                          }
                                        }

                                        isLastPage = true;

                                        if (hasTest) {
                                          showHeader = false;
                                        }

                                        updateUI();
                                      },
                                      hasTest: hasTest,
                                      backendBookModel: widget.backendBookModel,
                                      chapterId:
                                          chaptersList[currentChapterIndex]
                                              .chapterModel
                                              .id
                                              .toString(),
                                      chapterTitle:
                                          chaptersList[currentChapterIndex]
                                              .chapterModel
                                              .title
                                              .toString(),
                                      isLastChapter:
                                          chaptersList.last.chapterModel.id ==
                                                  'FINAL'
                                              ? currentChapterIndex ==
                                                  chaptersList.length - 2
                                              : currentChapterIndex ==
                                                  chaptersList.length - 1,
                                    );
                                  }
                              }
                            }),
                        //)

                        Align(
                          alignment: Alignment.bottomRight,
                          child: Visibility(
                            visible: showBrightnessWidget,
                            child: Container(
                                height: 150.h,
                                width: 30.w,
                                alignment: Alignment.bottomCenter,
                                margin:
                                    EdgeInsets.only(bottom: 40.h, right: 15.w),
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.brightness_7,
                                      size: 14.h,
                                      color: fontColor,
                                    ),
                                    SizedBox(
                                      height: 120.h,
                                      width: 30.w,
                                      child: RotatedBox(
                                          quarterTurns: -1,
                                          child: SliderTheme(
                                              data: SliderThemeData(
                                                activeTrackColor:
                                                    staticThemeId == 4
                                                        ? Colors.white
                                                        : Colors.blue,
                                                disabledThumbColor:
                                                    Colors.transparent,
                                                inactiveTrackColor: Colors.grey
                                                    .withOpacity(0.5),
                                                trackHeight: 5.0,

                                                thumbColor: staticThemeId == 4
                                                    ? Colors.grey
                                                        .withOpacity(0.8)
                                                    : Colors.blue,
                                                thumbShape:
                                                    RoundSliderThumbShape(
                                                        enabledThumbRadius:
                                                            0.r),
                                                // Adjust the size of the thumb
                                                overlayShape:
                                                    RoundSliderOverlayShape(
                                                        overlayRadius: 10
                                                            .r), // Adjust the size of the overlay
                                              ),
                                              child: Slider(
                                                value: brightnessLevel,
                                                min: 0.0,
                                                max: 1.0,
                                                onChangeEnd: (double value) {
                                                  setBrightness(value);
                                                },
                                                onChanged: (double value) {
                                                  setState(() {
                                                    brightnessLevel = value;
                                                  });
                                                },
                                              ))),
                                    ),
                                  ],
                                )),
                          ),
                        )
                      ],
                    )),
                    AnimatedContainer(
                      height: showHeader ? 50.h : 0,
                      duration: const Duration(milliseconds: 100),
                      color: backColor,
                      child: Container(
                        height: 40.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: backColor,
                          border: Border(
                            top: BorderSide(width: 3.w, color: cFirstColor),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            SizedBox(
                              width: 5.w,
                            ),
                            Visibility(
                              visible: showPrevious,
                              child: IconButton(
                                  onPressed: () {
                                    prevChapter();
                                  },
                                  icon: Icon(
                                    Icons.arrow_back_ios,
                                    size: 15.h,
                                    color: fontColor,
                                  )),
                            ),
                            SizedBox(
                              width: 5.w,
                            ),
                            Expanded(
                              flex: 10,
                              child: Text(
                                chaptersList.isNotEmpty
                                    ? chaptersList[bookProgress
                                                .getBookProgress(bookId)
                                                .currentChapterIndex ??
                                            0]
                                        .chapterModel
                                        .title
                                        .toString()
                                    : 'Loading...',
                                maxLines: 1,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 13.sp,
                                    overflow: TextOverflow.ellipsis,
                                    fontFamily: selectedTextStyle,
                                    fontWeight: FontWeight.bold,
                                    color: fontColor),
                              ),
                            ),
                            SizedBox(
                              width: 5.w,
                            ),
                            Visibility(
                                visible: hasTest
                                    ? true
                                    : showNext
                                        ? true
                                        : false,
                                child: IconButton(
                                    onPressed: hasTest
                                        ? () {
                                            var currentChapterIndex =
                                                bookProgress
                                                        .getBookProgress(bookId)
                                                        .currentChapterIndex ??
                                                    0;

                                            showDialog(
                                                context: context,
                                                builder: (context) {
                                                  var passPercentage = widget
                                                          .backendBookModel
                                                          .percent ??
                                                      0;

                                                  var chapterId = widget
                                                          .backendBookModel
                                                          .chaptersList?[
                                                              currentChapterIndex]
                                                          .id ??
                                                      '';

                                                  var chapterTitle = chaptersList[
                                                          currentChapterIndex]
                                                      .chapterModel
                                                      .title
                                                      .toString();

                                                  return TestDialog(
                                                      chapterId: chapterId,
                                                      chapterTitle:
                                                          chapterTitle,
                                                      bookPercentage:
                                                          passPercentage,
                                                      backendBookModel: widget
                                                          .backendBookModel,
                                                      isLastChapter: chaptersList
                                                                  .last
                                                                  .chapterModel
                                                                  .id ==
                                                              'FINAL'
                                                          ? currentChapterIndex ==
                                                              chaptersList
                                                                      .length -
                                                                  2
                                                          : currentChapterIndex ==
                                                              chaptersList
                                                                      .length -
                                                                  1);
                                                });
                                          }
                                        : () {
                                            nextChapter();
                                          },
                                    icon: Icon(
                                      Icons.arrow_forward_ios_rounded,
                                      size: 15.h,
                                      color: fontColor,
                                    ))),
                            SizedBox(
                              width: 5.w,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                AnimatedContainer(
                  height: showHeader ? 50.h : 0,
                  duration: const Duration(milliseconds: 100),
                  color: backColor,
                  child: Padding(
                    padding: EdgeInsets.only(top: 3.h),
                    child: AppBar(
                      centerTitle: true,
                      title: Text(
                        bookTitle,
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16.sp,
                            color: fontColor),
                      ),
                      backgroundColor: backColor,
                      shape: Border(
                          bottom: BorderSide(color: cFirstColor, width: 3.h)),
                      elevation: 0,
                      leading: IconButton(
                        onPressed: openTableOfContents,
                        icon: Icon(
                          Icons.menu,
                          color: fontColor,
                          size: 20.h,
                        ),
                      ),
                      actions: [
                        InkWell(
                            onTap: () {
                              updateFontSettings();
                            },
                            child: Container(
                              width: 40.w,
                              alignment: Alignment.center,
                              child: Text(
                                "Aa",
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: fontColor,
                                    fontWeight: FontWeight.bold),
                              ),
                            )),
                        SizedBox(
                          width: 5.w,
                        ),
                        InkWell(
                            onTap: () async {
                              setState(() {
                                showBrightnessWidget = true;
                              });
                              await Future.delayed(const Duration(seconds: 7));
                              setState(() {
                                showBrightnessWidget = false;
                              });
                            },
                            child: Icon(
                              Icons.brightness_high_sharp,
                              size: 20.h,
                              color: fontColor,
                            )),
                        SizedBox(
                          width: 10.w,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  openTableOfContents() async {
    bool? shouldUpdate = await Navigator.of(context).push(MaterialPageRoute(
            builder: (context) => ChaptersList(
                  lockFromIndex: firstUnfinishedChapterIndex == -1
                      ? chaptersList.length
                      : firstUnfinishedChapterIndex + 1,
                  bookId: bookId,
                  chapters: chaptersList,
                  backendBookModel: widget.backendBookModel,
                ))) ??
        false;
    if (shouldUpdate) {
      var index = bookProgress.getBookProgress(bookId).currentChapterIndex ?? 0;

      /// Update chapter index with content and set page to initial
      await reLoadChapter(index: index);
      await bookProgress.setCurrentPageIndex(bookId, 0);
    }
  }
}

// ignore: must_be_immutable
