import 'dart:io';

import 'package:epubx/epubx.dart';
import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/chapter_model.dart';
import 'package:yetakchi/features/electron_library/book_reader/show_epub.dart';

///TODO: Think about better naming / Write async future builder to open book buttons
class EpubAwesome {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  static Future<void> openBook(BookModel bookModel, BuildContext context,
      {bool shouldOpenDrawer = false,
      int starterChapter = -1,
      bool willPopScope = false}) async {
    final IsarService isarService = di();

    var chaptersList = isarService.isar.chapterModels
        .where()
        .filter()
        .bookIdEqualTo(bookModel.sId)
        .findAllSync();
    bookModel.chaptersList = chaptersList;

    ///Set starter chapter as current
    if (starterChapter != -1) {
      await bookProgress.setCurrentChapterIndex(
          bookModel.sId ?? '', starterChapter);
      await bookProgress.setCurrentPageIndex(bookModel.sId ?? '', 0);
    }

    ///TODO: Optimize with isolates
    var bytes = File(bookModel.localPath ?? '').readAsBytesSync();
    EpubBook epubBook = await EpubReader.readBook(bytes.buffer.asUint8List());

    ///Test locally
    // var bytes = await rootBundle.load(Assets.assetsBook);
    // EpubBook epubBook = await EpubReader.readBook(bytes.buffer.asUint8List());

    var route = MaterialPageRoute(
      builder: (context) {
        return ShowEpub(
          epubBook: epubBook,
          starterChapter: starterChapter >= 0
              ? starterChapter
              : bookProgress
                      .getBookProgress(bookModel.sId ?? '')
                      .currentChapterIndex ??
                  0,
          backendBookModel: bookModel,
          shouldOpenDrawer: shouldOpenDrawer,
        );
      },
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('OPENING: ${shouldOpenDrawer != false || starterChapter != -1}');
      (shouldOpenDrawer != false || starterChapter != -1) && !willPopScope
          ? Navigator.pushReplacement(
              context,
              route,
            )
          : Navigator.push(
              context,
              route,
            );
    });
  }
}
