import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/errors/failures.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/electron_library/book_reader/Model/book_progress_model.dart';

class BookProgressSingleton {
  final IsarService isarService = di();

  Future<bool> setCurrentChapterIndex(String bookId, int chapterIndex) async {
    try {
      BookProgressModel? oldBookProgressModel = await isarService
          .isar.bookProgressModels
          .where()
          .filter()
          .bookIdEqualTo(bookId)
          .findFirst();

      if (oldBookProgressModel != null) {
        oldBookProgressModel.currentChapterIndex = chapterIndex;
        await isarService.isar.writeTxn(() async {
          isarService.isar.bookProgressModels.put(oldBookProgressModel);
        });
      } else {
        var newBookProgressModel = BookProgressModel(
            currentPageIndex: 0,
            currentChapterIndex: chapterIndex,
            bookId: bookId);
        await isarService.isar.writeTxn(() async {
          isarService.isar.bookProgressModels.put(newBookProgressModel);
        });
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> setCurrentPageIndex(
      String bookId, int pageIndex) async {
    try {
      BookProgressModel? oldBookProgressModel = await isarService
          .isar.bookProgressModels
          .where()
          .filter()
          .bookIdEqualTo(bookId)
          .findFirst();

      if (oldBookProgressModel != null) {
        oldBookProgressModel.currentPageIndex = pageIndex;
        await isarService.isar.writeTxn(() async {
          isarService.isar.bookProgressModels.put(oldBookProgressModel);
        });
      } else {
        var newBookProgressModel = BookProgressModel(
            currentPageIndex: pageIndex,
            currentChapterIndex: 0,
            bookId: bookId);
        await isarService.isar.writeTxn(() async {
          isarService.isar.bookProgressModels.put(newBookProgressModel);
        });
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  BookProgressModel getBookProgress(String bookId) {
    var newBookProgressModel =
        BookProgressModel(currentPageIndex: 0, currentChapterIndex: 0);
    try {
      BookProgressModel? oldBookProgressModel = isarService
          .isar.bookProgressModels
          .where()
          .filter()
          .bookIdEqualTo(bookId)
          .findFirstSync();
      if (oldBookProgressModel != null) {
        return oldBookProgressModel;
      } else {
        return newBookProgressModel;
      }
    } on LocalFailure {
      return newBookProgressModel;
    }
  }
}
