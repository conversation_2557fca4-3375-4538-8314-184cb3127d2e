import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_test/page/book_test_page.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

import '../Model/chapter_model.dart';
import '../show_epub.dart';

// ignore: must_be_immutable
class ChaptersList extends StatelessWidget {
  List<LocalChapterModel> chapters = [];
  final String bookId;
  final BookModel backendBookModel;
  final lockFromIndex;

  ChaptersList(
      {super.key,
      required this.chapters,
      required this.bookId,
      required this.lockFromIndex,
      required this.backendBookModel});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 40.h,
        backgroundColor: backColor,
        leading: InkWell(
            onTap: () {
              Navigator.of(context).pop(false);
            },
            child: Icon(
              Icons.close,
              color: fontColor,
              size: 20.h,
            )),
        centerTitle: true,
        title: Text(
          LocaleKeys.chapters.tr(),
          style: TextStyle(
              fontWeight: FontWeight.bold, color: cFirstColor, fontSize: 15.sp),
        ),
      ),
      body: SafeArea(
        child: Container(
          color: backColor,
          padding: EdgeInsets.all(10.h),
          child: ListView.builder(
              itemCount: chapters.length,
              physics: BouncingScrollPhysics(),
              itemBuilder: (context, i) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ListTile(
                      onTap: chapters[i].chapterModel.id == 'FINAL'
                          ? i == lockFromIndex
                              ? () async {
                                  var passPercentage =
                                      backendBookModel.percent ?? 0;

                                  var totalChapters = chapters.length;

                                  var chapterId = backendBookModel
                                          .chaptersList?[i - 1].id ?? '';

                                  var chapterTitle = chapters[bookProgress
                                              .getBookProgress(bookId ?? '')
                                              .currentChapterIndex ??
                                          0]
                                      .chapterModel
                                      .title
                                      .toString();

                                  Navigator.pop(context);
                                  Navigator.pushReplacement(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              BookTestPage.screen(
                                                chapterId: chapterId,
                                                chapterTitle: chapterTitle,
                                                bookPercentage: passPercentage,
                                                isLastChapter:
                                                    i == (totalChapters - 1),
                                                backendBookModel:
                                                    backendBookModel,
                                              )));
                                }
                              : () {
                                  CustomToast.showToast(
                                      "Yakuniy testni yechish uchun avvalgi test sinlovlaridan o'ting");
                                }
                          : i >= lockFromIndex
                              ? () {
                                  CustomToast.showToast(
                                      LocaleKeys.need_testing_previous.tr());
                                }
                              : () async {
                                  await bookProgress.setCurrentChapterIndex(
                                      bookId, i);
                                  Navigator.of(context).pop(true);
                                },
                      leading: chapters[i].chapterModel.id == 'FINAL'
                          ? i == lockFromIndex
                              ? Icon(
                                  Icons.star,
                                  color: cDarkYellowColor,
                                  size: 20.h,
                                )
                              : Icon(
                                  Icons.lock,
                                  color: cGrayColor1,
                                  size: 20.h,
                                )
                          : i >= lockFromIndex
                              ? Icon(
                                  Icons.lock,
                                  color: cGrayColor1,
                                  size: 20.h,
                                )
                              : null,
                      minLeadingWidth: 20.w,
                      title: Padding(
                        padding: EdgeInsets.only(
                            left: chapters[i].isSubChapter ? 15.w : 0),
                        child: Text(chapters[i].chapter,
                            style: TextStyle(
                                color: chapters[i].chapterModel.id == 'FINAL'
                                    ? i == lockFromIndex
                                        ? cDarkYellowColor
                                        : cGrayColor1
                                    : i >= lockFromIndex
                                        ? cGrayColor1
                                        : bookProgress
                                                    .getBookProgress(bookId)
                                                    .currentChapterIndex ==
                                                i
                                            ? cFirstColor
                                            : fontColor,
                                fontFamily: fontNames
                                    .where((element) => element == selectedFont)
                                    .first,
                                fontSize: 15.sp,
                                fontWeight: chapters[i].isSubChapter
                                    ? FontWeight.w400
                                    : FontWeight.w600)),
                      ),
                      dense: true,
                    ),
                    Divider(height: 0, thickness: 1.h),
                  ],
                );
              }),
        ),
      ),
    );
  }
}
