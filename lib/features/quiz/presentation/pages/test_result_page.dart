import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/header_widget_sub.dart';
import 'package:yetakchi/features/quiz/data/model/test_result.dart';
import 'package:yetakchi/features/quiz/data/model/test_send.dart';
import 'package:yetakchi/features/quiz/presentation/widgets/incorrect_result_item.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

import '../../../../di/dependency_injection.dart';
import '../cubits/test_result_cubit/test_result_cubit.dart';
import '../widgets/correct_result_item.dart';

class TestResultPage extends StatefulWidget {
  final TestSend testSend;
  final String title;

  const TestResultPage(
      {super.key, required this.testSend, required this.title});

  static Widget screen({required TestSend testSend, required String title}) {
    return BlocProvider(
      create: (context) => di<TestResultCubit>(),
      child: TestResultPage(
        testSend: testSend,
        title: title,
      ),
    );
  }

  @override
  State<TestResultPage> createState() => _TestResultPageState();
}

class _TestResultPageState extends State<TestResultPage> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<TestResultCubit>(context)
        .checkResult(testSend: widget.testSend);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.pop(context);
        return false;
      },
      child: Scaffold(
        appBar: AppHeaderWidgetSub(
          title: widget.title,
          fontWeight: FontWeight.w600,
          onBackTap: () {
            Navigator.pop(context, true);
          },
          actionImage: Assets.iconsPlaceholder,
          onActionTap: () {},
        ),
        body: BlocConsumer<TestResultCubit, TestResultState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state.status == TestResultStatus.loading ||
                state.status == TestResultStatus.initial) {
              return Center(
                child: CupertinoActivityIndicator(
                  color: Theme.of(context).primaryColor,
                  radius: 30.r,
                ),
              );
            } else if (state.status == TestResultStatus.success) {
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Column(
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width,
                      padding: EdgeInsets.symmetric(
                          horizontal: 10.w, vertical: 10.h),
                      decoration: BoxDecoration(
                          color: Theme.of(context).cardTheme.color,
                          borderRadius: BorderRadius.circular(20.r),
                          boxShadow: [boxShadow60]),
                      child: Column(
                        children: [
                          Text(
                            LocaleKeys.test_result.tr(),
                            textAlign: TextAlign.center,
                              style: TextStyle(fontSize: 18.sp)
                          ),
                          SizedBox(
                            height: 10.h,
                          ),
                          Text(
                            "${state.testResult?.trueAnswersPercent}%",
                            style: TextStyle(
                                color: cGreenColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 30.sp),
                          ),
                          SizedBox(
                            height: 10.h,
                          ),
                          Text(
                            "${state.testResult?.trueAnswers}/${state.testResult?.totalQuestions}",
                            style: TextStyle(
                                fontSize: 20.sp, fontWeight: FontWeight.bold),
                          )
                        ],
                      ),
                    ),
                    Expanded(
                        child: ListView.builder(
                            physics: BouncingScrollPhysics(),
                            itemCount: state.testResult?.questions?.length,
                            itemBuilder: (context, index) {
                              ResultQuestions? question =
                                  state.testResult?.questions?[index];
                              if (question?.isCorrect == true) {
                                return CorrectResultItem(
                                  questions: question,
                                );
                              } else {
                                return InCorrectResultItem(
                                  questions: question,
                                );
                              }
                            }))
                  ],
                ),
              );
            } else if (state.status == TestResultStatus.failure) {
              return Container(
                color: Theme.of(context).cardTheme.color,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ClipRect(
                        child: Container(
                          height: 300.h,
                          child: Column(
                            children: [
                              SizedBox(
                                height: 20.h,
                              ),
                              Expanded(
                                  child: SvgPicture.asset(
                                Assets.iconsWarning,
                                height: 140.h,
                              )),
                              Padding(
                                  padding: EdgeInsets.only(
                                      top: 10.h,
                                      left: 30.w,
                                      right: 30.w,
                                      bottom: 10.h),
                                  child: Text(
                                    state.message.toString(),
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color: cGrayColor1),
                                  )),
                              CupertinoButton(
                                  child: Text(
                                    LocaleKeys.refresh.tr(),
                                    style: TextStyle(color: cGrayColor1),
                                  ),
                                  color: cGrayColor1.withAlpha(80),
                                  onPressed: () {
                                    BlocProvider.of<TestResultCubit>(context)
                                        .checkResult(testSend: widget.testSend);
                                  }),
                              SizedBox(
                                height: 20.h,
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            } else {
              return SizedBox();
            }
          },
        ),
      ),
    );
  }
}
