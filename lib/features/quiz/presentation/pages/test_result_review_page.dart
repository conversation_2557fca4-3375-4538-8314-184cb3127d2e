import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/header_widget_sub.dart';
import 'package:yetakchi/features/quiz/data/model/test_result.dart';
import 'package:yetakchi/features/quiz/presentation/cubits/test_result_review/test_result_review_cubit.dart';
import 'package:yetakchi/features/quiz/presentation/widgets/incorrect_result_item.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

import '../../../../di/dependency_injection.dart';
import '../widgets/correct_result_item.dart';

class TestResultReviewPage extends StatefulWidget {
  final String title;
  final String id;

  const TestResultReviewPage(
      {super.key, required this.title, required this.id});

  static Widget screen({required String title, required String id}) {
    return BlocProvider(
      create: (context) => di<TestResultReviewCubit>(),
      child: TestResultReviewPage(
        title: title,
        id: id,
      ),
    );
  }

  @override
  State<TestResultReviewPage> createState() => _TestResultReviewPageState();
}

class _TestResultReviewPageState extends State<TestResultReviewPage> {
  @override
  void initState() {
    super.initState();

    BlocProvider.of<TestResultReviewCubit>(context).reviewResult(id: widget.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeaderWidgetSub(
        title: widget.title,
        fontWeight: FontWeight.w600,
        onBackTap: () {
          Navigator.pop(context);
        },
        actionImage: Assets.iconsPlaceholder,
        onActionTap: () {},
      ),
      body: BlocConsumer<TestResultReviewCubit, TestResultReviewState>(
        listener: (context, state) {
          print(state.status);
        },
        builder: (context, state) {
          if (state.status == TestResultReviewStatus.loading ||
              state.status == TestResultReviewStatus.initial) {
            return Center(
              child: CupertinoActivityIndicator(
                color: Theme.of(context).primaryColor,
                radius: 30.r,
              ),
            );
          } else if (state.status == TestResultReviewStatus.success) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Column(
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width,
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                    decoration: BoxDecoration(
                        color: Theme.of(context).cardTheme.color,
                        borderRadius: BorderRadius.circular(20.r),
                        boxShadow: [boxShadow60]),
                    child: Column(
                      children: [
                        Text(
                         LocaleKeys.test_result.tr(),
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 18.sp),
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        Text(
                          "${state.testResultReview?.trueAnswersPercent}%",
                          style: TextStyle(
                              color: cGreenColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 30.sp),
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        Text(
                          "${state.testResultReview?.trueAnswers}/${state.testResultReview?.totalQuestions}",
                          style: TextStyle(
                              fontSize: 20.sp, fontWeight: FontWeight.bold),
                        )
                      ],
                    ),
                  ),
                  Expanded(
                      child: ListView.builder(
                          physics: BouncingScrollPhysics(),
                          itemCount: state.testResultReview?.questions?.length,
                          itemBuilder: (context, index) {
                            ResultQuestions? question =
                                state.testResultReview?.questions?[index];
                            if (question?.isCorrect == true) {
                              return CorrectResultItem(
                                questions: question,
                              );
                            } else {
                              return InCorrectResultItem(
                                questions: question,
                              );
                            }
                          }))
                ],
              ),
            );
          } else if (state.status == TestResultReviewStatus.failure) {
            return Container(
              color: Theme.of(context).cardTheme.color,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ClipRect(
                      child: Container(
                        height: 300.h,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 20.h,
                            ),
                            Expanded(
                                child: SvgPicture.asset(
                              Assets.iconsWarning,
                              height: 140.h,
                            )),
                            Padding(
                                padding: EdgeInsets.only(
                                    top: 10.h,
                                    left: 30.w,
                                    right: 30.w,
                                    bottom: 10.h),
                                child: Text(
                                  state.message.toString(),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: cGrayColor1),
                                )),
                            CupertinoButton(
                                child: Text(
                                  LocaleKeys.refresh.tr(),
                                  style: TextStyle(color: cGrayColor1),
                                ),
                                color: cGrayColor1.withAlpha(80),
                                onPressed: () {
                                  BlocProvider.of<TestResultReviewCubit>(
                                          context)
                                      .reviewResult(id: widget.id);
                                }),
                            SizedBox(
                              height: 20.h,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return SizedBox();
          }
        },
      ),
    );
  }
}
