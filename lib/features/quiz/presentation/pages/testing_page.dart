import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/custom_toast.dart';
import 'package:yetakchi/core/widgets/header_widget.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/quiz/data/model/test_model.dart';
import 'package:yetakchi/features/quiz/data/model/test_send.dart';
import 'package:yetakchi/features/quiz/presentation/cubits/test/test_cubit.dart';
import 'package:yetakchi/features/quiz/presentation/pages/test_result_page.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class TestingPage extends StatefulWidget {
  final String id;
  final String title;

  const TestingPage({super.key, required this.id, required this.title});

  static Widget screen({required String id, required String title}) {
    return BlocProvider(
      create: (context) => di<TestCubit>(),
      child: TestingPage(
        id: id,
        title: title,
      ),
    );
  }

  @override
  State<TestingPage> createState() => _TestingPageState();
}

class _TestingPageState extends State<TestingPage> {
  int selectedIndex = -1;
  int questionNumber = 0;
  List<Questions>? questions = [];
  List<SendQuestions>? sendQuestions = [];

  @override
  void initState() {
    super.initState();
    BlocProvider.of<TestCubit>(context).loadTest(widget.id);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.pop(context);
        return false;
      },
      child: Scaffold(
          appBar: AppHeaderWidget(
            title: LocaleKeys.tests.tr(),
            onBackTap: () {
              Navigator.pop(context);
            },
            isBackVisible: true,
          ),
          body: BlocConsumer<TestCubit, TestState>(
            listener: (context, state) {
              if (state.status == TestStatus.success) {
                questions = state.testModel?.questions;
              }
            },
            builder: (context, state) {
              if (state.status == TestStatus.loading ||
                  state.status == TestStatus.initial) {
                return Center(
                  child: CupertinoActivityIndicator(
                    color: Theme.of(context).primaryColor,
                    radius: 30.r,
                  ),
                );
              } else if (state.status == TestStatus.success) {
                return Container(
                  width: MediaQuery.of(context).size.width,
                  margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          physics: BouncingScrollPhysics(),
                          child: Column(
                            children: [
                              SizedBox(
                                height: 10.h,
                              ),
                              Text(
                                state.testModel?.title ?? "Empty",
                                style: TextStyle(
                                    fontSize: 20.sp, fontWeight: FontWeight.w600),
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              Text(
                                "${questionNumber + 1}/${state.testModel?.questions?.length}",
                                style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w600,fontSize: 20.sp),
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              Container(
                                width: MediaQuery.of(context).size.width,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20.w, vertical: 10.h),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(cRadius22.r),
                                    color: cFirstColor.withAlpha(20),
                                    border: Border.all(
                                        width: 1.w,
                                        color: isDark()
                                            ? cWhiteColor
                                            : cFirstColor.withAlpha(90))),
                                child: Text(
                                    textAlign: TextAlign.center,
                                    questions?[questionNumber].title ?? "Empty",style: TextStyle(fontSize: 20.sp),),
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              ListView.builder(
                                physics: NeverScrollableScrollPhysics(parent: NeverScrollableScrollPhysics()),
                                shrinkWrap: true,
                                  itemCount: questions?[questionNumber]
                                      .answerOptions
                                      ?.length,
                                  itemBuilder: (context, index) {
                                    return ZoomTapAnimation(
                                      onTap: () {
                                        setState(() {
                                          selectedIndex = index;
                                        });
                                      },
                                      child: Container(
                                        margin: EdgeInsets.symmetric(vertical: 4.h),
                                        width: MediaQuery.of(context).size.width,
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 20.w, vertical: 20.h),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(cRadius22.r),
                                            border: Border.all(
                                                width: 1,
                                                color: selectedIndex == index
                                                    ? cGreenColor
                                                    : isDark()
                                                        ? cCardDarkColor
                                                        : cFirstColor.withAlpha(90)),
                                            color: selectedIndex == index
                                                ? cGreenColor
                                                : isDark()
                                                    ? cCardDarkColor
                                                    : cWhiteColor),
                                        child: Text(
                                          questions?[questionNumber]
                                                  .answerOptions?[index]
                                                  .title ??
                                              "",
                                          style: TextStyle(
                                              color: selectedIndex == index
                                                      ? cWhiteColor
                                                      :isDark()?cPrimaryTextDark:cFirstTextColor,fontSize: 18.sp),
                                        ),
                                      ),
                                    );
                                  }),
                              SizedBox(
                                height: 10.h,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Column(
                        children: [
                          MaterialButton(
                              minWidth: MediaQuery.of(context).size.width,
                              height: 55.h,
                              color: cFirstColor,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(cRadius36)),
                              child: Text(
                                questionNumber + 1 == (questions?.length)
                                    ? LocaleKeys.finishing.tr()
                                    : LocaleKeys.next_question.tr(),
                                style:
                                    TextStyle(color: cWhiteColor, fontSize: 16.sp),
                              ),
                              onPressed: () {
                                if (selectedIndex != -1) {
                                  if (questionNumber + 1 == questions?.length) {
                                    sendQuestions?.add(SendQuestions(
                                        question: questions?[questionNumber].id,
                                        userAnswer: questions?[questionNumber]
                                            .answerOptions?[selectedIndex]
                                            .id));
                                    selectedIndex = -1;
                                    TestSend testSend = TestSend(
                                        quiz: widget.id, questions: sendQuestions);

                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                TestResultPage.screen(
                                                  testSend: testSend,
                                                  title: widget.title,
                                                ))).then((value) {
                                      Navigator.pop(context, value);
                                    });
                                  } else {
                                    sendQuestions?.add(SendQuestions(
                                        question: questions?[questionNumber].id,
                                        userAnswer: questions?[questionNumber]
                                            .answerOptions?[selectedIndex]
                                            .id));
                                    selectedIndex = -1;
                                    setState(() {
                                      questionNumber += 1;
                                    });
                                  }
                                } else {
                                  CustomToast.showToast(
                                      LocaleKeys.select_answer.tr());
                                }
                              }),
                          SizedBox(
                            height: 8.h,
                          ),
                        ],
                      )
                    ],
                  ),
                );
              } else if (state.status == TestStatus.failure) {
                return Container(
                  color: Theme.of(context).cardTheme.color,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ClipRect(
                          child: Container(
                            height: 300.h,
                            child: Column(
                              children: [
                                SizedBox(
                                  height: 20.h,
                                ),
                                Expanded(
                                    child: SvgPicture.asset(
                                  Assets.iconsWarning,
                                  height: 140.h,
                                )),
                                Padding(
                                    padding: EdgeInsets.only(
                                        top: 10.h,
                                        left: 30.w,
                                        right: 30.w,
                                        bottom: 10.h),
                                    child: Text(
                                      state.message.toString(),
                                      textAlign: TextAlign.center,
                                      style: TextStyle(color: cGrayColor1),
                                    )),
                                CupertinoButton(
                                    child: Text(
                                      LocaleKeys.refresh.tr(),
                                      style: TextStyle(color: cGrayColor1,fontSize: 20.sp),
                                    ),
                                    color: cGrayColor1.withAlpha(80),
                                    onPressed: () {
                                      BlocProvider.of<TestCubit>(context)
                                          .loadTest(widget.id);
                                    }),
                                SizedBox(
                                  height: 20.h,
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              } else {
                return SizedBox();

              }
            },
          )),
    );
  }
}
