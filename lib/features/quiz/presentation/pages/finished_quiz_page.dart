import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/quiz/data/model/test_card_finished_model.dart';
import 'package:yetakchi/features/quiz/presentation/cubits/test_card_finished_cubit/test_card_finished_cubit.dart';
import 'package:yetakchi/features/quiz/presentation/pages/test_result_review_page.dart';
import 'package:yetakchi/features/quiz/presentation/widgets/test_finished_container.dart';
import 'package:yetakchi/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class FinishedQuizPage extends StatefulWidget {
  const FinishedQuizPage({super.key});

  static Widget screen() {
    return BlocProvider(
      create: (context) => di<TestCardFinishedCubit>(),
      child: FinishedQuizPage(),
    );
  }

  @override
  State<FinishedQuizPage> createState() => _FinishedQuizPageState();
}

class _FinishedQuizPageState extends State<FinishedQuizPage> {
  final PagingController<int, TestCardFinished> _pagingController =
      PagingController(firstPageKey: 1);
  List<TestCardFinished> list = [];
  bool refresh = false;

  handleRefresh(bool refresh) {
    this.refresh = refresh;
    _pagingController.refresh();
  }
  @override
  void initState() {
    super.initState();

    if (refresh) {
      ///First event when online
      _pagingController.notifyPageRequestListeners(1);
    } else {
      ///First event when offline
      BlocProvider.of<TestCardFinishedCubit>(context)
          .getFinishedTest(pageKey: 1, refresh: refresh);
    }
    _pagingController.addPageRequestListener((pageKey) {
      print(pageKey);
      if (pageKey == 1) {
        BlocProvider.of<TestCardFinishedCubit>(context)
            .getFinishedTest(pageKey: pageKey, refresh: true);
      } else {
        BlocProvider.of<TestCardFinishedCubit>(context)
            .getFinishedTest(pageKey: pageKey, refresh: false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TestCardFinishedCubit, TestCardFinishedState>(
      listener: (context, state) {
        if (state.status == TestCardFinishedStatus.success) {
          list = state.testCardFinishedModel?.testCardFinished ?? [];
          print(list.length);
          print(state.testCardFinishedModel?.testCardFinished?.length);
          int currentPage = state.testCardFinishedModel?.page ?? 1;
          bool isLastPage =
              state.testCardFinishedModel?.totalPages == currentPage;
          int next = currentPage + 1;
          if (isLastPage) {
            _pagingController.appendLastPage(list);
          } else {
            _pagingController.appendPage(list, next);
          }
        }
      },
      builder: (context, state) {
        if (state.status == TestCardFinishedStatus.loading ||
            state.status == TestCardFinishedStatus.initial) {
          return Center(
            child: CupertinoActivityIndicator(
              color: Theme.of(context).primaryColor,
              radius: 30.r,
            ),
          );
        } else if (state.status == TestCardFinishedStatus.success) {
          return RefreshIndicator(
            onRefresh: () async {
              handleRefresh(true);
            },
            child: PagedListView(
                physics: BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                padding: EdgeInsets.only(top: 4.h, bottom: 30.h),
                pagingController: _pagingController,
                builderDelegate: PagedChildBuilderDelegate<TestCardFinished>(
                    noItemsFoundIndicatorBuilder: (context) {
                  return EmptyListWidget(
                    onTap: () {
                      handleRefresh(true);
                    },
                    title: LocaleKeys.test_empty.tr(),
                  );
                }, itemBuilder: (context, item, index) {
                  return TestFinishedContainer(
                    onTap: (TestCardFinished) {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => TestResultReviewPage.screen(
                                  title: item.quiz?.title ?? "Empty",
                                  id: item.id ?? "")));
                    },
                    testCardFinished: item,
                  );
                })),
          );
        } else if (state.status == TestCardFinishedStatus.failure) {
          return Container(
            color: Theme.of(context).cardTheme.color,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ClipRect(
                    child: Container(
                      height: 300.h,
                      child: Column(
                        children: [
                          SizedBox(
                            height: 20.h,
                          ),
                          Expanded(
                              child: SvgPicture.asset(
                            Assets.iconsWarning,
                            height: 140.h,
                          )),
                          Padding(
                              padding: EdgeInsets.only(
                                  top: 10.h,
                                  left: 30.w,
                                  right: 30.w,
                                  bottom: 10.h),
                              child: Text(
                                state.message.toString(),
                                textAlign: TextAlign.center,
                                style: TextStyle(color: cGrayColor1),
                              )),
                          CupertinoButton(
                              child: Text(
                                LocaleKeys.refresh.tr(),
                                style: TextStyle(color: cGrayColor1),
                              ),
                              color: cGrayColor1.withAlpha(80),
                              onPressed: () {
                                handleRefresh(true);
                              }),
                          SizedBox(
                            height: 20.h,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          return SizedBox();
        }
      },
    );
  }
}
