import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/quiz/data/model/test_card_new_model.dart';
import 'package:yetakchi/features/quiz/presentation/cubits/test_card_new_cubit/test_card_new_cubit.dart';
import 'package:yetakchi/features/quiz/presentation/pages/testing_page.dart';
import 'package:yetakchi/features/quiz/presentation/widgets/test_new_container.dart';
import 'package:yetakchi/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class NewQuizPage extends StatefulWidget {
  const NewQuizPage({super.key});

  static Widget screen() {
    return BlocProvider(
      create: (context) => di<TestCardNewCubit>(),
      child: NewQuizPage(),
    );
  }

  @override
  State<NewQuizPage> createState() => _NewQuizPageState();
}

class _NewQuizPageState extends State<NewQuizPage> {
  final PagingController<int, TestCardNew> _pagingController =
      PagingController(firstPageKey: 1);
  List<TestCardNew> list = [];

  bool refresh = false;

  handleRefresh(bool refresh) {
    this.refresh = refresh;
    _pagingController.refresh();
  }

  @override
  void initState() {
    super.initState();

    if (refresh) {
      ///First event when online
      _pagingController.notifyPageRequestListeners(1);
    } else {
      ///First event when offline
      BlocProvider.of<TestCardNewCubit>(context)
          .getNewTest(pageKey: 1, refresh: refresh);
    }
    _pagingController.addPageRequestListener((pageKey) {
      print(pageKey);
      if (pageKey == 1) {
        BlocProvider.of<TestCardNewCubit>(context)
            .getNewTest(pageKey: pageKey, refresh: true);
      } else {
        BlocProvider.of<TestCardNewCubit>(context)
            .getNewTest(pageKey: pageKey, refresh: false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TestCardNewCubit, TestCardNewState>(
      listener: (context, state) {
        if (state.status == TestCardNewStatus.success) {
          list = state.testCardNewModel?.testCardNew ?? [];
          int currentPage = state.testCardNewModel?.page ?? 1;
          bool isLastPage = state.testCardNewModel?.totalPages == currentPage;
          int next = currentPage + 1;
          if (isLastPage) {
            _pagingController.appendLastPage(list);
          } else {
            _pagingController.appendPage(list, next);
          }
        }
      },
      builder: (context, state) {
        if (state.status == TestCardNewStatus.loading ||
            state.status == TestCardNewStatus.initial) {
          return Center(
            child: CupertinoActivityIndicator(
              color: Theme.of(context).primaryColor,
              radius: 30.r,
            ),
          );
        } else if (state.status == TestCardNewStatus.success) {
          return RefreshIndicator(
            onRefresh: () async {
              handleRefresh(true);
            },
            child: PagedListView(
                physics: BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                padding: EdgeInsets.only(top: 4.h, bottom: 30.h),
                pagingController: _pagingController,
                builderDelegate: PagedChildBuilderDelegate<TestCardNew>(
                    noItemsFoundIndicatorBuilder: (context) {
                  return EmptyListWidget(
                    onTap: () {
                      handleRefresh(true);
                    },
                    title: LocaleKeys.test_empty.tr(),
                  );
                }, itemBuilder: (context, item, index) {
                  return TestNewContainer(
                    onTap: (TestCardNew testCardNew) {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => TestingPage.screen(
                                  id: testCardNew.id ?? "",
                                  title: item.title ?? "Empty"))).then((value) {
                        print(value);
                        if (value == true) {
                          handleRefresh(true);
                        }
                      });
                    },
                    testCardNew: item,
                  );
                })),
          );
        } else if (state.status == TestCardNewStatus.failure) {
          return Container(
            color: Theme.of(context).cardTheme.color,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ClipRect(
                    child: Container(
                      height: 300.h,
                      child: Column(
                        children: [
                          SizedBox(
                            height: 20.h,
                          ),
                          Expanded(
                              child: SvgPicture.asset(
                            Assets.iconsWarning,
                            height: 140.h,
                          )),
                          Padding(
                              padding: EdgeInsets.only(
                                  top: 10.h,
                                  left: 30.w,
                                  right: 30.w,
                                  bottom: 10.h),
                              child: Text(
                                state.message.toString(),
                                textAlign: TextAlign.center,
                                style: TextStyle(color: cGrayColor1),
                              )),
                          CupertinoButton(
                              child: Text(
                                LocaleKeys.refresh.tr(),
                                style: TextStyle(color: cGrayColor1),
                              ),
                              color: cGrayColor1.withAlpha(80),
                              onPressed: () {
                                handleRefresh(true);
                              }),
                          SizedBox(
                            height: 20.h,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          return SizedBox();
        }
      },
    );
  }
}
