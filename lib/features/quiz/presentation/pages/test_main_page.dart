import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/core/widgets/header_widget.dart';
import 'package:yetakchi/features/quiz/presentation/pages/finished_quiz_page.dart';
import 'package:yetakchi/features/quiz/presentation/pages/new_quiz_page.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class TestMainPage extends StatefulWidget {
  const TestMainPage({super.key});

  @override
  State<TestMainPage> createState() => _TestMainPageState();
}

class _TestMainPageState extends State<TestMainPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(vsync: this, initialIndex: 0, length: 2);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeaderWidget(
        title: LocaleKeys.tests.tr(),
        onBackTap: () {
          Navigator.pop(context);
        },
        isBackVisible: true,
      ),
      body: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
            padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 8.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(40.r),
              color: Theme.of(context).cardTheme.color,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 4,
                  blurRadius: 5,
                  offset: Offset(0, 4), // changes position of shadow
                ),
              ],
            ),
            child: TabBar(
              dividerColor: Colors.transparent,
              indicatorSize: TabBarIndicatorSize.tab,
              controller: _tabController,
              indicatorColor: Colors.transparent,
              unselectedLabelColor:
                  Theme.of(context).textTheme.bodySmall?.color,
              labelColor: cWhiteColor,
              indicator: BoxDecoration(
                  color: cFirstColor,
                  borderRadius: BorderRadius.circular(40.r)),
              tabs: [
                Tab(
                  height: context.isTablet ? 50.h : 40.h,
                  child: Text(
                    LocaleKeys.new_word.tr(),
                    style:
                        TextStyle(fontWeight: FontWeight.w500, fontSize: 16.sp),
                  ),
                ),
                Tab(
                  height: context.isTablet ? 50.h : 40.h,
                  child: Text(
                    LocaleKeys.finished.tr(),
                    style:
                        TextStyle(fontWeight: FontWeight.w500, fontSize: 16.sp),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
              child: TabBarView(
                  controller: _tabController,
                  children: [NewQuizPage.screen(), FinishedQuizPage.screen()]))
        ],
      ),
    );
  }
}
