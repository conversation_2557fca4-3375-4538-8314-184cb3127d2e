import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/features/quiz/data/model/test_result.dart';
import 'package:yetakchi/features/quiz/data/model/test_send.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'test_result_state.dart';

class TestResultCubit extends Cubit<TestResultState> {
  final Dio dio;
  final NetworkInfo networkInfo;

  TestResultCubit({required this.dio, required this.networkInfo})
      : super(TestResultState.initial());

  Future<void> checkResult({required TestSend testSend}) async {
    emit(TestResultState(status: TestResultStatus.loading));
    if (await networkInfo.isConnected) {
      try {
        var response = await dio.post(sendAnswerPath, data: testSend.toJson());
        if (response.statusCode == 201 || response.statusCode == 200) {
          emit(TestResultState(
              status: TestResultStatus.success,
              testResult: TestResult.fromJson(response.data)));
        } else {
          emit(TestResultState(
              status: TestResultStatus.failure, message: "Server Error"));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            try {
              emit(TestResultState(
                  status: TestResultStatus.failure,
                  message:
                      "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}"));
            } catch (e) {
              emit(TestResultState(
                  status: TestResultStatus.failure,
                  message: "Parsing error of RAW data"));
            }
          }
        }
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(TestResultState(
              status: TestResultStatus.failure, message: "Connection timeout"));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          emit(TestResultState(
              status: TestResultStatus.failure, message: "Receive timeout"));
        }

        if (e.type == DioExceptionType.unknown) {
          emit(TestResultState(
              status: TestResultStatus.failure,
              message: "Something went wrong"));
        }

        if (e.response?.statusCode == 400) {
          try {
            emit(TestResultState(
                status: TestResultStatus.failure,
                message: "${e.response?.data['message']}"));
          } catch (e) {
            emit(TestResultState(
                status: TestResultStatus.failure,
                message: "Something went wrong"));
          }
        }
      }
    } else {
      emit(TestResultState(
          status: TestResultStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
