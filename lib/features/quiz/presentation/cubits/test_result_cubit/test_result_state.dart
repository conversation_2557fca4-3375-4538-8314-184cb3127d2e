part of 'test_result_cubit.dart';

enum TestResultStatus { initial, loading, success, failure }

class TestResultState {
  final TestResultStatus status;
  final TestResult? testResult;
  final String? message;

  TestResultState({required this.status, this.testResult, this.message});

  static TestResultState initial() =>
      TestResultState(status: TestResultStatus.initial);

  TestResultState copyWith(
      TestResultStatus? status, TestResult? testResult, String? message) {
    return TestResultState(
        status: status ?? this.status,
        testResult: testResult ?? this.testResult,
        message: message ?? this.message);
  }
}
