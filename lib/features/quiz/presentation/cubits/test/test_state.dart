part of 'test_cubit.dart';

enum TestStatus { initial, loading, success, failure }

class TestState {
  final TestStatus? status;
  final TestModel? testModel;
  final String? message;

  TestState({required this.status, this.testModel, this.message});

  static TestState initial() => TestState(status: TestStatus.initial);

  TestState copyWith(
      TestStatus? status, TestModel? testCardNewModel, String? message) {
    return TestState(
        status: status ?? this.status,
        testModel: testCardNewModel ?? this.testModel,
        message: message ?? this.message);
  }
}
