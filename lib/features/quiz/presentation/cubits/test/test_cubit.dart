import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/features/quiz/data/model/test_model.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'test_state.dart';

class TestCubit extends Cubit<TestState> {
  final Dio dio;
  final NetworkInfo networkInfo;

  TestCubit({required this.dio, required this.networkInfo})
      : super(TestState.initial());

  loadTest(String id) async {
    if (await networkInfo.isConnected) {
      emit(TestState(status: TestStatus.loading));
      try {
        var response = await dio.get("${getTestsPath}$id");
        if (response.statusCode == 200) {
          TestModel testModel = TestModel.fromJson(response.data);
          emit(TestState(status: TestStatus.success, testModel: testModel));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            try {
              emit(TestState(
                  status: TestStatus.failure,
                  message:
                  "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}"));
            } catch (e) {
              emit(TestState(
                  status: TestStatus.failure,
                  message: "Parsing error of RAW data"));
            }
          }
        }
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(TestState(
              status: TestStatus.failure,
              message: "Connection timeout"));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          emit(TestState(
              status: TestStatus.failure, message: "Receive timeout"));
        }

        if (e.type == DioExceptionType.unknown) {
          emit(TestState(
              status: TestStatus.failure,
              message: "Something went wrong"));
        }

        if (e.response?.statusCode == 400) {
          try {
            emit(TestState(
                status: TestStatus.failure,
                message: "${e.response?.data['message']}"));
          } catch (e) {
            emit(TestState(
                status: TestStatus.failure,
                message: "Something went wrong"));
          }
        }
      } catch (e) {
        emit(TestState(
            status: TestStatus.failure,
            message:
            "${LocaleKeys.error_download_data.tr()}.. ${LocaleKeys.server_form_error.tr()}"));

      }
    } else {
      emit(TestState(
          status: TestStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
