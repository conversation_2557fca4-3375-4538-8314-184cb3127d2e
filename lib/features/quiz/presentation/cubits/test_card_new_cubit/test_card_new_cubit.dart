import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:get_storage/get_storage.dart';
import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/features/quiz/data/model/test_card_new_model.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'test_card_new_state.dart';

class TestCardNewCubit extends Cubit<TestCardNewState> {
  final Dio dio;
  final NetworkInfo networkInfo;
  final IsarService isarService;
  final GetStorage gs;

  TestCardNewCubit(
      {required this.dio,
      required this.networkInfo,
      required this.isarService,
      required this.gs})
      : super(TestCardNewState.initial());

  void getNewTest({required int pageKey, bool refresh = false}) async {
    if (refresh) {
      emit(TestCardNewState(status: TestCardNewStatus.loading));
    }
    if (await networkInfo.isConnected) {
      try {
        var response = await dio
            .post(getNewQuizPath, data: {"page": pageKey, "limit": 10});
        if (response.statusCode == 200) {
          TestCardNewModel testCardNewModel =
              TestCardNewModel.fromJson(response.data);
          gs.write(TEST_COUNT, testCardNewModel.totalDocs);
          emit(TestCardNewState(
              status: TestCardNewStatus.success,
              testCardNewModel: testCardNewModel));
          if (refresh) {
            await isarService.isar.writeTxn(() async {
              isarService.isar.testCardNews.clear();
              isarService.isar.testCardNews
                  .putAll(testCardNewModel.testCardNew ?? []);
            });
          } else {
            await isarService.isar.writeTxn(() async {
              isarService.isar.testCardNews
                  .putAll(testCardNewModel.testCardNew ?? []);
            });
          }
        } else {
          emit(TestCardNewState(
              status: TestCardNewStatus.failure, message: "Server Error"));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            try {
              emit(TestCardNewState(
                  status: TestCardNewStatus.failure,
                  message:
                      "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}"));
            } catch (e) {
              emit(TestCardNewState(
                  status: TestCardNewStatus.failure,
                  message: "Parsing error of RAW data"));
            }
          }
        }
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(TestCardNewState(
              status: TestCardNewStatus.failure,
              message: "Connection timeout"));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          emit(TestCardNewState(
              status: TestCardNewStatus.failure, message: "Receive timeout"));
        }

        if (e.type == DioExceptionType.unknown) {
          emit(TestCardNewState(
              status: TestCardNewStatus.failure,
              message: "Something went wrong"));
        }

        if (e.response?.statusCode == 400) {
          try {
            emit(TestCardNewState(
                status: TestCardNewStatus.failure,
                message: "${e.response?.data['message']}"));
          } catch (e) {
            emit(TestCardNewState(
                status: TestCardNewStatus.failure,
                message: "Something went wrong"));
          }
        }
      } catch (e) {
        emit(TestCardNewState(
            status: TestCardNewStatus.failure,
            message:
                "${LocaleKeys.error_download_data.tr()}.. ${LocaleKeys.server_form_error.tr()}"));
      }
    } else {
      List<TestCardNew> list =
          await isarService.isar.testCardNews.where().findAll();
      TestCardNewModel testCardNewModel =
          TestCardNewModel(page: 1, totalPages: 1, testCardNew: list);
      emit(TestCardNewState(
          status: TestCardNewStatus.success,
          testCardNewModel: testCardNewModel));
    }
  }
}
