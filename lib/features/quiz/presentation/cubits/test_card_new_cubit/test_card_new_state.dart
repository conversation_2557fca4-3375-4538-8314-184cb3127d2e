part of 'test_card_new_cubit.dart';

enum TestCardNewStatus { initial, loading, success, failure }

class TestCardNewState {
  final TestCardNewStatus? status;
  final TestCardNewModel? testCardNewModel;
  final String? message;

  TestCardNewState({required this.status, this.testCardNewModel, this.message});

  static TestCardNewState initial() =>
      TestCardNewState(status: TestCardNewStatus.initial);

  TestCardNewState copyWith(TestCardNewStatus? status,
      TestCardNewModel? testCardNewModel, String? message) {
    return TestCardNewState(
        status: status ?? this.status,
        testCardNewModel: testCardNewModel ?? this.testCardNewModel,
        message: message ?? this.message);
  }
}
