part of 'test_result_review_cubit.dart';

enum TestResultReviewStatus { initial, loading, success, failure }

class TestResultReviewState {
  final TestResultReviewStatus status;
  final TestResultReview? testResultReview;
  final String? message;

  TestResultReviewState(
      {required this.status, this.testResultReview, this.message});

  static TestResultReviewState initial() =>
      TestResultReviewState(status: TestResultReviewStatus.initial);

  TestResultReviewState copyWith(TestResultReviewStatus? status,
      TestResultReview? testResult, String? message) {
    return TestResultReviewState(
        status: status ?? this.status,
        testResultReview: testResult ?? this.testResultReview,
        message: message ?? this.message);
  }
}