import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/features/quiz/data/model/test_result_review.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'test_result_review_state.dart';

class TestResultReviewCubit extends Cubit<TestResultReviewState> {
  final Dio dio;
  final NetworkInfo networkInfo;

  TestResultReviewCubit({required this.dio, required this.networkInfo})
      : super(TestResultReviewState.initial());

  Future<void> reviewResult({required String id}) async {
    emit(TestResultReviewState(status: TestResultReviewStatus.loading));
    if (await networkInfo.isConnected) {
      try {
        var response = await dio.get("$sendAnswerPath/$id");
        if (response.statusCode == 201 || response.statusCode == 200) {
          emit(TestResultReviewState(
              status: TestResultReviewStatus.success,
              testResultReview: TestResultReview.fromJson(response.data)));
        } else {
          emit(TestResultReviewState(
              status: TestResultReviewStatus.failure, message: "Server Error"));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            try {
              emit(TestResultReviewState(
                  status: TestResultReviewStatus.failure,
                  message:
                      "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}"));
            } catch (e) {
              emit(TestResultReviewState(
                  status: TestResultReviewStatus.failure,
                  message: "Parsing error of RAW data"));
            }
          }
        }
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(TestResultReviewState(
              status: TestResultReviewStatus.failure,
              message: "Connection timeout"));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          emit(TestResultReviewState(
              status: TestResultReviewStatus.failure,
              message: "Receive timeout"));
        }

        if (e.type == DioExceptionType.unknown) {
          emit(TestResultReviewState(
              status: TestResultReviewStatus.failure,
              message: "Something went wrong"));
        }

        if (e.response?.statusCode == 400) {
          try {
            emit(TestResultReviewState(
                status: TestResultReviewStatus.failure,
                message: "${e.response?.data['message']}"));
          } catch (e) {
            emit(TestResultReviewState(
                status: TestResultReviewStatus.failure,
                message: "Something went wrong"));
          }
        }
      }
    } else {
      emit(TestResultReviewState(
          status: TestResultReviewStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
