part of 'test_card_finished_cubit.dart';

enum TestCardFinishedStatus { initial, loading, success, failure }

class TestCardFinishedState {
  final TestCardFinishedStatus? status;
  final TestCardFinishedModel? testCardFinishedModel;
  final String? message;

  TestCardFinishedState(
      {required this.status, this.testCardFinishedModel, this.message});

  static TestCardFinishedState initial() =>
      TestCardFinishedState(status: TestCardFinishedStatus.initial);

  TestCardFinishedState copyWith(TestCardFinishedStatus? status,
      TestCardFinishedModel? testCardFinishedModel, String? message) {
    return TestCardFinishedState(
        status: status ?? this.status,
        testCardFinishedModel:
            testCardFinishedModel ?? this.testCardFinishedModel,
        message: message ?? this.message);
  }
}
