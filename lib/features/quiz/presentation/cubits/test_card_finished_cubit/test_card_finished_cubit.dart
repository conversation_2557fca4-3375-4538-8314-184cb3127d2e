import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/core/utils/api_path.dart';
import 'package:yetakchi/features/quiz/data/model/test_card_finished_model.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

part 'test_card_finished_state.dart';

class TestCardFinishedCubit extends Cubit<TestCardFinishedState> {
  final Dio dio;
  final NetworkInfo networkInfo;
  final IsarService isarService;

  TestCardFinishedCubit(
      {required this.dio, required this.networkInfo, required this.isarService})
      : super(TestCardFinishedState.initial());

  void getFinishedTest(
      {required int pageKey, bool refresh = false, bool reset = false}) async {
    if (refresh) {
      emit(TestCardFinishedState(status: TestCardFinishedStatus.loading));
    }
    if (await networkInfo.isConnected) {
      try {
        var response = await dio
            .post(getFinishedQuizPath, data: {"page": pageKey, "limit": 10});
        if (response.statusCode == 200) {
          TestCardFinishedModel testCardFinishedModel =
              TestCardFinishedModel.fromJson(response.data);
          emit(TestCardFinishedState(
              status: TestCardFinishedStatus.success,
              testCardFinishedModel: testCardFinishedModel));
          if (refresh) {
            await isarService.isar.writeTxn(() async {
              isarService.isar.testCardFinisheds.clear();
              isarService.isar.testCardFinisheds
                  .putAll(testCardFinishedModel.testCardFinished ?? []);
            });
          } else {
            await isarService.isar.writeTxn(() async {
              isarService.isar.testCardFinisheds
                  .putAll(testCardFinishedModel.testCardFinished ?? []);
            });
          }
        } else {
          emit(TestCardFinishedState(
              status: TestCardFinishedStatus.failure, message: "Server Error"));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          if (e.response != null) {
            try {
              emit(TestCardFinishedState(
                  status: TestCardFinishedStatus.failure,
                  message:
                      "${LocaleKeys.error1.tr()}: ${e.response?.data['message']}"));
            } catch (e) {
              emit(TestCardFinishedState(
                  status: TestCardFinishedStatus.failure,
                  message: "Parsing error of RAW data"));
            }
          }
        }
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(TestCardFinishedState(
              status: TestCardFinishedStatus.failure,
              message: "Connection timeout"));
        }

        if (e.type == DioExceptionType.receiveTimeout) {
          emit(TestCardFinishedState(
              status: TestCardFinishedStatus.failure,
              message: "Receive timeout"));
        }

        if (e.type == DioExceptionType.unknown) {
          emit(TestCardFinishedState(
              status: TestCardFinishedStatus.failure,
              message: "Something went wrong"));
        }

        if (e.response?.statusCode == 400) {
          try {
            emit(TestCardFinishedState(
                status: TestCardFinishedStatus.failure,
                message: "${e.response?.data['message']}"));
          } catch (e) {
            emit(TestCardFinishedState(
                status: TestCardFinishedStatus.failure,
                message: "Something went wrong"));
          }
        }
      } catch (e) {
        emit(TestCardFinishedState(
            status: TestCardFinishedStatus.failure,
            message:
                "${LocaleKeys.error_download_data.tr()}.. ${LocaleKeys.server_form_error.tr()}"));
      }
    } else {
      List<TestCardFinished> list =
          await isarService.isar.testCardFinisheds.where().findAll();
      TestCardFinishedModel testCardFinishedModel =
          TestCardFinishedModel(page: 1, totalPages: 1, testCardFinished: list);
      emit(TestCardFinishedState(
          status: TestCardFinishedStatus.success,
          testCardFinishedModel: testCardFinishedModel));
    }
  }
}
