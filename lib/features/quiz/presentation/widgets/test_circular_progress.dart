import 'package:awesome_circular_chart/awesome_circular_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

class TestCircularProgress extends StatelessWidget {
  const TestCircularProgress({required this.total,
    required this.done,
    this.size = 100,
    this.holeRadius = 22,
    this.bookQuiz = false,
    this.isPassed = false,
    this.fontSize = 14,
    super.key});

  final double total;
  final double done;
  final double size;
  final double holeRadius;
  final bool bookQuiz;
  final bool isPassed;
  final double fontSize;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(),
      child: AnimatedCircularChart(
        edgeStyle: SegmentEdgeStyle.round,
        size: Size(size.h, size.h),
        holeRadius: holeRadius.r,
        initialChartData: <CircularStackEntry>[
          new CircularStackEntry(
            <CircularSegmentEntry>[
              new CircularSegmentEntry(
                done,
                isDark()
                    ? bookQuiz
                    ? isPassed
                    ? cGreenColor
                    : cRedColor
                    : cPrimaryTextDark
                    : bookQuiz
                    ? isPassed
                    ? cGreenColor
                    : cRedColor
                    : cFirstColor,
                rankKey: 'completed',
              ),
              new CircularSegmentEntry(
                total - done,
                isDark()
                    ? cPrimaryTextDark.withAlpha(30)
                    : cFirstColor.withAlpha(30),
                rankKey: 'remaining',
              ),
            ],
            rankKey: 'progress',
          ),
        ],
        chartType: CircularChartType.Radial,
        percentageValues: false,
        holeLabel: "${(done).toInt()}%",
        labelStyle: new TextStyle(
          color: bookQuiz
              ? isPassed
              ? cGreenColor
              : cRedColor : Theme
              .of(context)
              .primaryColor,
          fontWeight: FontWeight.w600,
          fontSize: fontSize.sp,
        ),
      ),
    );
  }
}
