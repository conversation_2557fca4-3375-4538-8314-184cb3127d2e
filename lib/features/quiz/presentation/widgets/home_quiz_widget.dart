import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class HomeQuizWidget extends StatefulWidget {
  final VoidCallback onTap;

  const HomeQuizWidget({super.key, required this.onTap});

  @override
  State<HomeQuizWidget> createState() => _HomeQuizWidgetState();
}

class _HomeQuizWidgetState extends State<HomeQuizWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      width: MediaQuery.of(context).size.width,
      height: 120.h,
      decoration: BoxDecoration(
        boxShadow: [boxShadow60],
        color: cFirstColor,
        borderRadius: BorderRadius.circular(30.r),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30.r),
        child: Stack(
          fit: StackFit.expand,
          children: [
            SvgPicture.asset(
              Assets.iconsQuizBackground,
              fit: BoxFit.fitWidth,
            ),
            Positioned(
              top: 10.h,
              bottom: 10.h,
              left: 20.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Container(
                    width: 160.w,
                    child: Text(
                      LocaleKeys.has_test.tr(),
                      style: TextStyle(
                          color: cWhiteColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12.sp),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  MaterialButton(
                    onPressed: () {
                      widget.onTap();
                    },
                    minWidth: 100.w,
                    height: 30.h,
                    child: Text(
                      LocaleKeys.see.tr(),
                      style: TextStyle(
                          color: cFirstColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12.sp),
                    ),
                    color: cWhiteColor,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(cRadius22.r)),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
