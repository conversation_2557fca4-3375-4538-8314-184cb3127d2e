import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/features/quiz/data/model/test_card_new_model.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class TestNewContainer extends StatelessWidget {
  final Function(TestCardNew) onTap;
  final TestCardNew testCardNew;

  const TestNewContainer(
      {super.key, required this.onTap, required this.testCardNew});

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      onTap: () {
        onTap(testCardNew);
      },
      child: Container(
        height: 110.h,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(cRadius22.r),
            color: Theme.of(context).cardTheme.color,
            boxShadow: [boxShadow20]),
        margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 14.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Flexible(
              child: Text(
                testCardNew.title ?? "",
                style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).primaryColor),
                overflow: TextOverflow.ellipsis,
                maxLines: 3,
              ),
            ),
            Text(
              "${LocaleKeys.testCount.tr()}: ${testCardNew.questions}",
              style: TextStyle(
                  fontWeight: FontWeight.normal,
                  color: Theme.of(context).primaryColor,
                  fontSize: 16.sp),
            )
          ],
        ),
      ),
    );
  }

}
