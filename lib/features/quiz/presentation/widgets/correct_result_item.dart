import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/features/quiz/data/model/test_result.dart';
import 'package:yetakchi/generated/assets.dart';

class CorrectResultItem extends StatelessWidget {
  final ResultQuestions? questions;

  const CorrectResultItem({super.key, required this.questions});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.w),
      margin: EdgeInsets.symmetric(vertical: 10.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            width: 1,
            color: cGreenColor,
          ),
          color: cGreenColor.withAlpha(20)),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: Text(questions?.question?.title ?? "Empty",style: TextStyle(fontSize: 16.sp,fontWeight: FontWeight.w600),),
              ),
              SvgPicture.asset(Assets.iconsCorrect,width: 20.w,height: 20.w,)
            ],
          ),
          SizedBox(
            height: 8.h,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Divider(
              thickness: 2,
              color: cWhiteColor,
            ),
          ),
          SizedBox(
            height: 8.h,
          ),
          Container(
            width: MediaQuery.of(context).size.width,
            padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 10.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.r),  color:isDark()?cCardDarkColor: cWhiteColor),
            child: Text(
              questions?.correctAnswer?.title ?? "Empty",
              style: TextStyle(
                  fontSize: 14.sp,
                  color: cGreenColor,),
            ),
          )
        ],
      ),
    );
  }
}
