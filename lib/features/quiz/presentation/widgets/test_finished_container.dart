import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/features/quiz/data/model/test_card_finished_model.dart';
import 'package:yetakchi/features/quiz/presentation/widgets/test_circular_progress.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class TestFinishedContainer extends StatelessWidget {
  final Function(TestCardFinished) onTap;
  final TestCardFinished testCardFinished;

  const TestFinishedContainer(
      {super.key, required this.onTap, required this.testCardFinished});

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      onTap: () {
        onTap(testCardFinished);
      },
      child: Container(
        height: 130.h,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: Theme.of(context).cardTheme.color,
            boxShadow: [boxShadow20]),
        margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 14.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 2,
              child: Container(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Flexible(
                      child: Text(
                        testCardFinished.quiz?.title ?? "",
                        style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColor),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ),
                    Text(
                         "${LocaleKeys.testCount.tr()}: ${testCardFinished.totalQuestions}",
                      style: TextStyle(
                          fontWeight: FontWeight.normal,
                          color: Theme.of(context).primaryColor,fontSize: 16.sp),
                    ),
                    Text(
                      "${LocaleKeys.correct_test_count.tr()}: ${testCardFinished.trueAnswers}",
                      style: TextStyle(
                          fontWeight: FontWeight.normal,
                          color: Theme.of(context).primaryColor,fontSize: 16.sp),
                    )
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 1,
              child: TestCircularProgress(
                total: 100,
                done: testCardFinished.trueAnswersPercent?.toDouble() ?? 0,
              ),
            )
          ],
        ),
      ),
    );
  }
}
