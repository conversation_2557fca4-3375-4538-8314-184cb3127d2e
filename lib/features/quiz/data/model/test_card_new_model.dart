import 'package:isar/isar.dart';

part 'test_card_new_model.g.dart';
class TestCardNewModel {
  TestCardNewModel({
    this.testCardNew,
    this.totalDocs,
    this.limit,
    this.totalPages,
    this.page,
    this.pagingCounter,
    this.hasPrevPage,
    this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  TestCardNewModel.fromJson(dynamic json) {
    if (json['docs'] != null) {
      testCardNew = [];
      json['docs'].forEach((v) {
        testCardNew?.add(TestCardNew.fromJson(v));
      });
    }
    totalDocs = json['totalDocs'];
    limit = json['limit'];
    totalPages = json['totalPages'];
    page = json['page'];
    pagingCounter = json['pagingCounter'];
    hasPrevPage = json['hasPrevPage'];
    hasNextPage = json['hasNextPage'];
    prevPage = json['prevPage'];
    nextPage = json['nextPage'];
  }

  List<TestCardNew>? testCardNew;
  int? totalDocs;
  int? limit;
  int? totalPages;
  int? page;
  int? pagingCounter;
  bool? hasPrevPage;
  bool? hasNextPage;
  dynamic prevPage;
  dynamic nextPage;
}

@collection
@Name("TestCardNew")
class TestCardNew {
  TestCardNew({
    this.id,
    this.title,
    this.questions,
  });

  TestCardNew.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
    questions = json['questions'];
  }

  Id localId = Isar.autoIncrement;
  String? id;
  String? title;
  int? questions;
}
