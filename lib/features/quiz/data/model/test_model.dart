class TestModel {
  TestModel({
      this.id, 
      this.title, 
      this.questions,});

  TestModel.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
    if (json['questions'] != null) {
      questions = [];
      json['questions'].forEach((v) {
        questions?.add(Questions.fromJson(v));
      });
    }
  }
  String? id;
  String? title;
  List<Questions>? questions;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['title'] = title;
    if (questions != null) {
      map['questions'] = questions?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class Questions {
  Questions({
      this.title, 
      this.answerOptions, 
      this.id,});

  Questions.fromJson(dynamic json) {
    title = json['title'];
    if (json['answerOptions'] != null) {
      answerOptions = [];
      json['answerOptions'].forEach((v) {
        answerOptions?.add(AnswerOptions.fromJson(v));
      });
    }
    id = json['_id'];
  }
  String? title;
  List<AnswerOptions>? answerOptions;
  String? id;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['title'] = title;
    if (answerOptions != null) {
      map['answerOptions'] = answerOptions?.map((v) => v.toJson()).toList();
    }
    map['_id'] = id;
    return map;
  }

}

class AnswerOptions {
  AnswerOptions({
      this.title, 
      this.id,});

  AnswerOptions.fromJson(dynamic json) {
    title = json['title'];
    id = json['_id'];
  }
  String? title;
  String? id;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['title'] = title;
    map['_id'] = id;
    return map;
  }

}