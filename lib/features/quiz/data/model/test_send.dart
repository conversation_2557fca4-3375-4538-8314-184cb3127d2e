class TestSend {
  TestSend({
    this.quiz,
    this.questions,
  });

  TestSend.fromJson(dynamic json) {
    quiz = json['quiz'];
    if (json['questions'] != null) {
      questions = [];
      json['questions'].forEach((v) {
        questions?.add(SendQuestions.fromJson(v));
      });
    }
  }

  String? quiz;
  List<SendQuestions>? questions;


  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['quiz'] = quiz;
    if (questions != null) {
      map['questions'] = questions?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class SendQuestions {
  SendQuestions({
    this.question,
    this.userAnswer,
  });

  SendQuestions.fromJson(dynamic json) {
    question = json['question'];
    userAnswer = json['userAnswer'];
  }

  String? question;
  String? userAnswer;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['question'] = question;
    map['userAnswer'] = userAnswer;
    return map;
  }
}
