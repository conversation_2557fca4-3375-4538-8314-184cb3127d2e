import 'package:isar/isar.dart';

part 'test_card_finished_model.g.dart';

class TestCardFinishedModel {
  TestCardFinishedModel({
    this.testCardFinished,
    this.totalDocs,
    this.limit,
    this.totalPages,
    this.page,
    this.pagingCounter,
    this.hasPrevPage,
    this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  TestCardFinishedModel.fromJson(dynamic json) {
    if (json['docs'] != null) {
      testCardFinished = [];
      json['docs'].forEach((v) {
        testCardFinished?.add(TestCardFinished.fromJson(v));
      });
    }
    totalDocs = json['totalDocs'];
    limit = json['limit'];
    totalPages = json['totalPages'];
    page = json['page'];
    pagingCounter = json['pagingCounter'];
    hasPrevPage = json['hasPrevPage'];
    hasNextPage = json['hasNextPage'];
    prevPage = json['prevPage'];
    nextPage = json['nextPage'];
  }

  List<TestCardFinished>? testCardFinished;
  int? totalDocs;
  int? limit;
  int? totalPages;
  int? page;
  int? pagingCounter;
  bool? hasPrevPage;
  bool? hasNextPage;
  dynamic prevPage;
  dynamic nextPage;
}

@collection
@Name("TestCardFinished")
class TestCardFinished {
  TestCardFinished({
    this.id,
    this.quiz,
    this.trueAnswers,
    this.trueAnswersPercent,
    this.totalQuestions,
  });

  TestCardFinished.fromJson(dynamic json) {
    id = json['_id'];
    quiz = json['quiz'] != null ? Quiz.fromJson(json['quiz']) : null;
    trueAnswers = json['trueAnswers'];
    trueAnswersPercent = json['trueAnswersPercent'];
    totalQuestions = json['totalQuestions'];
  }

  Id localId = Isar.autoIncrement;
  String? id;
  Quiz? quiz;
  int? trueAnswers;
  int? trueAnswersPercent;
  int? totalQuestions;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    if (quiz != null) {
      map['quiz'] = quiz?.toJson();
    }
    map['trueAnswers'] = trueAnswers;
    map['trueAnswersPercent'] = trueAnswersPercent;
    map['totalQuestions'] = totalQuestions;
    return map;
  }
}
@embedded
class Quiz {
  Quiz({
    this.id,
    this.title,
  });

  Quiz.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
  }
  String? id;
  String? title;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['title'] = title;
    return map;
  }
}
