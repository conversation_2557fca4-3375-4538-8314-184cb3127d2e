import 'package:yetakchi/features/quiz/data/model/test_card_finished_model.dart';
import 'package:yetakchi/features/quiz/data/model/test_result.dart';

class TestResultReview {
  TestResultReview({
    this.id,
    this.quiz,
    this.trueAnswers,
    this.trueAnswersPercent,
    this.totalQuestions,
    this.questions,
  });

  TestResultReview.fromJson(dynamic json) {
    id = json['_id'];
    quiz = json['quiz'] != null ? Quiz.fromJson(json['quiz']) : null;
    trueAnswers = json['trueAnswers'];
    trueAnswersPercent = json['trueAnswersPercent'];
    totalQuestions = json['totalQuestions'];
    if (json['questions'] != null) {
      questions = [];
      json['questions'].forEach((v) {
        questions?.add(ResultQuestions.fromJson(v));
      });
    }
  }

  String? id;
  Quiz? quiz;
  int? trueAnswers;
  int? trueAnswersPercent;
  int? totalQuestions;
  List<ResultQuestions>? questions;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    if (quiz != null) {
      map['quiz'] = quiz?.toJson();
    }
    map['trueAnswers'] = trueAnswers;
    map['trueAnswersPercent'] = trueAnswersPercent;
    map['totalQuestions'] = totalQuestions;
    if (questions != null) {
      map['questions'] = questions?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}
