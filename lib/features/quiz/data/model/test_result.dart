class TestResult {
  TestResult({
      this.user, 
      this.province, 
      this.region, 
      this.district, 
      this.quiz, 
      this.trueAnswers, 
      this.trueAnswersPercent, 
      this.totalQuestions, 
      this.questions, 
      this.id, 
      this.createdAt, 
      this.updatedAt,});

  TestResult.fromJson(dynamic json) {
    user = json['user'];
    province = json['province'];
    region = json['region'];
    district = json['district'];
    quiz = json['quiz'];
    trueAnswers = json['trueAnswers'];
    trueAnswersPercent = json['trueAnswersPercent'];
    totalQuestions = json['totalQuestions'];
    if (json['questions'] != null) {
      questions = [];
      json['questions'].forEach((v) {
        questions?.add(ResultQuestions.fromJson(v));
      });
    }
    id = json['_id'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
  String? user;
  String? province;
  String? region;
  String? district;
  String? quiz;
  int? trueAnswers;
  int? trueAnswersPercent;
  int? totalQuestions;
  List<ResultQuestions>? questions;
  String? id;
  String? createdAt;
  String? updatedAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['user'] = user;
    map['province'] = province;
    map['region'] = region;
    map['district'] = district;
    map['quiz'] = quiz;
    map['trueAnswers'] = trueAnswers;
    map['trueAnswersPercent'] = trueAnswersPercent;
    map['totalQuestions'] = totalQuestions;
    if (questions != null) {
      map['questions'] = questions?.map((v) => v.toJson()).toList();
    }
    map['_id'] = id;
    map['createdAt'] = createdAt;
    map['updatedAt'] = updatedAt;
    return map;
  }

}

class ResultQuestions {
  ResultQuestions({
      this.question, 
      this.userAnswer, 
      this.correctAnswer, 
      this.isCorrect, 
      this.id,});

  ResultQuestions.fromJson(dynamic json) {
    question = json['question'] != null ? Question.fromJson(json['question']) : null;
    userAnswer = json['userAnswer'] != null ? UserAnswer.fromJson(json['userAnswer']) : null;
    correctAnswer = json['correctAnswer'] != null ? CorrectAnswer.fromJson(json['correctAnswer']) : null;
    isCorrect = json['isCorrect'];
    id = json['_id'];
  }
  Question? question;
  UserAnswer? userAnswer;
  CorrectAnswer? correctAnswer;
  bool? isCorrect;
  String? id;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (question != null) {
      map['question'] = question?.toJson();
    }
    if (userAnswer != null) {
      map['userAnswer'] = userAnswer?.toJson();
    }
    if (correctAnswer != null) {
      map['correctAnswer'] = correctAnswer?.toJson();
    }
    map['isCorrect'] = isCorrect;
    map['_id'] = id;
    return map;
  }

}

class CorrectAnswer {
  CorrectAnswer({
      this.id, 
      this.title,});

  CorrectAnswer.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
  }
  String? id;
  String? title;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['title'] = title;
    return map;
  }

}

class UserAnswer {
  UserAnswer({
      this.id, 
      this.title,});

  UserAnswer.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
  }
  String? id;
  String? title;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['title'] = title;
    return map;
  }

}

class Question {
  Question({
      this.id, 
      this.title,});

  Question.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
  }
  String? id;
  String? title;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['title'] = title;
    return map;
  }

}