/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsDocumentsGen {
  const $AssetsDocumentsGen();

  /// File path: assets/documents/privacy_ru.pdf
  String get privacyRu => 'assets/documents/privacy_ru.pdf';

  /// File path: assets/documents/privacy_uz.pdf
  String get privacyUz => 'assets/documents/privacy_uz.pdf';

  /// File path: assets/documents/sway.epub
  String get sway => 'assets/documents/sway.epub';

  /// List of all assets
  List<String> get values => [privacyRu, privacyUz, sway];
}

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Segoe_400.ttf
  String get segoe400 => 'assets/fonts/Segoe_400.ttf';

  /// File path: assets/fonts/Segoe_Black_900.ttf
  String get segoeBlack900 => 'assets/fonts/Segoe_Black_900.ttf';

  /// File path: assets/fonts/Segoe_Black_Italic_900.ttf
  String get segoeBlackItalic900 => 'assets/fonts/Segoe_Black_Italic_900.ttf';

  /// File path: assets/fonts/Segoe_Gras_700.ttf
  String get segoeGras700 => 'assets/fonts/Segoe_Gras_700.ttf';

  /// File path: assets/fonts/Segoe_Gras_Italique_700.ttf
  String get segoeGrasItalique700 => 'assets/fonts/Segoe_Gras_Italique_700.ttf';

  /// File path: assets/fonts/Segoe_Italique_400.ttf
  String get segoeItalique400 => 'assets/fonts/Segoe_Italique_400.ttf';

  /// File path: assets/fonts/Segoe_Light_300.ttf
  String get segoeLight300 => 'assets/fonts/Segoe_Light_300.ttf';

  /// File path: assets/fonts/Segoe_Light_Italic_300.ttf
  String get segoeLightItalic300 => 'assets/fonts/Segoe_Light_Italic_300.ttf';

  /// File path: assets/fonts/Segoe_Semibold_600.ttf
  String get segoeSemibold600 => 'assets/fonts/Segoe_Semibold_600.ttf';

  /// File path: assets/fonts/Segoe_Semibold_Italic_600.ttf
  String get segoeSemiboldItalic600 =>
      'assets/fonts/Segoe_Semibold_Italic_600.ttf';

  /// File path: assets/fonts/Segoe_Semilight_350.ttf
  String get segoeSemilight350 => 'assets/fonts/Segoe_Semilight_350.ttf';

  /// File path: assets/fonts/Segoe_Semilight_Italic_350.ttf
  String get segoeSemilightItalic350 =>
      'assets/fonts/Segoe_Semilight_Italic_350.ttf';

  /// List of all assets
  List<String> get values => [
        segoe400,
        segoeBlack900,
        segoeBlackItalic900,
        segoeGras700,
        segoeGrasItalique700,
        segoeItalique400,
        segoeLight300,
        segoeLightItalic300,
        segoeSemibold600,
        segoeSemiboldItalic600,
        segoeSemilight350,
        segoeSemilightItalic350
      ];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/app_icon.png
  AssetGenImage get appIcon => const AssetGenImage('assets/icons/app_icon.png');

  /// File path: assets/icons/arrow-left.svg
  String get arrowLeft => 'assets/icons/arrow-left.svg';

  /// File path: assets/icons/arrow-right.svg
  String get arrowRight => 'assets/icons/arrow-right.svg';

  /// File path: assets/icons/award.svg
  String get award => 'assets/icons/award.svg';

  /// File path: assets/icons/back_arrow_circle.svg
  String get backArrowCircle => 'assets/icons/back_arrow_circle.svg';

  /// File path: assets/icons/back_arrow_colored.svg
  String get backArrowColored => 'assets/icons/back_arrow_colored.svg';

  /// File path: assets/icons/bell.svg
  String get bell => 'assets/icons/bell.svg';

  /// File path: assets/icons/biometrics.png
  AssetGenImage get biometrics =>
      const AssetGenImage('assets/icons/biometrics.png');

  /// File path: assets/icons/book.svg
  String get book => 'assets/icons/book.svg';

  /// File path: assets/icons/bookPng.png
  AssetGenImage get bookPng => const AssetGenImage('assets/icons/bookPng.png');

  /// File path: assets/icons/book_placeholder.svg
  String get bookPlaceholder => 'assets/icons/book_placeholder.svg';

  /// File path: assets/icons/briefcase.svg
  String get briefcase => 'assets/icons/briefcase.svg';

  /// File path: assets/icons/busted_lock.svg
  String get bustedLock => 'assets/icons/busted_lock.svg';

  /// File path: assets/icons/c.svg
  String get c => 'assets/icons/c.svg';

  /// File path: assets/icons/calendar.svg
  String get calendar => 'assets/icons/calendar.svg';

  /// File path: assets/icons/camera1.svg
  String get camera1 => 'assets/icons/camera1.svg';

  /// File path: assets/icons/camera2.svg
  String get camera2 => 'assets/icons/camera2.svg';

  /// File path: assets/icons/check.svg
  String get check => 'assets/icons/check.svg';

  /// File path: assets/icons/check_circle.svg
  String get checkCircle => 'assets/icons/check_circle.svg';

  /// File path: assets/icons/check_circle_big.svg
  String get checkCircleBig => 'assets/icons/check_circle_big.svg';

  /// File path: assets/icons/cleaner.svg
  String get cleaner => 'assets/icons/cleaner.svg';

  /// File path: assets/icons/clock.svg
  String get clock => 'assets/icons/clock.svg';

  /// File path: assets/icons/close.svg
  String get close => 'assets/icons/close.svg';

  /// File path: assets/icons/company_logo.svg
  String get companyLogo => 'assets/icons/company_logo.svg';

  /// File path: assets/icons/compliant.svg
  String get compliant => 'assets/icons/compliant.svg';

  /// File path: assets/icons/correct.svg
  String get correct => 'assets/icons/correct.svg';

  /// File path: assets/icons/dash_divider.svg
  String get dashDivider => 'assets/icons/dash_divider.svg';

  /// File path: assets/icons/delete.svg
  String get delete => 'assets/icons/delete.svg';

  /// File path: assets/icons/document.svg
  String get document => 'assets/icons/document.svg';

  /// File path: assets/icons/done.json
  String get done => 'assets/icons/done.json';

  /// File path: assets/icons/edit.svg
  String get edit => 'assets/icons/edit.svg';

  /// File path: assets/icons/empty.png
  AssetGenImage get empty => const AssetGenImage('assets/icons/empty.png');

  /// File path: assets/icons/faceIdBig.svg
  String get faceIdBig => 'assets/icons/faceIdBig.svg';

  /// File path: assets/icons/face_id.svg
  String get faceId => 'assets/icons/face_id.svg';

  /// File path: assets/icons/finger-scan.svg
  String get fingerScan => 'assets/icons/finger-scan.svg';

  /// File path: assets/icons/global.svg
  String get global => 'assets/icons/global.svg';

  /// File path: assets/icons/hamburger.svg
  String get hamburger => 'assets/icons/hamburger.svg';

  /// File path: assets/icons/history.svg
  String get history => 'assets/icons/history.svg';

  /// File path: assets/icons/history_dark.svg
  String get historyDark => 'assets/icons/history_dark.svg';

  /// File path: assets/icons/home.svg
  String get home => 'assets/icons/home.svg';

  /// File path: assets/icons/ic_delete.svg
  String get icDelete => 'assets/icons/ic_delete.svg';

  /// File path: assets/icons/icon_close_dark.svg
  String get iconCloseDark => 'assets/icons/icon_close_dark.svg';

  /// File path: assets/icons/image_upload.svg
  String get imageUpload => 'assets/icons/image_upload.svg';

  /// File path: assets/icons/info-circle.svg
  String get infoCircle => 'assets/icons/info-circle.svg';

  /// File path: assets/icons/locale_icon.svg
  String get localeIcon => 'assets/icons/locale_icon.svg';

  /// File path: assets/icons/location.svg
  String get location => 'assets/icons/location.svg';

  /// File path: assets/icons/location1.svg
  String get location1 => 'assets/icons/location1.svg';

  /// File path: assets/icons/location2.svg
  String get location2 => 'assets/icons/location2.svg';

  /// File path: assets/icons/lock.svg
  String get lock => 'assets/icons/lock.svg';

  /// File path: assets/icons/lock2.svg
  String get lock2 => 'assets/icons/lock2.svg';

  /// File path: assets/icons/logo.svg
  String get logo => 'assets/icons/logo.svg';

  /// File path: assets/icons/logoAPI31.png
  AssetGenImage get logoAPI31 =>
      const AssetGenImage('assets/icons/logoAPI31.png');

  /// File path: assets/icons/logoIOS.png
  AssetGenImage get logoIOS => const AssetGenImage('assets/icons/logoIOS.png');

  /// File path: assets/icons/logoIbrat.svg
  String get logoIbrat => 'assets/icons/logoIbrat.svg';

  /// File path: assets/icons/logoPng.png
  AssetGenImage get logoPng => const AssetGenImage('assets/icons/logoPng.png');

  /// File path: assets/icons/logo_with_text.svg
  String get logoWithText => 'assets/icons/logo_with_text.svg';

  /// File path: assets/icons/logout.svg
  String get logout => 'assets/icons/logout.svg';

  /// File path: assets/icons/moon.svg
  String get moon => 'assets/icons/moon.svg';

  /// File path: assets/icons/payment.svg
  String get payment => 'assets/icons/payment.svg';

  /// File path: assets/icons/placeholder.svg
  String get placeholder => 'assets/icons/placeholder.svg';

  /// File path: assets/icons/pngLogoIbrat.png
  AssetGenImage get pngLogoIbrat =>
      const AssetGenImage('assets/icons/pngLogoIbrat.png');

  /// File path: assets/icons/profile.svg
  String get profile => 'assets/icons/profile.svg';

  /// File path: assets/icons/quiz.svg
  String get quiz => 'assets/icons/quiz.svg';

  /// File path: assets/icons/quiz_background.svg
  String get quizBackground => 'assets/icons/quiz_background.svg';

  /// File path: assets/icons/recognation_border.svg
  String get recognationBorder => 'assets/icons/recognation_border.svg';

  /// File path: assets/icons/recycle.svg
  String get recycle => 'assets/icons/recycle.svg';

  /// File path: assets/icons/rotate.json
  String get rotate => 'assets/icons/rotate.json';

  /// File path: assets/icons/scanning_done.svg
  String get scanningDone => 'assets/icons/scanning_done.svg';

  /// File path: assets/icons/scanning_done_png.png
  AssetGenImage get scanningDonePng =>
      const AssetGenImage('assets/icons/scanning_done_png.png');

  /// File path: assets/icons/sheet_pull.svg
  String get sheetPull => 'assets/icons/sheet_pull.svg';

  /// File path: assets/icons/song-icon.svg
  String get songIcon => 'assets/icons/song-icon.svg';

  /// File path: assets/icons/statistics.svg
  String get statistics => 'assets/icons/statistics.svg';

  /// File path: assets/icons/sun.svg
  String get sun => 'assets/icons/sun.svg';

  /// File path: assets/icons/support.svg
  String get support => 'assets/icons/support.svg';

  /// File path: assets/icons/swipe.svg
  String get swipe => 'assets/icons/swipe.svg';

  /// File path: assets/icons/text.svg
  String get text => 'assets/icons/text.svg';

  /// File path: assets/icons/tick-circle.svg
  String get tickCircle => 'assets/icons/tick-circle.svg';

  /// File path: assets/icons/tick.svg
  String get tick => 'assets/icons/tick.svg';

  /// File path: assets/icons/vertical_divider.svg
  String get verticalDivider => 'assets/icons/vertical_divider.svg';

  /// File path: assets/icons/wallet.svg
  String get wallet => 'assets/icons/wallet.svg';

  /// File path: assets/icons/warning.svg
  String get warning => 'assets/icons/warning.svg';

  /// File path: assets/icons/web.svg
  String get web => 'assets/icons/web.svg';

  /// File path: assets/icons/web_profile.svg
  String get webProfile => 'assets/icons/web_profile.svg';

  /// File path: assets/icons/wrong.svg
  String get wrong => 'assets/icons/wrong.svg';

  /// List of all assets
  List<dynamic> get values => [
        appIcon,
        arrowLeft,
        arrowRight,
        award,
        backArrowCircle,
        backArrowColored,
        bell,
        biometrics,
        book,
        bookPng,
        bookPlaceholder,
        briefcase,
        bustedLock,
        c,
        calendar,
        camera1,
        camera2,
        check,
        checkCircle,
        checkCircleBig,
        cleaner,
        clock,
        close,
        companyLogo,
        compliant,
        correct,
        dashDivider,
        delete,
        document,
        done,
        edit,
        empty,
        faceIdBig,
        faceId,
        fingerScan,
        global,
        hamburger,
        history,
        historyDark,
        home,
        icDelete,
        iconCloseDark,
        imageUpload,
        infoCircle,
        localeIcon,
        location,
        location1,
        location2,
        lock,
        lock2,
        logo,
        logoAPI31,
        logoIOS,
        logoIbrat,
        logoPng,
        logoWithText,
        logout,
        moon,
        payment,
        placeholder,
        pngLogoIbrat,
        profile,
        quiz,
        quizBackground,
        recognationBorder,
        recycle,
        rotate,
        scanningDone,
        scanningDonePng,
        sheetPull,
        songIcon,
        statistics,
        sun,
        support,
        swipe,
        text,
        tickCircle,
        tick,
        verticalDivider,
        wallet,
        warning,
        web,
        webProfile,
        wrong
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/browser.png
  AssetGenImage get browser => const AssetGenImage('assets/images/browser.png');

  /// File path: assets/images/browsers.png
  AssetGenImage get browsers =>
      const AssetGenImage('assets/images/browsers.png');

  /// File path: assets/images/click_logo.png
  AssetGenImage get clickLogo =>
      const AssetGenImage('assets/images/click_logo.png');

  /// File path: assets/images/empty_box.png
  AssetGenImage get emptyBox =>
      const AssetGenImage('assets/images/empty_box.png');

  /// File path: assets/images/flag_qq.png
  AssetGenImage get flagQq => const AssetGenImage('assets/images/flag_qq.png');

  /// File path: assets/images/flag_ru.png
  AssetGenImage get flagRu => const AssetGenImage('assets/images/flag_ru.png');

  /// File path: assets/images/flag_uz.png
  AssetGenImage get flagUz => const AssetGenImage('assets/images/flag_uz.png');

  /// File path: assets/images/gerb-opacity-gradient.png
  AssetGenImage get gerbOpacityGradient =>
      const AssetGenImage('assets/images/gerb-opacity-gradient.png');

  /// File path: assets/images/icon.png
  AssetGenImage get icon => const AssetGenImage('assets/images/icon.png');

  /// File path: assets/images/lock-pattern.png
  AssetGenImage get lockPattern =>
      const AssetGenImage('assets/images/lock-pattern.png');

  /// File path: assets/images/no_connection.png
  AssetGenImage get noConnection =>
      const AssetGenImage('assets/images/no_connection.png');

  /// File path: assets/images/payme_logo.png
  AssetGenImage get paymeLogo =>
      const AssetGenImage('assets/images/payme_logo.png');

  /// File path: assets/images/quiz_background.png
  AssetGenImage get quizBackground =>
      const AssetGenImage('assets/images/quiz_background.png');

  /// File path: assets/images/yia-vector.svg
  String get yiaVector => 'assets/images/yia-vector.svg';

  /// File path: assets/images/yia-white.png
  AssetGenImage get yiaWhite =>
      const AssetGenImage('assets/images/yia-white.png');

  /// List of all assets
  List<dynamic> get values => [
        browser,
        browsers,
        clickLogo,
        emptyBox,
        flagQq,
        flagRu,
        flagUz,
        gerbOpacityGradient,
        icon,
        lockPattern,
        noConnection,
        paymeLogo,
        quizBackground,
        yiaVector,
        yiaWhite
      ];
}

class $AssetsMapStyleGen {
  const $AssetsMapStyleGen();

  /// File path: assets/map_style/map_style.txt
  String get mapStyle => 'assets/map_style/map_style.txt';

  /// List of all assets
  List<String> get values => [mapStyle];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/kk.json
  String get kk => 'assets/translations/kk.json';

  /// File path: assets/translations/ru.json
  String get ru => 'assets/translations/ru.json';

  /// File path: assets/translations/uz.json
  String get uz => 'assets/translations/uz.json';

  /// List of all assets
  List<String> get values => [kk, ru, uz];
}

class Assets {
  Assets._();

  static const String book = 'assets/book.epub';
  static const $AssetsDocumentsGen documents = $AssetsDocumentsGen();
  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const AssetGenImage icGithub = AssetGenImage('assets/ic_github.png');
  static const AssetGenImage icSkype = AssetGenImage('assets/ic_skype.png');
  static const AssetGenImage icTelegram =
      AssetGenImage('assets/ic_telegram.png');
  static const AssetGenImage icWhatsapp =
      AssetGenImage('assets/ic_whatsapp.png');
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsMapStyleGen mapStyle = $AssetsMapStyleGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();

  /// List of all assets
  List<dynamic> get values => [book, icGithub, icSkype, icTelegram, icWhatsapp];
}

class AssetGenImage {
  const AssetGenImage(this._assetName);

  final String _assetName;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
