/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

class FontFamily {
  FontFamily._();

  /// Font family: Alegreya
  static const String alegreya = 'Alegreya';

  /// Font family: Amazon Ember
  static const String amazonEmber = 'Amazon Ember';

  /// Font family: Atkinson Hyperlegible
  static const String atkinsonHyperlegible = 'Atkinson Hyperlegible';

  /// Font family: Bitter Pro
  static const String bitterPro = 'Bitter Pro';

  /// Font family: Bookerly
  static const String bookerly = 'Bookerly';

  /// Font family: Droid Sans
  static const String droidSans = 'Droid Sans';

  /// Font family: EB Garamond
  static const String eBGaramond = 'EB Garamond';

  /// Font family: Gentium Book Plus
  static const String gentiumBookPlus = 'Gentium Book Plus';

  /// Font family: Halant
  static const String halant = 'Halant';

  /// Font family: IBM Plex Sans
  static const String iBMPlexSans = 'IBM Plex Sans';

  /// Font family: LinLibertine
  static const String linLibertine = 'LinLibertine';

  /// Font family: Literata
  static const String literata = 'Literata';

  /// Font family: Lora
  static const String lora = 'Lora';

  /// Font family: Segoe
  static const String segoe = 'Segoe';

  /// Font family: Ubuntu
  static const String ubuntu = 'Ubuntu';
}
