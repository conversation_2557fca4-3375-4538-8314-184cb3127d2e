///This file is automatically generated. DO NOT EDIT, all your changes would be lost.
class Assets {
  Assets._();

  static const String assetsBook = 'assets/book.epub';
  static const String assetsIcGithub = 'assets/ic_github.png';
  static const String assetsIcSkype = 'assets/ic_skype.png';
  static const String assetsIcTelegram = 'assets/ic_telegram.png';
  static const String assetsIcWhatsapp = 'assets/ic_whatsapp.png';
  static const String bookFontsAlegreya = 'assets/fonts/book_fonts/Alegreya.ttf';
  static const String bookFontsAmazonEmberRegular = 'assets/fonts/book_fonts/Amazon-Ember-Regular.ttf';
  static const String bookFontsAtkinsonHyperlegibleRegular = 'assets/fonts/book_fonts/AtkinsonHyperlegible-Regular.ttf';
  static const String bookFontsBitterProRegular = 'assets/fonts/book_fonts/BitterPro-Regular.ttf';
  static const String bookFontsBookerly = 'assets/fonts/book_fonts/Bookerly.ttf';
  static const String bookFontsDroidSans = 'assets/fonts/book_fonts/DroidSans.ttf';
  static const String bookFontsEBGaramondVar = 'assets/fonts/book_fonts/EBGaramond-Var.ttf';
  static const String bookFontsGentiumBookPlusRegular = 'assets/fonts/book_fonts/GentiumBookPlus-Regular.ttf';
  static const String bookFontsHalantRegular = 'assets/fonts/book_fonts/Halant-Regular.ttf';
  static const String bookFontsIBMPlexSansRegular = 'assets/fonts/book_fonts/IBMPlexSans-Regular.ttf';
  static const String bookFontsLinLibertineRegular = 'assets/fonts/book_fonts/LinLibertine-Regular.ttf';
  static const String bookFontsLiterataVar = 'assets/fonts/book_fonts/Literata-Var.ttf';
  static const String bookFontsLoraVar = 'assets/fonts/book_fonts/Lora-Var.ttf';
  static const String bookFontsUbuntuVar = 'assets/fonts/book_fonts/Ubuntu-Var.ttf';
  static const String documentsPrivacyRu = 'assets/documents/privacy_ru.pdf';
  static const String documentsPrivacyUz = 'assets/documents/privacy_uz.pdf';
  static const String documentsSway = 'assets/documents/sway.epub';
  static const String fontsSegoe400 = 'assets/fonts/Segoe_400.ttf';
  static const String fontsSegoeBlack900 = 'assets/fonts/Segoe_Black_900.ttf';
  static const String fontsSegoeBlackItalic900 = 'assets/fonts/Segoe_Black_Italic_900.ttf';
  static const String fontsSegoeGras700 = 'assets/fonts/Segoe_Gras_700.ttf';
  static const String fontsSegoeGrasItalique700 = 'assets/fonts/Segoe_Gras_Italique_700.ttf';
  static const String fontsSegoeItalique400 = 'assets/fonts/Segoe_Italique_400.ttf';
  static const String fontsSegoeLight300 = 'assets/fonts/Segoe_Light_300.ttf';
  static const String fontsSegoeLightItalic300 = 'assets/fonts/Segoe_Light_Italic_300.ttf';
  static const String fontsSegoeSemibold600 = 'assets/fonts/Segoe_Semibold_600.ttf';
  static const String fontsSegoeSemiboldItalic600 = 'assets/fonts/Segoe_Semibold_Italic_600.ttf';
  static const String fontsSegoeSemilight350 = 'assets/fonts/Segoe_Semilight_350.ttf';
  static const String fontsSegoeSemilightItalic350 = 'assets/fonts/Segoe_Semilight_Italic_350.ttf';
  static const String iconsAppIcon = 'assets/icons/app_icon.png';
  static const String iconsArrowLeft = 'assets/icons/arrow-left.svg';
  static const String iconsArrowRight = 'assets/icons/arrow-right.svg';
  static const String iconsAward = 'assets/icons/award.svg';
  static const String iconsBackArrowCircle = 'assets/icons/back_arrow_circle.svg';
  static const String iconsBackArrowColored = 'assets/icons/back_arrow_colored.svg';
  static const String iconsBell = 'assets/icons/bell.svg';
  static const String iconsBiometrics = 'assets/icons/biometrics.png';
  static const String iconsBook = 'assets/icons/book.svg';
  static const String iconsBookPlaceholder = 'assets/icons/book_placeholder.svg';
  static const String iconsBookPng = 'assets/icons/bookPng.png';
  static const String iconsBriefcase = 'assets/icons/briefcase.svg';
  static const String iconsBustedLock = 'assets/icons/busted_lock.svg';
  static const String iconsC = 'assets/icons/c.svg';
  static const String iconsCalendar = 'assets/icons/calendar.svg';
  static const String iconsCamera1 = 'assets/icons/camera1.svg';
  static const String iconsCamera2 = 'assets/icons/camera2.svg';
  static const String iconsCheck = 'assets/icons/check.svg';
  static const String iconsCheckCircle = 'assets/icons/check_circle.svg';
  static const String iconsCheckCircleBig = 'assets/icons/check_circle_big.svg';
  static const String iconsCleaner = 'assets/icons/cleaner.svg';
  static const String iconsClock = 'assets/icons/clock.svg';
  static const String iconsClose = 'assets/icons/close.svg';
  static const String iconsCompanyLogo = 'assets/icons/company_logo.svg';
  static const String iconsCompliant = 'assets/icons/compliant.svg';
  static const String iconsCorrect = 'assets/icons/correct.svg';
  static const String iconsDashDivider = 'assets/icons/dash_divider.svg';
  static const String iconsDelete = 'assets/icons/delete.svg';
  static const String iconsDocument = 'assets/icons/document.svg';
  static const String iconsDone = 'assets/icons/done.json';
  static const String iconsEdit = 'assets/icons/edit.svg';
  static const String iconsEmpty = 'assets/icons/empty.png';
  static const String iconsFaceId = 'assets/icons/face_id.svg';
  static const String iconsFaceIdBig = 'assets/icons/faceIdBig.svg';
  static const String iconsFingerScan = 'assets/icons/finger-scan.svg';
  static const String iconsGlobal = 'assets/icons/global.svg';
  static const String iconsHamburger = 'assets/icons/hamburger.svg';
  static const String iconsHistory = 'assets/icons/history.svg';
  static const String iconsHistoryDark = 'assets/icons/history_dark.svg';
  static const String iconsHome = 'assets/icons/home.svg';
  static const String iconsIcDelete = 'assets/icons/ic_delete.svg';
  static const String iconsIconCloseDark = 'assets/icons/icon_close_dark.svg';
  static const String iconsImageUpload = 'assets/icons/image_upload.svg';
  static const String iconsInfoCircle = 'assets/icons/info-circle.svg';
  static const String iconsLocaleIcon = 'assets/icons/locale_icon.svg';
  static const String iconsLocation = 'assets/icons/location.svg';
  static const String iconsLocation1 = 'assets/icons/location1.svg';
  static const String iconsLocation2 = 'assets/icons/location2.svg';
  static const String iconsLock = 'assets/icons/lock.svg';
  static const String iconsLock2 = 'assets/icons/lock2.svg';
  static const String iconsLogo = 'assets/icons/logo.svg';
  static const String iconsLogoAPI31 = 'assets/icons/logoAPI31.png';
  static const String iconsLogoIOS = 'assets/icons/logoIOS.png';
  static const String iconsLogoIbrat = 'assets/icons/logoIbrat.svg';
  static const String iconsLogoPng = 'assets/icons/logoPng.png';
  static const String iconsLogoWithText = 'assets/icons/logo_with_text.svg';
  static const String iconsLogout = 'assets/icons/logout.svg';
  static const String iconsMoon = 'assets/icons/moon.svg';
  static const String iconsPayment = 'assets/icons/payment.svg';
  static const String iconsPlaceholder = 'assets/icons/placeholder.svg';
  static const String iconsPngLogoIbrat = 'assets/icons/pngLogoIbrat.png';
  static const String iconsProfile = 'assets/icons/profile.svg';
  static const String iconsQuiz = 'assets/icons/quiz.svg';
  static const String iconsQuizBackground = 'assets/icons/quiz_background.svg';
  static const String iconsRecognationBorder = 'assets/icons/recognation_border.svg';
  static const String iconsRecycle = 'assets/icons/recycle.svg';
  static const String iconsRotate = 'assets/icons/rotate.json';
  static const String iconsScanningDone = 'assets/icons/scanning_done.svg';
  static const String iconsScanningDonePng = 'assets/icons/scanning_done_png.png';
  static const String iconsSend = 'assets/icons/send.svg';
  static const String iconsSheetPull = 'assets/icons/sheet_pull.svg';
  static const String iconsSongIcon = 'assets/icons/song-icon.svg';
  static const String iconsStatistics = 'assets/icons/statistics.svg';
  static const String iconsSun = 'assets/icons/sun.svg';
  static const String iconsSupport = 'assets/icons/support.svg';
  static const String iconsSwipe = 'assets/icons/swipe.svg';
  static const String iconsText = 'assets/icons/text.svg';
  static const String iconsTick = 'assets/icons/tick.svg';
  static const String iconsTickCircle = 'assets/icons/tick-circle.svg';
  static const String iconsVerticalDivider = 'assets/icons/vertical_divider.svg';
  static const String iconsWallet = 'assets/icons/wallet.svg';
  static const String iconsWarning = 'assets/icons/warning.svg';
  static const String iconsWeb = 'assets/icons/web.svg';
  static const String iconsWebProfile = 'assets/icons/web_profile.svg';
  static const String iconsWrong = 'assets/icons/wrong.svg';
  static const String imagesBrowser = 'assets/images/browser.png';
  static const String imagesBrowsers = 'assets/images/browsers.png';
  static const String imagesClickLogo = 'assets/images/click_logo.png';
  static const String imagesEmptyBox = 'assets/images/empty_box.png';
  static const String imagesFlagQq = 'assets/images/flag_qq.png';
  static const String imagesFlagRu = 'assets/images/flag_ru.png';
  static const String imagesFlagUz = 'assets/images/flag_uz.png';
  static const String imagesGerbOpacityGradient = 'assets/images/gerb-opacity-gradient.png';
  static const String imagesIcon = 'assets/images/icon.png';
  static const String imagesLockPattern = 'assets/images/lock-pattern.png';
  static const String imagesNoConnection = 'assets/images/no_connection.png';
  static const String imagesPaymeLogo = 'assets/images/payme_logo.png';
  static const String imagesQuizBackground = 'assets/images/quiz_background.png';
  static const String imagesYetakchiAi = 'assets/images/yetakchi_ai.png';
  static const String imagesYiaVector = 'assets/images/yia-vector.svg';
  static const String imagesYiaWhite = 'assets/images/yia-white.png';
  static const String mapStyleMapStyle = 'assets/map_style/map_style.txt';
  static const String riveLoadingDots = 'assets/rive/loading_dots.riv';
  static const String translationsKk = 'assets/translations/kk.json';
  static const String translationsRu = 'assets/translations/ru.json';
  static const String translationsUz = 'assets/translations/uz.json';

}
