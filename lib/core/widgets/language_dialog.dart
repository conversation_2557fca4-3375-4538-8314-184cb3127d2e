import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class LanguageDialog extends StatefulWidget {
  final VoidCallback onSelectCallBack;

  const LanguageDialog({Key? key, required this.onSelectCallBack})
      : super(key: key);

  @override
  State<LanguageDialog> createState() => _LanguageDialogState();
}

class _LanguageDialogState extends State<LanguageDialog> {
  SharedPreferences prefs = di();

  String _verticalGroupValue = "O\'zbekcha";

  final _status = [
    "O\'zbekcha",
    "Qaraqa<PERSON>paq<PERSON>",
    "Русский",
  ];
  BuildContext? dcontext;

  dismissDailog() {
    if (dcontext != null) {
      Navigator.pop(dcontext!);
    }
  }

  String languageText() {
    String? lang = prefs.getString(language_pref);
    switch (lang) {
      case 'uz':
        {
          return 'O\'zbekcha';
        }
      case 'ru':
        {
          return 'Русский';
        }
      case 'kk':
        {
          return 'Qaraqalpaqsha';
        }
      default:
        {
          return 'O\'zbekcha';
        }
    }
  }

  @override
  void initState() {
    languageText();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      _verticalGroupValue = languageText();
      return Dialog(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(20.r))),
        insetPadding:
            EdgeInsets.symmetric(horizontal: context.isTablet ? 80.w : 30.w),
        child: Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(20.r)),
              color: Theme.of(context).cardTheme.color),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 20, top: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      LocaleKeys.change_language.tr(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold, fontSize: context.isTablet ? 12.sp : 16.sp),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Icon(
                            Icons.close,
                            color: cGrayColor1,
                            size: 20.h,
                          )),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 6.h,
              ),
              Divider(
                height: 1,
                color: cGrayColor1,
              ),
              SizedBox(
                height: 6.h,
              ),
              InkWell(
                onTap: () {
                  setState(() {
                    _verticalGroupValue = _status[0];
                    context.setLocale(Locale('uz'));
                    setLanguage('uz');
                    Get.updateLocale(Locale("uz"))
                        .then((value) => widget.onSelectCallBack());
                  });
                },
                child: ListTile(
                  leading: Transform.scale(
                    scale: context.isTablet
                        ? _verticalGroupValue == _status[0]
                            ? 2.2
                            : 2
                        : _verticalGroupValue == _status[0]
                            ? 1.4
                            : 1.2,
                    child: Radio(
                      value: _status[0],
                      groupValue: _verticalGroupValue,
                      onChanged: (value) {
                        setState(() {
                          _verticalGroupValue = _status[0];
                          context.setLocale(Locale('uz'));
                          setLanguage('uz');
                          Get.updateLocale(Locale("uz"))
                              .then((value) => widget.onSelectCallBack());
                        });
                      },
                      activeColor: cFirstColor,
                    ),
                  ),
                  title: Text(_status[0],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold, fontSize: context.isTablet ? 12.sp : 16.sp)),
                ),
              ),
              InkWell(
                onTap: () {
                  setState(() {
                    _verticalGroupValue = _status[1];
                    context.setLocale(Locale('kk'));
                    setLanguage('kk');
                    Get.updateLocale(Locale("kk"))
                        .then((value) => widget.onSelectCallBack());
                  });
                },
                child: ListTile(
                  leading: Transform.scale(
                    scale: context.isTablet
                        ? _verticalGroupValue == _status[1]
                            ? 2.2
                            : 2
                        : _verticalGroupValue == _status[1]
                            ? 1.4
                            : 1.2,
                    child: Radio(
                      value: _status[1],
                      groupValue: _verticalGroupValue,
                      onChanged: (value) {
                        setState(() {
                          _verticalGroupValue = _status[1];
                          context.setLocale(Locale('kk'));
                          setLanguage('kk');
                          Get.updateLocale(Locale("kk"))
                              .then((value) => widget.onSelectCallBack());
                        });
                      },
                      activeColor: cFirstColor,
                    ),
                  ),
                  title: Text(_status[1],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: context.isTablet ? 12.sp : 16.sp)),
                ),
              ),
              InkWell(
                onTap: () {
                  setState(() {
                    _verticalGroupValue = _status[2];
                    context.setLocale(Locale('ru'));
                    setLanguage('ru');
                    Get.updateLocale(Locale("ru"))
                        .then((value) => widget.onSelectCallBack());
                  });
                },
                child: ListTile(
                  leading: Transform.scale(
                    scale: context.isTablet
                        ? _verticalGroupValue == _status[2]
                            ? 2.2
                            : 2
                        : _verticalGroupValue == _status[2]
                            ? 1.4
                            : 1.2,
                    child: Radio(
                      value: _status[2],
                      groupValue: _verticalGroupValue,
                      activeColor: cFirstColor,
                      onChanged: (String? value) {
                        setState(() {
                          _verticalGroupValue = _status[2];
                          context.setLocale(Locale('ru'));
                          setLanguage('ru');
                          Get.updateLocale(Locale("ru"))
                              .then((value) => widget.onSelectCallBack());
                        });
                      },
                    ),
                  ),
                  title: Text(_status[2],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold, fontSize: context.isTablet ? 12.sp : 16.sp)),
                ),
              ),
              // RadioGroup<String>.builder(
              //   groupValue: _verticalGroupValue,
              //   onChanged: (value) {
              //     _verticalGroupValue = value ?? '';
              //     setState(() {
              //       _verticalGroupValue = value ?? '';
              //       if (_verticalGroupValue == 'O\'zbekcha') {
              //         context.setLocale(Locale('uz'));
              //         setLanguage('uz');
              //         Get.updateLocale(Locale("uz"));
              //       } else if (_verticalGroupValue == 'Русский') {
              //         context.setLocale(Locale('ru'));
              //         setLanguage('ru');
              //         Get.updateLocale(Locale("ru"));
              //       } else {
              //         context.setLocale(Locale('kk'));
              //         setLanguage('kk');
              //         Get.updateLocale(Locale("kk"));
              //       }
              //       widget.onSelectCallBack();
              //     });
              //   },
              //   items: _status,
              //   textStyle: Theme.of(context)
              //       .textTheme
              //       .bodySmall
              //       ?.copyWith(fontWeight: FontWeight.bold, fontSize: 16.sp),
              //   itemBuilder: (item) => RadioButtonBuilder(
              //     item,
              //   ),
              //   fillColor: cFirstColor,
              // ),
              SizedBox(
                height: 20.h,
              ),
            ],
          ),
        ),
      );
    });
  }
}
