import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

class DottedBorderWidget extends StatelessWidget {
  final Widget child;

  const DottedBorderWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18.r),
        color: Theme.of(context).scaffoldBackgroundColor,
      ),
      child: DottedBorder(
        child: child,
        color: cFirstColor,
        strokeWidth: 1.w,
        dashPattern: [context.isPhone ? 4 : 10],
        borderType: BorderType.RRect,
        radius: Radius.circular(18.r),
      ),
    );
  }
}
