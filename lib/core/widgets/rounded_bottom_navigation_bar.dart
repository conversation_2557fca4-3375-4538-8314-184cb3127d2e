import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:badges/badges.dart' as badges;
import 'package:isar/isar.dart';
import 'package:yetakchi/core/database/isar_service.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart';
import 'package:yetakchi/features/send_data/models/not_send_model.dart';
import 'package:yetakchi/features/task_send/data/model/task_img_model.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

import '../../features/electron_library/book_list/presentation/pages/electron_library_page.dart';

class RoundedBottomNavigationBar extends StatefulWidget {
  final Function(int index) onSelected;
  final int? number;
  final int? pageIndex;

  const RoundedBottomNavigationBar(
      {super.key, required this.onSelected, this.number, this.pageIndex});

  @override
  State<RoundedBottomNavigationBar> createState() =>
      _RoundedBottomNavigationBarState();
}

class _RoundedBottomNavigationBarState
    extends State<RoundedBottomNavigationBar> {
  int _selectedIndex = 0;
  final IsarService isarService = di();
  int notSendCount = 0;

  count() {
    int task = isarService.isar.taskImgModels.where().countSync();
    int work = isarService.isar.notSendModels.where().countSync();
    notSendCount = task + work;
  }

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.pageIndex ?? 0;
    // Query<NotSendModel> notSendModelQuery =
    //     isarService.isar.notSendModels.where().build();
    //
    // Stream<List<NotSendModel>> queryChanged =
    //     notSendModelQuery.watch(fireImmediately: true);
    //
    // queryChanged.listen((event) {
    //   notSendCount += event.length;
    //   print("NOT SEND WORK:${event.length}");
    // });
    //
    // Query<TaskImgModel> taskImgModelQuery =
    //     isarService.isar.taskImgModels.where().build();
    //
    // Stream<List<TaskImgModel>> taskQuery =
    //     taskImgModelQuery.watch(fireImmediately: true);
    //
    // taskQuery.listen((event) {
    //   notSendCount += event.length;
    //   print("NOT SEND TASK:${event.length}");
    // });
  }

  @override
  Widget build(BuildContext context) {
    count();
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      margin: EdgeInsets.only(bottom: 10.h, top: 2.h, left: 10.w, right: 10.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(40.r),
        gradient: LinearGradient(
          colors: [
            cFirstColor,
            cThirdColor,
          ],
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(40.r),
        child: GNav(
          rippleColor: cWhiteColor,
          // tab button ripple color when pressed
          hoverColor: cWhiteColor,
          // tab button hover color
          haptic: true,
          // haptic feedback
          tabBorderRadius: 40.r,
          gap: 8.w,
          // the tab button gap between icon and text
          color: cWhiteColor,
          // unselected icon color
          activeColor: cFirstColor,
          // selected icon and text color
          iconSize: 24,
          // tab button icon size
          tabBackgroundColor: cWhiteColor,
          // selected tab background color
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
          // navigation bar padding
          tabs: [
            GButton(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
              leading: SizedBox(
                width: 25.h,
                height: 25.h,
                child: SvgPicture.asset(Assets.iconsHome,
                    colorFilter: ColorFilter.mode(
                        _selectedIndex == 0
                            ? cFirstColor
                            : cWhiteColor.withOpacity(0.5),
                        BlendMode.srcIn)),
              ),
              text: LocaleKeys.main_menu.tr(),
              icon: Icons.start,
              textStyle: TextStyle(fontSize: 14.sp, color: cFirstColor),
            ),
            GButton(
              icon: Icons.search,
              leading: badges.Badge(
                position: badges.BadgePosition.custom(start: 10.w, bottom: 6.h),
                badgeStyle: badges.BadgeStyle(padding: EdgeInsets.all(4.w)),
                showBadge: notSendCount != 0 ? true : false,
                badgeContent: Text(
                  notSendCount.toString(),
                  style: TextStyle(color: cWhiteColor, fontSize: 12.sp),
                ),
                child: SizedBox(
                  width: 25.h,
                  height: 25.h,
                  child: SvgPicture.asset(Assets.iconsAward,
                      colorFilter: ColorFilter.mode(
                          _selectedIndex == 1
                              ? cFirstColor
                              : cWhiteColor.withOpacity(0.5),
                          BlendMode.srcIn)),
                ),
              ),
              text: LocaleKeys.not_send_menu.tr(),
              textStyle: TextStyle(fontSize: 14.sp, color: cFirstColor),
            ),
            GButton(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
              icon: Icons.person,
              leading: SizedBox(
                width: 25.h,
                height: 25.h,
                child: SvgPicture.asset(Assets.iconsTickCircle,
                    colorFilter: ColorFilter.mode(
                        _selectedIndex == 2
                            ? cFirstColor
                            : cWhiteColor.withOpacity(0.5),
                        BlendMode.srcIn)),
              ),
              text: LocaleKeys.sent_menu.tr(),
              textStyle: TextStyle(fontSize: 14.sp, color: cFirstColor),
            ),
          ],
          selectedIndex: _selectedIndex,
          onTabChange: (index) {
            setState(() {
              _selectedIndex = index;
              widget.onSelected(_selectedIndex);
            });
          },
        ),
      ),
    );
  }
}

class RoundedBookReadingWidget extends StatelessWidget {
  const RoundedBookReadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      onTap: () {
        // CustomToast.showToast("${LocaleKeys.soon_add_feature.tr()}...");
        Get.to(ElectronLibraryPage.screen());
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            margin: EdgeInsets.only(
                bottom: 10.h, top: 2.h, left: 20.w, right: 20.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(40.r),
              gradient: LinearGradient(
                colors: [
                  cFirstColor,
                  cThirdColor,
                ],
              ),
            ),
            child: Row(
              children: [
                Spacer(
                  flex: 3,
                ),
                Expanded(
                  flex: 10,
                  child: Padding(
                    padding: EdgeInsets.only(top: 5.h, bottom: 5.h),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        LocaleKeys.digital_library.tr(),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: context.isTablet ? 16.sp : 18.sp,
                            fontWeight: FontWeight.w600,
                            color: cWhiteColor),
                      ),
                    ),
                  ),
                ),
                Spacer(
                  flex: 1,
                )
              ],
            ),
          ),
          SizedBox(
            height: 130.h,
            child: Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                    padding: EdgeInsets.only(left: 35.w, bottom: 40.h),
                    child: Image.asset(Assets.iconsBookPng,
                        height: 60.h, width: 60.w, scale: 0.1)),
              ],
            ),
          )
        ],
      ),
    );
  }
}
