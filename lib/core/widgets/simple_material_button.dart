import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

class SimpleMaterialButton extends StatelessWidget {
  final String title;
  final VoidCallback onTap;
  final double? width;
  final double? height;
  final Color color;
  final Color? textColor;

  const SimpleMaterialButton(
      {super.key,
      required this.title,
      required this.onTap,
      this.width,
      this.height,
      required this.color,
      this.textColor});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(40.r),
      child: Container(
          alignment: Alignment.center,
          width: width,
          height: height,
          decoration: BoxDecoration(
              color: color, borderRadius: BorderRadius.circular(40.r)),
          child: MaterialButton(
            minWidth: MediaQuery.of(context).size.width,
            height: 56.h,
            onPressed: onTap,
            child: Text(
              title,
              style:
                  TextStyle(color: textColor ?? cWhiteColor, fontSize: 17.sp),
            ),
          )),
    );
  }
}
