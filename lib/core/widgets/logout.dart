import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class LogOutDialog extends StatelessWidget {
  const LogOutDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
        child: Dialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 50.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.r)),
            backgroundColor: Theme.of(context).cardTheme.color,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: EdgeInsets.all(8.0.w),
                      child: InkWell(
                          onTap: () {
                            Navigator.pop(context);},
                          child: Icon(
                            Icons.close,
                            size: 20.w,
                          )),
                    )
                  ],
                ),
                SvgPicture.asset(
                  Assets.iconsWarning,
                  width: 60.w,
                ),
                SizedBox(
                  height: 10.h,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: Text(
                    LocaleKeys.want_exit_profile.tr(),
                    style: TextStyle(
                        fontSize: context.isTablet ? 18.sp : 24.sp,
                        color: Theme.of(context).primaryColor),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: 8.h,
                ),
                Text(LocaleKeys.exit_warning.tr(),
                    textAlign: TextAlign.center,
                    style: Theme.of(context)
                        .textTheme
                        .bodyLarge
                        ?.copyWith(fontSize: context.isTablet ? 12.sp : 16.sp)),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    MaterialButton(
                      onPressed: () {
                        clearAndLogout(context);
                      },
                      color: cCarrotColor,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r)),
                      minWidth: 80.w,
                      height: 40.h,
                      child: Text(
                        LocaleKeys.yes.tr(),
                        style: TextStyle(
                          color: cWhiteColor,
                          fontWeight: FontWeight.bold,
                          fontSize: context.isTablet ? 12.sp : 16.sp,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    MaterialButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      color: cFirstColor,
                      minWidth: 80.w,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r)),
                      height: 40.h,
                      child: Text(
                        LocaleKeys.no.tr(),
                        style: TextStyle(
                          color: cWhiteColor,
                          fontWeight: FontWeight.bold,
                          fontSize: context.isTablet ? 12.sp : 16.sp,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 20.h,
                )
              ],
            )));
  }
}
