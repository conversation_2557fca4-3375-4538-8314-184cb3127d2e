import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/function/functions.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/generated/assets.dart';

PreferredSize AppHeaderWidget(
    {required String title,
    required VoidCallback onBackTap,
    required bool isBackVisible,
    bool bigText = false}) {
  return PreferredSize(
    preferredSize: Size.fromHeight(HEADER_SIZE.h),
    child: HeaderWidget(
      title: title,
      onBackTap: onBackTap,
      isVisible: isBackVisible,
      bigText: bigText,
    ), // Set this height
  );
}

class HeaderWidget extends StatelessWidget {
  final String title;
  final VoidCallback onBackTap;
  final bool isVisible;
  final bool bigText;

  HeaderWidget(
      {super.key,
      required this.title,
      required this.onBackTap,
      required this.isVisible,
      this.bigText = false});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(20.r),
          bottomLeft: Radius.circular(20.r)),
      child: Container(
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
            color: cSecondColor,
            gradient: LinearGradient(
              colors: isDark()
                  ? [cCardDarkColor, cCardDarkColor]
                  : [
                      cSecondColor,
                      cFirstColor,
                    ],
            ),
            borderRadius: BorderRadius.only(
                bottomRight: Radius.circular(20.r),
                bottomLeft: Radius.circular(20.r))),
        child: Stack(
          alignment: Alignment.center,
          children: [
            SvgPicture.asset(
              Assets.iconsWeb,
              width: MediaQuery.of(context).size.width.w,
              fit: BoxFit.fill,
            ),
            Padding(
              padding: EdgeInsets.only(
                  left: 5.w, right: 5.w, top: 40.h, bottom: 10.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 2,
                    child: Visibility(
                      visible: isVisible,
                      child: Material(
                        color: Colors.transparent,
                        shape: CircleBorder(),
                        clipBehavior: Clip.hardEdge,
                        child: IconButton(
                          iconSize: 45.h,
                          onPressed: onBackTap,
                          icon: SvgPicture.asset(
                            Assets.iconsBackArrowCircle,
                            height: 45.h,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 6,
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: bigText ? 14.sp : 16.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white),
                    ),
                  ),
                  Spacer(
                    flex: 2,
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
