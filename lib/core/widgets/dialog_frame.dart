import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yetakchi/generated/assets.dart';


class AllDialogSkeleton extends StatefulWidget {
  final Widget child;
  final String title, icon;
  final Color? color;
  final double? radius;
  final Color? textColor;
  final Color? iconColor;
  final FontWeight? fontWeight;
  final bool? isCloseVisible;
  final bool? isNoShadow;

  const AllDialogSkeleton(
      {Key? key,
      required this.child,
      required this.title,
      required this.icon,
      this.color,
      this.radius,
      this.textColor,
      this.iconColor,
      this.fontWeight,
      this.isCloseVisible,
      this.isNoShadow})
      : super(key: key);

  @override
  State<AllDialogSkeleton> createState() => _AllDialogSkeletonState();
}

class _AllDialogSkeletonState extends State<AllDialogSkeleton> {
  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      child: Padding(
        // margin: EdgeInsets.only(
        //     bottom: MediaQuery.of(context).size.height / 5),
        padding: EdgeInsets.symmetric(horizontal: 18.w),
        child: Dialog(
          backgroundColor: widget.color,
          alignment: Alignment.center,
          shadowColor: widget.isNoShadow == true ? Colors.transparent : null,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
          insetPadding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 0),
          child: Container(
            padding:  EdgeInsets.all(14.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.radius ?? 20.r)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(widget.icon,
                        color: widget.iconColor ??
                            Theme.of(context).iconTheme.color,
                        height: 22.w,
                        width: 22.w),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(widget.title,
                          style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: widget.fontWeight ?? FontWeight.w400,
                              fontFamily: 'Medium',
                              color: widget.textColor ??
                                  Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.color)),
                    ),
                    Visibility(
                      visible: widget.isCloseVisible ?? true,
                      child: InkWell(
                        overlayColor:
                            MaterialStateProperty.all(Colors.transparent),
                        borderRadius: BorderRadius.circular(22.r),
                        onTap: () => Navigator.pop(context),
                        child: SvgPicture.asset(Assets.iconsClose,
                            color: Theme.of(context).primaryColor,
                            width: 22.w, height: 22.w),
                      ),
                    ),
                  ],
                ),
                widget.child,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
