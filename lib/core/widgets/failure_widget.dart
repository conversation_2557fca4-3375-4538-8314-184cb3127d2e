import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/generated/assets.dart';
import 'package:yetakchi/translations/locale_keys.g.dart';

class FailureWidget extends StatelessWidget {
  final String text;
  final VoidCallback onTap;

  const FailureWidget({super.key, required this.text, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ClipRect(
              child: Container(
                height: 300.h,
                child: Column(
                  children: [
                    SizedBox(
                      height: 20.h,
                    ),
                    Expanded(
                        child: SvgPicture.asset(
                      Assets.iconsWarning,
                      height: 140.h,
                    )),
                    Padding(
                        padding: EdgeInsets.only(
                            top: 10.h, left: 30.w, right: 30.w, bottom: 10.h),
                        child: Text(
                          text,
                          textAlign: TextAlign.center,
                          style: TextStyle(color: cGrayColor1),
                        )),
                    CupertinoButton(
                      child: Text(
                        LocaleKeys.refresh.tr(),
                        style: TextStyle(color: cGrayColor1),
                      ),
                      color: cGrayColor1.withAlpha(80),
                      onPressed: onTap,
                    ),
                    SizedBox(
                      height: 20.h,
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
