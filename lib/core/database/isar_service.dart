import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:yetakchi/back_service/model/tracked_location.dart';
import 'package:yetakchi/features/app_time/model/app_usage_model.dart';
import 'package:yetakchi/features/auth/data/model/calendar.dart';
import 'package:yetakchi/features/auth/data/model/user_model.dart';
import 'package:yetakchi/features/complaint/model/complaint_model.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/book_model.dart';
import 'package:yetakchi/features/electron_library/book_list/data/models/chapter_model.dart';
import 'package:yetakchi/features/electron_library/book_reader/Model/book_progress_model.dart';
import 'package:yetakchi/features/home/<USER>/models/category_model.dart';
import 'package:yetakchi/features/home/<USER>/models/today_works.dart';
import 'package:yetakchi/features/quiz/data/model/test_card_finished_model.dart';
import 'package:yetakchi/features/quiz/data/model/test_card_new_model.dart';
import 'package:yetakchi/features/sent_page/data/model/sent_model.dart';
import 'package:yetakchi/features/statistics/data/model/statistics.dart';
import 'package:yetakchi/features/home/<USER>/models/support_model.dart';
import 'package:yetakchi/features/send_data/models/not_send_model.dart';
import 'package:yetakchi/features/task_send/data/model/task_img_model.dart';
import 'package:yetakchi/features/tasks/data/model/special_done_task.dart';
import 'package:yetakchi/features/tasks/data/model/special_new_task.dart';

const SCHEMES = [
  UserModelSchema,
  TaskImgModelSchema,
  SpecialNewTaskItemSchema,
  SpecialDoneTaskItemSchema,
  SupportModelSchema,
  NotSendModelSchema,
  SentModelSchema,
  CategoryModelSchema,
  TodayWorksSchema,
  StatisticsSchema,
  TrackedLocationSchema,
  CalendarSchema,
  AppUsageModelSchema,
  ComplaintModelSchema,
  TestCardNewSchema,
  TestCardFinishedSchema,
  BookModelSchema,
  ChapterModelSchema,
  BookProgressModelSchema,
];

class IsarService {
  late final Isar isar;

  IsarService._create(this.isar);

  static Future<IsarService> buildIsarService() async {
    final dir = await getApplicationDocumentsDirectory();
    final isar = await Isar.open(
      SCHEMES,
      directory: dir.path,
    );

    print('=== Isar opened!');
    return IsarService._create(isar);
  }
}

Future<Isar> getIsarForSchedule() async {
  var isar = await Isar.getInstance();

  print("=== Before, isar is: ${isar?.path}"); //PRINT 1

  if (isar == null) {
    print("=== Isar is not open, so opening it..");
    final dir = await getApplicationSupportDirectory();
    return isar = await Isar.open(
      SCHEMES,
      directory: dir.path,
    );
  } else {
    return isar;
  }
}
