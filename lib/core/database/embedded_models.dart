import 'package:isar/isar.dart';

part 'embedded_models.g.dart';

@embedded
class Category {
  Category({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,
  });

  Category.fromJson(dynamic json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
  }

  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['titleQQ'] = titleQQ;
    return map;
  }
}

@embedded
class SubCategory {
  SubCategory({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,
    this.withVideo,
    this.meet,
    this.webEdit,
    this.support,
  });

  SubCategory.fromJson(dynamic json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
    withVideo = json['withVideo'];
    meet = json['meet'];
    webEdit = json['webEdit'];
    support = json['support'];
  }

  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;
  bool? withVideo;
  bool? meet;
  bool? webEdit;
  bool? support;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['titleQQ'] = titleQQ;
    map['withVideo'] = withVideo;
    map['meet'] = meet;
    map['webEdit'] = webEdit;
    map['support'] = support;
    return map;
  }
}

@embedded
class DocsToday {
  DocsToday({
    this.user,
    this.category,
    this.subCategory,
    this.value,
  });

  DocsToday.fromJson(dynamic json) {
    user = json['user'];
    category = json['category'];
    subCategory = json['subCategory'] != null
        ? SubCategory.fromJson(json['subCategory'])
        : null;
    value = json['value'];
  }

  String? user;
  String? category;
  SubCategory? subCategory;
  int? value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['user'] = user;
    map['category'] = category;
    if (subCategory != null) {
      map['subCategory'] = subCategory?.toJson();
    }
    map['value'] = value;
    return map;
  }
}

@embedded
class ImgModel {
  String? latLang;
  String? sana;
  String? image;

  ImgModel({this.latLang, this.sana, this.image});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['latLang'] = latLang;
    data['time'] = sana;
    data['image'] = image;
    return data;
  }

  ImgModel.fromJson(Map<String, dynamic> json) {
    latLang = json['latLang'];
    sana = json['sana'];
    image = json['image'];
  }
}

@embedded
class SubCategorys {
  SubCategorys({
    this.subCategory,
    this.value,
  });

  SubCategorys.fromJson(dynamic json) {
    subCategory = json['subCategory'] != null
        ? SubCategory.fromJson(json['subCategory'])
        : null;
    value = json['value'];
  }

  SubCategory? subCategory;
  int? value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (subCategory != null) {
      map['subCategory'] = subCategory?.toJson();
    }
    map['value'] = value;
    return map;
  }
}

@embedded
class District {
  District({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,
    this.coordinate,
    this.coordinates,
  });

  District.fromJson(Map<String, dynamic> json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
    coordinate = json['coordinate'] != null
        ? Coordinate.fromJson(json['coordinate'])
        : null;

    // Assuming 'coordinates' is a list of coordinates
    coordinates =
        (json['coordinates'] as List<dynamic>?)?.map<Coordinate>((item) {
      return Coordinate.fromJson(item);
    }).toList();
  }

  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;
  Coordinate? coordinate;
  List<Coordinate>? coordinates;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['titleQQ'] = titleQQ;
    map['coordinate'] = coordinate;
    map['coordinates'] = coordinates;
    return map;
  }
}

@embedded
class Payments {
  Payments({
    this.clickTransId,
    this.amount,
    this.signTime,
    this.error,
    this.errorNote,
    this.month,
    this.startDate,
    this.endDate,
  });

  Payments.fromJson(dynamic json) {
    clickTransId = json['click_trans_id'];
    amount = json['amount'];
    signTime = json['sign_time'];
    error = json['error'];
    errorNote = json['error_note'];
    month = json['month'];
    startDate = json['startDate'];
    endDate = json['endDate'];
  }

  String? clickTransId;
  String? amount;
  String? signTime;
  String? error;
  String? errorNote;
  int? month;
  String? startDate;
  String? endDate;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['click_trans_id'] = clickTransId;
    map['amount'] = amount;
    map['sign_time'] = signTime;
    map['error'] = error;
    map['error_note'] = errorNote;
    map['month'] = month;
    map['startDate'] = startDate;
    map['endDate'] = endDate;
    return map;
  }
}

@embedded
class Info {
  Info({
    this.platform,
    this.version,
    this.appVersion,
    this.id,
  });

  Info.fromJson(dynamic json) {
    platform = json['platform'];
    version = json['version'];
    appVersion = json['appVersion'];
    id = json['_id'];
  }

  String? platform;
  String? version;
  String? appVersion;
  String? id;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['platform'] = platform;
    map['version'] = version;
    map['appVersion'] = appVersion;
    map['_id'] = id;
    return map;
  }
}

@embedded
class Region {
  Region({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,
  });

  Region.fromJson(dynamic json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
  }

  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['titleQQ'] = titleQQ;
    return map;
  }
}

@embedded
class Province {
  Province({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,
  });

  Province.fromJson(dynamic json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
  }

  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['titleQQ'] = titleQQ;
    return map;
  }
}

@embedded
class Coordinate {
  double? latitude;
  double? longitude;

  Coordinate({this.latitude, this.longitude});

  factory Coordinate.fromJson(List<dynamic> json) {
    return Coordinate(
      latitude: (json.asMap().containsKey(1) ? json[1] as double : null),
      longitude: (json.asMap().containsKey(0) ? json[0] as double : null),
    );
  }

  List<double> toJson() {
    return [latitude ?? 0, longitude ?? 0];
  }
}

@embedded
class InActive {
  InActive({
    this.start,
    this.end,});

  InActive.fromJson(dynamic json) {
    start = json['start'];
    end = json['end'];
  }
  String? start;
  String? end;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['start'] = start;
    map['end'] = end;
    return map;
  }

}