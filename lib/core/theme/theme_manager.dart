import 'package:shared_preferences/shared_preferences.dart';
import 'package:theme_mode_handler/theme_mode_manager_interface.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

import '../../di/dependency_injection.dart';

class MyManager implements IThemeModeManager {
  final SharedPreferences sharedPreferences = di();

  @override
  Future<String> loadThemeMode() async {
    return await sharedPreferences.getString(theme_pref) ?? 'ThemeMode.light';
  }

  @override
  Future<bool> saveThemeMode(String value) async {
    return await sharedPreferences.setString(theme_pref, value);
  }
}
