import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yetakchi/core/utils/app_constants.dart';

ThemeData lightTheme = ThemeData(
  fontFamily: 'Segoe',
  brightness: Brightness.light,
  primaryColor: cFirstColor,
  primaryColorLight: cWhiteColor,
  primaryColorDark: cFirstColor,
  indicatorColor: cWhiteColor,
  cardColor: cPinkLight,
  floatingActionButtonTheme:
      FloatingActionButtonThemeData(backgroundColor: cSecondColor),
  scaffoldBackgroundColor: cFourthColor,
  textTheme: TextTheme(
      bodyMedium: TextStyle(
          color: cFirstTextColor,
          fontSize: 16.sp,
          fontWeight: FontWeight.normal),
      bodyLarge: TextStyle(
          color: cFirstTextColor,
          fontSize: 16.sp,
          fontWeight: FontWeight.normal),
      bodySmall: TextStyle(
          color: cBlackColor, fontSize: 16.sp, fontWeight: FontWeight.normal)),
  checkboxTheme: CheckboxThemeData(
    fillColor: MaterialStateProperty.resolveWith((states) {
      // If the checkbox is selected, return color, otherwise null (default)
      if (states.contains(MaterialState.selected)) {
        return cFirstColor;
      } else {
        return null;
      }
    }),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(2.r),
    ),
    side: BorderSide(color: cGrayColor1, width: 1.w), // Set the border color
  ),
  iconTheme: IconThemeData(color: cBlackColor),
  cardTheme: CardTheme(color: cWhiteColor),
);

ThemeData darkTheme = ThemeData(
  fontFamily: 'Segoe',
  brightness: Brightness.dark,
  primaryColor: cPrimaryTextDark,
  primaryColorLight: cFourthColorDark,
  primaryColorDark: cCardDarkColor,
  indicatorColor: cWhiteColor,
  cardColor: cCardDarkColor,
  scaffoldBackgroundColor: cFourthColorDark,
  floatingActionButtonTheme:
      FloatingActionButtonThemeData(backgroundColor: cPrimaryTextDark),
  textTheme: TextTheme(
      bodyMedium: TextStyle(
          color: cPrimaryTextDark,
          fontSize: 16.sp,
          fontWeight: FontWeight.normal),
      bodyLarge: TextStyle(
          color: cWhiteColor, fontSize: 16.sp, fontWeight: FontWeight.normal),
      bodySmall: TextStyle(
          color: cPrimaryTextDark,
          fontSize: 16.sp,
          fontWeight: FontWeight.normal)),
  checkboxTheme: CheckboxThemeData(
    fillColor: MaterialStateProperty.resolveWith((states) {
      // If the checkbox is selected, return color, otherwise null (default)
      if (states.contains(MaterialState.selected)) {
        return cFirstColor;
      } else {
        return null;
      }
    }),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(2.r),
    ),
    side: BorderSide(
        color: cPrimaryTextDark, width: 1.w), // Set the border color
  ),
  iconTheme: IconThemeData(color: cPrimaryTextDark),
  cardTheme: CardTheme(
    color: cCardDarkColor,
  ),
);
