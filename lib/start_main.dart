import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart' as di;
import 'package:yetakchi/core/network/network_info.dart';
import 'package:yetakchi/features/app.dart';
import 'package:yetakchi/firebase_options.dart';
import 'package:yetakchi/translations/codegen_loader.g.dart';
import 'features/lock/definitions.dart';

import 'push_notifications/awesome_notification_controller.dart';

void startMain() async {
  runZonedGuarded(() async {
    ///Don't move or touch stupid
    WidgetsFlutterBinding.ensureInitialized();
    HttpOverrides.global = MyHttpOverrides();
    await EasyLocalization.ensureInitialized();
    Intl.defaultLocale = 'en_US';

    await di.init();

    AwesomeNotifications().initialize(null, [
      NotificationChannel(
          channelKey: CHANNEL_KEY,
          channelName: CHANNEL_NAME,
          defaultColor: cFirstColor,
          channelDescription: CHANNEL_DESCRIPTION,
          importance: NotificationImportance.High)
    ]);

    AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
      if (!isAllowed) {
        // This is just a basic example. For real apps, you must show some
        // friendly dialog box before call the request method.
        // This is very important to not harm the user experience
        AwesomeNotifications()
            .requestPermissionToSendNotifications(permissions: [
          NotificationPermission.PreciseAlarms,
          NotificationPermission.Alert,
          NotificationPermission.Badge,
          NotificationPermission.Sound,
          NotificationPermission.Vibration,
          NotificationPermission.CriticalAlert,
          NotificationPermission.FullScreenIntent
        ]);
      }
    });

    await NotificationController.initializeLocalNotifications();
    await NotificationController.startListeningNotificationEvents();

    if (Platform.isAndroid) {
      await InAppWebViewController.setWebContentsDebuggingEnabled(false);
    }
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);
    FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

    SystemChrome.setPreferredOrientations(
      [
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ],
    ).then((val) {
      runApp(EasyLocalization(
          child: Stack(
            alignment: Alignment.center,
            children: [
              Phoenix(
                child: AppProvider(),
              ),
            ],
          ),
          supportedLocales: [Locale('uz'), Locale('kk'), Locale('ru')],
          assetLoader: CodegenLoader(),
          fallbackLocale: Locale('uz'),
          startLocale: Locale('uz'),
          path: 'assets/translations'));
    });
  }, (error, stacktrace) {
    log('runZonedGuarded Errors: $error');
    debugPrint("Yetakchi app error: $error");
    FirebaseCrashlytics.instance.recordError(error, stacktrace, fatal: true);
    debugPrint("Stacktrace: $stacktrace");
  });
}
