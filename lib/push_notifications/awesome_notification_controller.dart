import 'dart:isolate';
import 'dart:ui';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yetakchi/core/utils/app_constants.dart';
import 'package:yetakchi/di/dependency_injection.dart';

import 'notification_service.dart';

///  *********************************************
///     NOTIFICATION CONTROLLER
///  *********************************************

class NotificationController {
  static ReceivedAction? initialAction;

  static var _initialized = true;

  void bindBackgroundIsolate() {
    ReceivePort port = ReceivePort();
    IsolateNameServer.registerPortWithName(port.sendPort, 'background_isolate');

    port.listen((var received) async {
      _handleBackgroundAction(received);
    });
  }

  /// ============================ NOT USED  =========================== ///

  ///TODO: NOTES!
  /// Don't forget, you can't get valid context after opening killed app when set scheduled notification ///
  /// EVEN WITH SWITCHING ISOLATES ///

  static Future<void> onSilentActionHandle(ReceivedAction received) async {
    print('On new background action received: ${received.toMap()}');

    if (!_initialized) {
      SendPort? uiSendPort =
          IsolateNameServer.lookupPortByName('background_isolate');
      if (uiSendPort != null) {
        print(
            'Background action running on parallel isolate without valid context. Redirecting execution');
        uiSendPort.send(received);
        return;
      }
    }

    print('Background action running on main isolate');
    await _handleBackgroundAction(received);
  }

  static Future<void> _handleBackgroundAction(ReceivedAction received) async {
    ///
  }

  ///===================================================================///

  // static AudioPlayer audioPlayer = di();
  // static Source source =
  //     AssetSource('ringtone.m4a'); // Replace with your sound file path

  static late ReceivedNotification receivedNotification;
  static AndroidDeviceInfo androidInfo = di();

  ///  *********************************************
  ///     INITIALIZATIONS
  ///  *********************************************
  ///
  static Future<void> initializeLocalNotifications() async {
    ///=========================================================

    await AwesomeNotifications().initialize(
        null, //'resource://drawable/res_app_icon',//
        [
          NotificationChannel(
              channelKey: 'basic_channel',
              channelName: 'Simple notifications',
              channelDescription: 'Notification are awesome!',
              playSound: true,
              onlyAlertOnce: true,
              // soundSource: 'resource://raw/ringtone',
              defaultRingtoneType: DefaultRingtoneType.Notification,
              groupAlertBehavior: GroupAlertBehavior.Children,
              importance: NotificationImportance.Max,
              defaultPrivacy: NotificationPrivacy.Private,
              defaultColor: Colors.deepOrange,
              ledColor: Colors.deepOrange)
        ],
        debug: false);

    // Get initial notification action is optional
    initialAction = await AwesomeNotifications()
        .getInitialNotificationAction(removeFromActionEvents: false);
  }

  ///  *********************************************
  ///     NOTIFICATION EVENTS LISTENER
  ///  *********************************************
  ///  Notifications events are only delivered after call this method
  static Future<void> startListeningNotificationEvents() async {
    AwesomeNotifications().setListeners(
        onActionReceivedMethod: onActionReceivedMethod,
        onNotificationCreatedMethod: onNotificationCreatedMethod,
        onDismissActionReceivedMethod: onDismissActionReceivedMethod,
        onNotificationDisplayedMethod: onNotificationDisplayedMethod);
  }

  ///  *********************************************
  ///     NOTIFICATION EVENTS
  ///  *********************************************

  ///TODO: NOTES!
  /// Here context is valid even app is killed when notification is not from schedule///

  @pragma('vm:entry-point')
  static Future<void> onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    print(
        "Action (sometimes auto on lower SDK) is: ${receivedAction.buttonKeyPressed}");

    print("=== Payload: ${receivedAction.payload}");

    ///Open specific page
    open();
  }

  ///TODO: Bug in iOS: https://github.com/rafaelsetragni/awesome_notifications/issues/743
  ///
  /// Solution: https://github.com/rafaelsetragni/awesome_notifications/issues/717

  /// Use this method to detect every time that a new notification is displayed
  @pragma("vm:entry-point")
  static Future<void> onNotificationDisplayedMethod(
      ReceivedNotification notification) async {
    receivedNotification = notification;

    print(
        'Android: onNotificationDisplayedMethod | If you see me on IOS, issue has been solved!');
  }

  /// Use this method to detect when a new notification or a schedule is created
  @pragma("vm:entry-point")
  static Future<void> onNotificationCreatedMethod(
      ReceivedNotification receivedNotification) async {
    print("Notification ${receivedNotification.id} is created!");
  }

  /// Use this method to detect if the user dismissed a notification
  @pragma("vm:entry-point")
  static Future<void> onDismissActionReceivedMethod(
      ReceivedAction receivedAction) async {
    // Your code goes here
    // audioPlayer.stop();
    // FlutterRingtonePlayer.stop();
    print('Dismissed!');
  }

  ///  *********************************************
  ///     REQUESTING NOTIFICATION PERMISSIONS
  ///  *********************************************
  ///
  static Future<bool> displayNotificationRationale(BuildContext context) async {
    bool userAuthorized = false;

    await showDialog(
        context: context,
        builder: (BuildContext ctx) {
          return AlertDialog(
            title: Text('Get Notified!',
                style: Theme.of(context).textTheme.titleLarge),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Image.asset(
                        'assets/animated-bell.gif',
                        height: MediaQuery.of(context).size.height * 0.3,
                        fit: BoxFit.fitWidth,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const Text(
                    'Allow Awesome Notifications to send you beautiful notifications!'),
              ],
            ),
            actions: [
              TextButton(
                  onPressed: () {
                    Navigator.of(ctx).pop();
                  },
                  child: Text(
                    'Deny',
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(color: Colors.red),
                  )),
              TextButton(
                  onPressed: () async {
                    userAuthorized = true;
                    Navigator.of(ctx).pop();
                  },
                  child: Text(
                    'Allow',
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(color: Colors.deepPurple),
                  )),
            ],
          );
        });
    return userAuthorized &&
        await AwesomeNotifications().requestPermissionToSendNotifications();
  }

  ///  *********************************************
  ///     NOTIFICATION CREATION METHODS
  ///  *********************************************

  static Future<void> createNewNotification(data) async {
    //for reading language from preference
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String lang = prefs.getString(language_pref) ?? 'uz';

    bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
    // if (!isAllowed) isAllowed = await displayNotificationRationale(context);
    if (!isAllowed) return;

    await AwesomeNotifications().createNotification(
        content: NotificationContent(
            id: -1,
            // -1 is replaced by a random number
            channelKey: 'basic_channel',
            title: data['title'],
            body: data['body'],
            // bigPicture:
            //     'https://storage.googleapis.com/cms-storage-bucket/d406c736e7c4c57f5f61.png',
            // largeIcon: 'https://storage.googleapis.com/cms-storage-bucket/0dbfcc7a59cd1cf16282.png',
            //'asset://assets/images/balloons-in-sky.jpg',
            notificationLayout: NotificationLayout.BigText,
            actionType: ActionType.Default,
            wakeUpScreen: true,
            criticalAlert: true,
        ),
        actionButtons: []);
  }

  static Future<void> resetBadgeCounter() async {
    await AwesomeNotifications().resetGlobalBadge();
  }

  static Future<void> cancelNotifications() async {
    await AwesomeNotifications().cancelAll();
  }

  static Future<void> cancelNotificationById(int id) async {
    await AwesomeNotifications().cancel(id);
  }
}
