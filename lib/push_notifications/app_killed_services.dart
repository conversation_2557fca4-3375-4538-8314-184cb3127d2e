// import 'package:awesome_notifications/awesome_notifications.dart';
// import 'package:flutter/material.dart';
// import 'package:ijrochi/core/utils/app_constants.dart';
// import 'package:ijrochi/features/home/<USER>/pages/home.dart';
// import 'package:ijrochi/features/lock/presentation/pages/lock_page.dart';
// import 'package:ijrochi/features/login/presentation/pages/login_page.dart';
// import 'package:ijrochi/features/notifications/notification/data/model/notification_model.dart'
//     as n;
// import 'package:ijrochi/features/notifications/notification_detail/presentation/page/notification_detail_page.dart';
// import 'package:ijrochi/features/tasks/model/task_model.dart';
// import 'package:ijrochi/features/tasks/task_detail_page/presentation/pages/task_detail_page.dart';
// import 'package:ijrochi/push_notifications/ringing_page_notification.dart';
// import 'package:ijrochi/push_notifications/ringing_page_task.dart';
// import 'package:shared_preferences/shared_preferences.dart';
//
// import '../di/dependency_injection.dart';
// import 'notification_service.dart';
//
// ///It's addon for our working code in case app can't get context when killed
//
// Future<Widget> getInitialPage() async {
//   final SharedPreferences prefs = di();
//
//   ReceivedAction? receivedAction = await AwesomeNotifications()
//       .getInitialNotificationAction(removeFromActionEvents: true);
//
//   var lastStamp = prefs.getString(LAST_ACTION_STAMP) ?? null;
//
//   if (receivedAction?.channelKey == 'sound_channel' && receivedAction != null) {
//     if (lastStamp != receivedAction.displayedDate.toString()) {
//       print(
//           'SOUND-CHANNEL: ========== Set initial page to RINGING PAGE ==========');
//       prefs.setString(
//           LAST_ACTION_STAMP, receivedAction.displayedDate.toString());
//       return setInitialPageToRingingPage(receivedAction);
//     } else {
//       print(
//           'SOUND-CHANNEL: ========== Set initial page to HOME PAGE ==========');
//       return setInitialPageToHomePage();
//     }
//   } else if (receivedAction?.channelKey == 'basic_channel' &&
//       receivedAction != null) {
//     if (lastStamp != receivedAction.displayedDate.toString() &&
//         receivedAction.buttonKeyPressed == OPEN) {
//       print(
//           'BASIC-CHANNEL: ========== Set initial page to OPEN PAGE ==========');
//       prefs.setString(
//           LAST_ACTION_STAMP, receivedAction.displayedDate.toString());
//       return openReturn(receivedAction.payload?['uid'] ?? '-1',
//           receivedAction.payload?['type'] ?? NULL, OPEN);
//     } else if (lastStamp != receivedAction.displayedDate.toString() &&
//         receivedAction.buttonKeyPressed == ACCEPT) {
//       print(
//           'BASIC-CHANNEL: ========== Set initial page to ACCEPT PAGE ==========');
//       prefs.setString(
//           LAST_ACTION_STAMP, receivedAction.displayedDate.toString());
//       return openReturn(receivedAction.payload?['uid'] ?? '-1',
//           receivedAction.payload?['type'] ?? NULL, ACCEPT);
//     } else {
//       print(
//           'BASIC-CHANNEL: ========== Set initial page to HOME PAGE ==========');
//       return setInitialPageToHomePage();
//     }
//   } else {
//     print('========== Set initial page to HOME PAGE ==========');
//     return setInitialPageToHomePage();
//   }
// }
//
// Widget setInitialPageToRingingPage(ReceivedAction receivedAction) {
//   if (receivedAction.payload?['type'] == NOTIFICATION) {
//     return RingingPageNotification(receivedNotification: receivedAction);
//   } else {
//     return RingingPageTask(
//       receivedNotification: receivedAction,
//     );
//   }
// }
//
// class Loading extends StatefulWidget {
//   const Loading({super.key});
//
//   @override
//   State<Loading> createState() => _LoadingState();
// }
//
// class _LoadingState extends State<Loading> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         body: Container(
//             height: double.infinity,
//             width: double.infinity,
//             child: Center(
//               child: CircularProgressIndicator(
//                 color: cFirstColor,
//               ),
//             )));
//   }
// }
//
//
// Widget setInitialPageToHomePage() {
//   final SharedPreferences prefs = di();
//
//   //Ensure to stop ringing if something happens wrong!
//   // FlutterRingtonePlayer.stop();
//
//   String id = prefs.getString('id') ?? "";
//   bool isLoggedIn = (prefs.getString('pin_code') ?? '') != '';
//   return (id == ""
//       ? LoginPage.screen()
//       : isLoggedIn
//           ? LockPage()
//           : HomePage());
// }
//
// Future<Widget> openReturn(String id, String type, String ACTION) async {
//   ///TODO: Test API (tested)
//   var isAccepted = await isAcceptedFunc(id, type);
//
//   if (type == NOTIFICATION) {
//     n.NotificationModel? notification = await getData(id, type);
//     if (notification != null) {
//       if (!isAccepted) {
//         return NotificationDetailPage.screen(notification, pushKey: ACTION);
//       } else {
//         return NotificationDetailPage.screen(notification);
//       }
//     } else {
//       return setInitialPageToHomePage();
//     }
//   } else {
//     TaskModel? task = await getData(id, type);
//     if (task != null) {
//       if (!isAccepted) {
//         return TaskDetailPage.screen(task, pushKey: ACTION);
//       } else {
//         return TaskDetailPage.screen(task);
//       }
//     } else {
//       return setInitialPageToHomePage();
//     }
//   }
// }
