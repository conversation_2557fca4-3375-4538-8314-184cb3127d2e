


void main(){
  List<int> a=[1,2,3];
  List<int> b=[];
  b=a;
  b.add(2);
  print(b);
}


// import 'dart:convert';
//
// import 'package:intl/intl.dart';
// import 'package:yetakchi/back_service/model/tracked_location.dart';
//
// void main() {
//   List<TrackedLocation> list = [
//     TrackedLocation(
//       lat: 40.71,
//       lng: 70.34,
//       dateTime: '2024-02-03 10:00',
//       date: '2024-02-03',
//       offside: false,
//       timeChange: false,
//       locChange: false,
//     ),
//     TrackedLocation(
//       lat: 40.71,
//       lng: 70.34,
//       dateTime: '2024-02-03 10:00',
//       date: '2024-02-03',
//       offside: false,
//       timeChange: false,
//       locChange: false,
//     ),
//   ];
//   List<Map<String, dynamic>> filteredList = getFilteredLocation(list);
//   List<Map<String, dynamic>> sendList = [];
//
//   for (int j = 0; j < filteredList.length; j++) {
//     for (int i = 1; i < filteredList[j]['data'].length; i++) {
//       DateTime currentDateTime =
//           DateTime.parse(filteredList[j]['data'][i]['date']);
//       DateTime previousDateTime =
//           DateTime.parse(filteredList[j]['data'][i - 1]['date']);
//       int differenceInMinutes =
//           currentDateTime.difference(previousDateTime).inMinutes;
//
//       if (differenceInMinutes > 15) {
//         filteredList[j]['data'][i]['deviceOff'] = true;
//       } else {
//         filteredList[j]['data'][i]['deviceOff'] = false;
//       }
//     }
//   }
//   for (int j = 0; j < filteredList.length; j++) {
//     bool needRemove = true;
//     for (int i = 0; i < filteredList[j]['data'].length; i++) {
//       if (filteredList[j]['data'][i]['offside'] == true ||
//           filteredList[j]['data'][i]['deviceOff'] == true ||
//           filteredList[j]['data'][i]['timeChange'] == true ||
//           filteredList[j]['data'][i]['locChange'] == true) {
//         needRemove = false;
//         break;
//       }
//     }
//
//     if (needRemove == false) {
//       sendList.add(filteredList[j]);
//     }
//     ;
//   }
//   print("\n");
//   print(json.encode(sendList));
// }
//
// List<Map<String, dynamic>> getFilteredLocation(List<TrackedLocation?> list) {
//   Map<String, List<Map<String, dynamic>>> filteredData = {};
//   for (var obj in list) {
//     if (!filteredData.containsKey(obj?.date)) {
//       filteredData[obj!.date] = [];
//     }
//
//     filteredData[obj?.date]!.add({
//       "date": obj?.dateTime,
//       "lat": obj?.lat,
//       "lng": obj?.lng,
//       "offside": obj?.offside,
//       "locChange": obj?.locChange,
//       "timeChange": obj?.timeChange,
//       "deviceOff": false
//     });
//   }
//   List<Map<String, dynamic>> result = [];
//
//   filteredData.forEach((date, data) {
//     result.add({"data": data, "date": date});
//   });
//   return result;
// }
