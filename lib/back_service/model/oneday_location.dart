// import 'package:isar/isar.dart';
//
// part 'oneday_location';
//
// @collection
// @Name("OneDayLocation")
// class OneDayLocation {
//   Id localId = Isar.autoIncrement;
//   double? lat;
//   double? lng;
//   String? date;
//   String? dateTime;
//   bool? offside;
//   bool? timeChange;
//   bool? locChange;
//   bool? deviceOff;
//
//   OneDayLocation(
//       {this.lat,
//       this.lng,
//       this.date,
//       this.dateTime,
//       this.offside,
//       this.timeChange,
//       this.locChange,
//       this.deviceOff});
// }
