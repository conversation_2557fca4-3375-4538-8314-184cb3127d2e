import 'package:isar/isar.dart';

part 'location.g.dart';

@embedded
class Location {
  double? lat;
  double? lng;
  String? date;
  String? dateTime;
  bool? offside;
  bool? timeChange;
  bool? locChange;
  bool? deviceOff;

  Location({this.lat, this.lng, this.date, this.dateTime, this.offside,
    this.timeChange, this.locChange,this.deviceOff=false});

  Location copyWith({
    double? lat,
    double? lng,
    String? date,
    String? dateTime,
    bool? offside,
    bool? timeChange,
    bool? locChange,
    bool? deviceOff,
  }) {
    return Location(
      lat: lat ?? this.lat,
      lng: lng ?? this.lng,
      date: date ?? this.date,
      dateTime: dateTime ?? this.dateTime,
      offside: offside ?? this.offside,
      timeChange: timeChange ?? this.timeChange,
      locChange: locChange ?? this.locChange,
      deviceOff: deviceOff ?? this.deviceOff,
    );
  }
}
