// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tracked_location.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetTrackedLocationCollection on Isar {
  IsarCollection<TrackedLocation> get trackedLocations => this.collection();
}

const TrackedLocationSchema = CollectionSchema(
  name: r'TrackedLocation',
  id: -8746698804100189263,
  properties: {
    r'date': PropertySchema(
      id: 0,
      name: r'date',
      type: IsarType.string,
    ),
    r'locations': PropertySchema(
      id: 1,
      name: r'locations',
      type: IsarType.objectList,
      target: r'Location',
    ),
    r'status': PropertySchema(
      id: 2,
      name: r'status',
      type: IsarType.bool,
    )
  },
  estimateSize: _trackedLocationEstimateSize,
  serialize: _trackedLocationSerialize,
  deserialize: _trackedLocationDeserialize,
  deserializeProp: _trackedLocationDeserializeProp,
  idName: r'localId',
  indexes: {},
  links: {},
  embeddedSchemas: {r'Location': LocationSchema},
  getId: _trackedLocationGetId,
  getLinks: _trackedLocationGetLinks,
  attach: _trackedLocationAttach,
  version: '3.1.0+1',
);

int _trackedLocationEstimateSize(
  TrackedLocation object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.date.length * 3;
  bytesCount += 3 + object.locations.length * 3;
  {
    final offsets = allOffsets[Location]!;
    for (var i = 0; i < object.locations.length; i++) {
      final value = object.locations[i];
      bytesCount += LocationSchema.estimateSize(value, offsets, allOffsets);
    }
  }
  return bytesCount;
}

void _trackedLocationSerialize(
  TrackedLocation object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.date);
  writer.writeObjectList<Location>(
    offsets[1],
    allOffsets,
    LocationSchema.serialize,
    object.locations,
  );
  writer.writeBool(offsets[2], object.status);
}

TrackedLocation _trackedLocationDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = TrackedLocation(
    date: reader.readString(offsets[0]),
    locations: reader.readObjectList<Location>(
          offsets[1],
          LocationSchema.deserialize,
          allOffsets,
          Location(),
        ) ??
        [],
    status: reader.readBoolOrNull(offsets[2]) ?? false,
  );
  object.localId = id;
  return object;
}

P _trackedLocationDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readString(offset)) as P;
    case 1:
      return (reader.readObjectList<Location>(
            offset,
            LocationSchema.deserialize,
            allOffsets,
            Location(),
          ) ??
          []) as P;
    case 2:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _trackedLocationGetId(TrackedLocation object) {
  return object.localId;
}

List<IsarLinkBase<dynamic>> _trackedLocationGetLinks(TrackedLocation object) {
  return [];
}

void _trackedLocationAttach(
    IsarCollection<dynamic> col, Id id, TrackedLocation object) {
  object.localId = id;
}

extension TrackedLocationQueryWhereSort
    on QueryBuilder<TrackedLocation, TrackedLocation, QWhere> {
  QueryBuilder<TrackedLocation, TrackedLocation, QAfterWhere> anyLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension TrackedLocationQueryWhere
    on QueryBuilder<TrackedLocation, TrackedLocation, QWhereClause> {
  QueryBuilder<TrackedLocation, TrackedLocation, QAfterWhereClause>
      localIdEqualTo(Id localId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localId,
        upper: localId,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterWhereClause>
      localIdNotEqualTo(Id localId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterWhereClause>
      localIdGreaterThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localId, includeLower: include),
      );
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterWhereClause>
      localIdLessThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localId, includeUpper: include),
      );
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterWhereClause>
      localIdBetween(
    Id lowerLocalId,
    Id upperLocalId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalId,
        includeLower: includeLower,
        upper: upperLocalId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension TrackedLocationQueryFilter
    on QueryBuilder<TrackedLocation, TrackedLocation, QFilterCondition> {
  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'date',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'date',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'date',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: '',
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      dateIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'date',
        value: '',
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      localIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      localIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      localIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      localIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      locationsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'locations',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      locationsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'locations',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      locationsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'locations',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      locationsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'locations',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      locationsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'locations',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      locationsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'locations',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      statusEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
      ));
    });
  }
}

extension TrackedLocationQueryObject
    on QueryBuilder<TrackedLocation, TrackedLocation, QFilterCondition> {
  QueryBuilder<TrackedLocation, TrackedLocation, QAfterFilterCondition>
      locationsElement(FilterQuery<Location> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'locations');
    });
  }
}

extension TrackedLocationQueryLinks
    on QueryBuilder<TrackedLocation, TrackedLocation, QFilterCondition> {}

extension TrackedLocationQuerySortBy
    on QueryBuilder<TrackedLocation, TrackedLocation, QSortBy> {
  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy> sortByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy>
      sortByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy> sortByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy>
      sortByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }
}

extension TrackedLocationQuerySortThenBy
    on QueryBuilder<TrackedLocation, TrackedLocation, QSortThenBy> {
  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy> thenByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy>
      thenByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy> thenByLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.asc);
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy>
      thenByLocalIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.desc);
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy> thenByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QAfterSortBy>
      thenByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }
}

extension TrackedLocationQueryWhereDistinct
    on QueryBuilder<TrackedLocation, TrackedLocation, QDistinct> {
  QueryBuilder<TrackedLocation, TrackedLocation, QDistinct> distinctByDate(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'date', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TrackedLocation, TrackedLocation, QDistinct> distinctByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'status');
    });
  }
}

extension TrackedLocationQueryProperty
    on QueryBuilder<TrackedLocation, TrackedLocation, QQueryProperty> {
  QueryBuilder<TrackedLocation, int, QQueryOperations> localIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localId');
    });
  }

  QueryBuilder<TrackedLocation, String, QQueryOperations> dateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'date');
    });
  }

  QueryBuilder<TrackedLocation, List<Location>, QQueryOperations>
      locationsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'locations');
    });
  }

  QueryBuilder<TrackedLocation, bool, QQueryOperations> statusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'status');
    });
  }
}
